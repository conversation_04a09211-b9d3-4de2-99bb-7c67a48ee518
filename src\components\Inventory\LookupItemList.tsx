import React, {memo, forwardRef, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableWithoutFeedback,
} from 'react-native';
import LottieView from 'lottie-react-native';
import ChoieItemCard from './ChoieItemCard';
import {Inventory_Filter} from '../../server/types';

// Memoized Item component
const InventoryItem = memo(
  ({item, onPress}: {item: Inventory_Filter; onPress: () => void}) => {
    return (
      <ChoieItemCard
        Barcode={item.ItemNum}
        Price={item.Price}
        Title={item.ItemName}
        inStock={item.In_Stock}
        OnPress={onPress}
      />
    );
  },
  (prevProps, nextProps) => {
    return prevProps.item.ItemNum === nextProps.item.ItemNum;
  },
);

// Empty list component
const EmptyListComponent = memo(
  ({
    searchQuery,
    IsfromLottery = false,
    styles,
  }: {
    searchQuery: string;
    IsfromLottery: boolean;
    styles: any;
  }) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : IsfromLottery
          ? 'No Lottery Games available'
          : 'No inventory items available'}
      </Text>
    </View>
  ),
);

// Footer loading component
const ListFooter = memo(
  ({loading, styles}: {loading: boolean; styles: any}) => {
    if (!loading) {
      return null;
    }
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  },
);

interface LookupItemListProps {
  data: Inventory_Filter[];
  keyExtractor: (item: Inventory_Filter) => string;
  onEndReached: () => void;
  refreshing: boolean;
  onRefresh: () => Promise<void>;
  getItemLayout: (
    data: any,
    index: number,
  ) => {length: number; offset: number; index: number};
  searchQuery: string;
  isFromLottery?: boolean;
  loading: boolean;
  styles: any;
  contentContainerStyle?: any;
  onEndReachedThreshold?: number;
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
  windowSize?: number;
  removeClippedSubviews?: boolean;
  updateCellsBatchingPeriod?: number;
  showsVerticalScrollIndicator?: boolean;
  maintainVisibleContentPosition?: any;
  onItemPress: (item: Inventory_Filter) => void;
  onOutsidePress: () => void;
}

const LookupItemList = forwardRef<FlatList, LookupItemListProps>(
  (
    {
      data,
      keyExtractor,
      onEndReached,
      refreshing,
      onRefresh,
      getItemLayout,
      searchQuery,
      isFromLottery,
      loading,
      styles,
      contentContainerStyle,
      onEndReachedThreshold = 0.5,
      initialNumToRender = 10,
      maxToRenderPerBatch = 10,
      windowSize = 10,
      removeClippedSubviews = true,
      updateCellsBatchingPeriod = 75,
      showsVerticalScrollIndicator = false,
      maintainVisibleContentPosition,
      onItemPress,
      onOutsidePress,
    },
    ref,
  ) => {
    // Internal renderItem function
    const renderItem = useCallback(
      ({item}: {item: Inventory_Filter}) => (
        <TouchableWithoutFeedback onPress={onOutsidePress}>
          <View>
            <InventoryItem item={item} onPress={() => onItemPress(item)} />
          </View>
        </TouchableWithoutFeedback>
      ),
      [onItemPress, onOutsidePress],
    );

    return (
      <FlatList
        ref={ref}
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={contentContainerStyle}
        onEndReached={onEndReached}
        onEndReachedThreshold={onEndReachedThreshold}
        refreshing={refreshing}
        onRefresh={onRefresh}
        getItemLayout={getItemLayout}
        ListEmptyComponent={
          <EmptyListComponent
            searchQuery={searchQuery}
            IsfromLottery={isFromLottery || false}
            styles={styles}
          />
        }
        ListFooterComponent={<ListFooter loading={loading} styles={styles} />}
        initialNumToRender={initialNumToRender}
        maxToRenderPerBatch={maxToRenderPerBatch}
        windowSize={windowSize}
        removeClippedSubviews={removeClippedSubviews}
        updateCellsBatchingPeriod={updateCellsBatchingPeriod}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        maintainVisibleContentPosition={maintainVisibleContentPosition}
      />
    );
  },
);

export default LookupItemList;
export {InventoryItem, EmptyListComponent, ListFooter};
