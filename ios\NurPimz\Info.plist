<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSCameraUsageDescription</key>
	<string>Your custom message explaining why the app needs camera access</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Your custom message explaining why the app needs microphone access</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Your message here</string>
	<key>NSCameraUsageDescription</key>
	<string>Your message here</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Your message here</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>NurPimz</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>Onest-Black.ttf</string>
		<string>Onest-Bold.ttf</string>
		<string>Onest-ExtraBold.ttf</string>
		<string>Onest-ExtraLight.ttf</string>
		<string>Onest-Light.ttf</string>
		<string>Onest-Medium.ttf</string>
		<string>Onest-Regular.ttf</string>
		<string>Onest-SemiBold.ttf</string>
		<string>Onest-Thin.ttf</string>
		<string>Poppins-ExtraBold.ttf</string>
	</array>
</dict>
</plist>
