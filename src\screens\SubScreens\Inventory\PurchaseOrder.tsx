import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useCallback, useRef, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {PurchaseOrder} from '../../../server/types';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {Fonts} from '../../../styles/fonts';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {hasPermission} from '../../../utils/permissionHelper';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {usePurchaseOrders} from '../../../hooks';
import PurchaseOrderList from '../../../components/Inventory/PurchaseOrderList';
import {GetItemsWithParams} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
type CountItemRouteProp = RouteProp<any, 'PurchaseOrderItem'>;

const PurchaseOrders: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [filter, setFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);

  // Use custom hook for purchase order management
  const {
    filterPO,
    vendors,
    loading,
    refreshing,
    searchQuery,
    action,
    selectedVendor,
    isEnableFilter,
    setSearchQuery,
    setSelectedVendor,
    getPurOrder,
    getVendor,
    onRefresh,
    onSearchChange,
    onStatusFilter,
  } = usePurchaseOrders({
    poType: route?.params?.ItemData ?? 0,
  });

  const Action = [
    {label: 'Open', value: 'O'},
    {label: 'Closed', value: 'C'},
  ];

  useFocusEffect(
    useCallback(() => {
      getPurOrder();
      getVendor();
    }, [getPurOrder, getVendor]),
  );

  const handleItemPress = (item: PurchaseOrder) => {
    if (route?.params?.ItemData === 0) {
      navigation.navigate('PurchaseOrderItem', {
        ItemData: item,
        POType: route?.params?.ItemData,
      });
    } else {
      navigation.navigate('ReturnToVendor', {
        ItemData: item,
        POType: route?.params?.ItemData,
        POTYPE: route?.params?.ItemData,
      });
    }
  };

  const vendorOptions = vendors.map(vent => ({
    label: vent.Company,
    value: vent.Vendor_Number,
  }));

  const toggleLookup = useCallback(
    async (value: boolean) => {
      await GetItemsWithParams(
        (await getInventoryPort()).toString(),
        '/getpurchaseorder/:POType',
        () => {},
        () => {},
        () => {},
        {POType: route?.params?.ItemData},
      );
      setshowLookup(value);
      setSearchQuery('');

      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        onSearchChange('');
        setSearchQuery('');
        Keyboard.dismiss();
      }
    },
    [onSearchChange, route?.params?.ItemData, setSearchQuery],
  );

  const handleDoneClick = async () => {
    await GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorder/:POType',
      () => {},
      () => {},
      () => {},
      {POType: route?.params?.ItemData},
    );
    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  };

  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
      justifyContent: 'space-between',
    },
    paddingContainer: {
      paddingHorizontal: wp('2.5%'),
    },
    bottomContainer: {
      position: 'absolute',
      right: 0,
      left: 0,
      bottom: 0,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1%'),
      backgroundColor: colors.surface,
    },
    modalBackdrop: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      backgroundColor: colors.background,
      width: '93%',
      height: '70%',
      borderRadius: 15,
      paddingVertical: 10,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      color: colors.text,
    },
    closeButton: {
      paddingLeft: 10,
    },
    filterContent: {
      paddingHorizontal: wp('2.5%'),
      marginTop: 20,
    },
  });
  return (
    <View style={styles.container}>
      <View style={styles.paddingContainer}>
        <Header
          NavName={
            route?.params?.ItemData === 0
              ? 'Purchase Order'
              : route?.params?.ItemData === 1
              ? 'Return To Vendor'
              : 'Direct Purchase'
          }
        />
        {route?.params?.ItemData === 0 && (
          <AppDropDown
            label="Status"
            options={Action}
            selectedValue={action}
            onSelect={value => onStatusFilter(value)}
            isNotClear={true}
          />
        )}
        {/* <Search
          value={searchQuery}
          PlaceHolder="Search"
          onChange={onSearchChange}
          AutoFocus={true}
        /> */}

        <AppSearchWIthFilter
          OnSearch={onSearchChange}
          SearchValue={searchQuery}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={isEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={handleDoneClick}
        />

        <PurchaseOrderList
          data={filterPO}
          loading={loading}
          refreshing={refreshing}
          onRefresh={onRefresh}
          onItemPress={handleItemPress}
          height="66%"
        />
      </View>

      <View style={styles.bottomContainer}>
        {/* <AppButton
          Title={
            route.params?.ItemData === 0
              ? 'Create New Order'
              : route.params?.ItemData === 1
              ? 'Create Vendor Return'
              : 'Create Direct Purchase'
          }
          OnPress={async () => {
            const isAuthorized = await hasPermission('CFA_HH_Create_PO');

            if (!isAuthorized) {
              Alert.alert(
                'You do not have permission to create purchase orders.',
              );
              return;
            }

            navigation.navigate('PurchaseOrderCreate', {
              POType: route?.params?.ItemData,
            });
          }}
        /> */}
      </View>
      <FAB
        label={
          route.params?.ItemData === 0
            ? 'Create New Order'
            : route.params?.ItemData === 1
            ? 'Create Vendor Return'
            : 'Create Direct Purchase'
        }
        position="bottomRight"
        onPress={async () => {
          const isAuthorized = await hasPermission('CFA_HH_Create_PO');

          if (!isAuthorized) {
            Alert.alert(
              'You do not have permission to create purchase orders.',
            );
            return;
          }

          navigation.navigate('PurchaseOrderCreate', {
            POType: route?.params?.ItemData,
          });
        }}
      />

      <Modal
        animationType="fade"
        transparent={true}
        visible={filter}
        onRequestClose={() => setFilter(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Items</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setFilter(false)}>
                <AntDesign
                  name="closecircle"
                  color={colors.error} // Changed from MaterialColors.error.main
                  size={hp('3%')}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.filterContent}>
              <AppDropDown
                label="Vendors"
                options={vendorOptions}
                selectedValue={selectedVendor}
                onSelect={value => {
                  setSelectedVendor(value);
                }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PurchaseOrders;
