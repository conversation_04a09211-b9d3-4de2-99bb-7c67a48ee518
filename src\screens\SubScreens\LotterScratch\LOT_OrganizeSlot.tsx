import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  TextInput,
  Alert,
} from 'react-native';
import React, {useCallback, useRef, useState} from 'react';
import DataList from '../../../components/Inventory/AppList';
import {Department, Inventory} from '../../../server/types';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetAllItems,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertMulti,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import TicketsDetails from '../../../components/LotteryScratch/TicketsDetails';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, Primary} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AppButton from '../../../components/Inventory/AppButton';
import {applyDefaults} from '../../../Validator/Inventory/Barcode';
import {
  Activate_Book,
  Game_Details,
} from '../../../Types/Lottery/Lottery_Types';
import {
  applyDefaultsActivateBook,
  applyDefaultsGameDetails,
} from '../../../Validator/Lottery/Lottery_Validator';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import TicketsDetailsForOther from '../../../components/LotteryScratch/TicketDetailsForOther';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import Search from '../../../components/Inventory/Search';

type BarcodeScreenRouteProp = RouteProp<any, 'OrganizeSlot'>;
const LOT_OrganizeSlot: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [lottery, setLottery] = useState<Inventory[]>([]);
  const [lotteryFilter, setLotteryFilter] = useState<Inventory[]>([]);
  const [selectedLottery, setSelectedLottery] = useState<Inventory>();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [book, setBooks] = useState<Inventory>(route.params?.ItemData || []);
  const [exist, setExists] = useState<Inventory>();
  const [location, setLocation] = useState(route.params?.ItemData?.Location);
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [isConfirm, setIsConfirm] = useState<boolean>(true);
  const textInputRef = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      checkServerConnection();
    }, []),
  );

  const getInitDept = async () => {
    const data = await GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
    );
  };

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      getInitDept();
      GetDepartmentsbyTickets();
    }
  };
  const GetDepartmentsbyTickets = async () => {
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      const data = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/inventorybydepid/:Dept_ID',
        setLottery,
        {Dept_ID: lotteryDepartment},
      );

      setLotteryFilter(data);
      setSelectedDepartment(lotteryDepartment);
    } else {
      showAlert(
        'Lottery Department Not Found Would You Like to Select the Lottery Department?',
        'Lottery Not Found!',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('LotterySetup');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const renderItem = ({item}: {item: Inventory}) => (
    <TicketsDetailsForOther
      isShift={false}
      Item={item}
      // OnpressIN={() => navigation.navigate('ChangeLocation', {ItemData: item})}
      OnpressIN={() => {
        setSelectedLottery(item);
        setIsVisible(true);
        setLocation(item.Location);
      }}
      Stock={item.In_Stock}
      Cost={Number(item?.Price)}
      isLocation={true}
    />
  );

  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  const confirmChangeLocation = async () => {
    if (!currentBarcode || currentBarcode === '' || currentBarcode === null) {
      Alert.alert('Must Fill the Details');
    } else {
      const gameDetails = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getLocationExist/:Dept_ID/:Location',
        setExists,
        {Dept_ID: selectedLottery?.Dept_ID, Location: currentBarcode},
      );

      if (
        !gameDetails ||
        (Array.isArray(gameDetails) && gameDetails.length === 0)
      ) {
        const inventoryData: Partial<Inventory> = {
          ItemNum: selectedLottery?.ItemNum,
          ItemName: selectedLottery?.ItemName,
          Dept_ID: selectedLottery?.Dept_ID,
          Cost: selectedLottery?.Cost,
          Price: selectedLottery?.Price,
          Retail_Price: selectedLottery?.Retail_Price,
          In_Stock: selectedLottery?.In_Stock,
          Date_Created: selectedLottery?.Date_Created,
          Last_Sold: selectedLottery?.Last_Sold,
          Location: currentBarcode,
          Vendor_Number: selectedLottery?.Vendor_Number,
          Vendor_Part_Num: selectedLottery?.Vendor_Part_Num,
          Reorder_Level: selectedLottery?.Reorder_Level,
          Reorder_Quantity: selectedLottery?.Reorder_Quantity,
          ReOrder_Cost: selectedLottery?.ReOrder_Cost,
          Unit_Size: selectedLottery?.Unit_Size,
          Unit_Type: selectedLottery?.Unit_Type,
          FoodStampable: selectedLottery?.FoodStampable,
          Tax_1: selectedLottery?.Tax_1[0],
          Tax_2: selectedLottery?.Tax_2[0],
          Tax_3: selectedLottery?.Tax_3[0],
          Tax_4: selectedLottery?.Tax_4[0],
          Tax_5: selectedLottery?.Tax_5[0],
          Tax_6: selectedLottery?.Tax_6[0],
          Check_ID: selectedLottery?.Check_ID,
          Check_ID2: selectedLottery?.Check_ID2,
          Store_ID: selectedLottery?.Store_ID,
          ItemName_Extra: selectedLottery?.ItemName_Extra,
        };

        const applyDefault = applyDefaults(inventoryData);
        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });
        if (selectedLottery?.Location) {
          if (result) {
            const getBookDetails = await GetItemsParamsNoFilter(
              (await getLotteryPort()).toString(),
              '/GetShiftBookTickets/:ItemNum/:Location',
              setExists,
              {
                ItemNum: selectedLottery?.ItemNum,
                Location: selectedLottery?.Location,
              },
            );

            const bookData: Partial<Activate_Book> = {
              Book_No: getBookDetails[0].Book_No,
              Book_Created: getBookDetails[0].Book_Created,
              Book_Tickets: getBookDetails[0].Book_Tickets,
              CreatedBy: getBookDetails[0].CreatedBy,
              Location: currentBarcode,
              ItemNum: getBookDetails[0].ItemNum,
            };

            const applyDefault = applyDefaultsActivateBook(bookData);
            const result = await updateData<Activate_Book>({
              baseURL: (await getLotteryPort()).toString(),
              data: applyDefault,
              endpoint: '/updateactivatebooks',
            });
            if (result) {
              setIsVisible(false);
              Alert.alert('Location Updated Success!');
              setLocation(currentBarcode);
              getInitDept();
              GetDepartmentsbyTickets();
              const isShiftStarted = await AsyncStorage.getItem('ShiftStarted');
              if (isShiftStarted) {
                const getShiftID = await AsyncStorage.getItem('Shift_ID');

                const getMissingTickets = await GetItemsParamsNoFilterNoReturn(
                  (await getLotteryPort()).toString(),
                  '/GetGameDetails/:Shift_ID/:Location',
                  {
                    Shift_ID: getShiftID,
                    Location: selectedLottery?.Location,
                  },
                );

                const shiftGameData: Partial<Game_Details> = {
                  Game_ID: getMissingTickets[0]?.Game_ID,
                  Open_Book: getMissingTickets[0]?.Open_Book,
                  Close_Book: getMissingTickets[0]?.Close_Book,
                  Stock_Level_Open: getMissingTickets[0]?.Stock_Level_Open,
                  Stock_Level_Close: getMissingTickets[0]?.Stock_Level_Close,
                  Shift_Open_Serial: getMissingTickets[0]?.Shift_Open_Serial,
                  Shift_Close_Serial: getMissingTickets[0]?.Shift_Close_Serial,
                  Missing_Ticket: getMissingTickets[0]?.Missing_Ticket,
                  Location: currentBarcode,
                  Shift_ID: getMissingTickets[0]?.Shift_ID,
                  Date: getMissingTickets[0]?.Date,
                  Reset: getMissingTickets[0]?.Reset,
                };
                const applyDefault = applyDefaultsGameDetails(shiftGameData);
                const result = await updateData<Game_Details>({
                  baseURL: (await getLotteryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updatelotgamesdetails',
                });
              }
            } else {
              Alert.alert('Not Updated Book');
            }
          } else {
            Alert.alert('Not Updated Inventory');
          }
        } else {
          Alert.alert('Location Upated Succes!');
          setCurrentBarcode('');
          setIsVisible(false);
        }
      } else {
        showAlertMulti(
          'Confirm Action:',
          `Location already assigned to (${gameDetails[0]?.ItemName})`,
          'Continue anyway',
          'Cancel',
          'Swap locations',
        ).then(async result => {
          if (result === 'Yes') {
            //cancel
          } else if (result === 'No') {
            // swipe location
            const oldLocation = location;
            const inventoryData: Partial<Inventory> = {
              ItemNum: selectedLottery?.ItemNum,
              ItemName: selectedLottery?.ItemName,
              Dept_ID: selectedLottery?.Dept_ID,
              Cost: selectedLottery?.Cost,
              Price: selectedLottery?.Price,
              Retail_Price: selectedLottery?.Retail_Price,
              In_Stock: selectedLottery?.In_Stock,
              Date_Created: selectedLottery?.Date_Created,
              Last_Sold: selectedLottery?.Last_Sold,
              Location: currentBarcode,
              Vendor_Number: selectedLottery?.Vendor_Number,
              Vendor_Part_Num: selectedLottery?.Vendor_Part_Num,
              Reorder_Level: selectedLottery?.Reorder_Level,
              Reorder_Quantity: selectedLottery?.Reorder_Quantity,
              ReOrder_Cost: selectedLottery?.ReOrder_Cost,
              Unit_Size: selectedLottery?.Unit_Size,
              Unit_Type: selectedLottery?.Unit_Type,
              FoodStampable: selectedLottery?.FoodStampable,
              Tax_1: selectedLottery?.Tax_1[0],
              Tax_2: selectedLottery?.Tax_2[0],
              Tax_3: selectedLottery?.Tax_3[0],
              Tax_4: selectedLottery?.Tax_4[0],
              Tax_5: selectedLottery?.Tax_5[0],
              Tax_6: selectedLottery?.Tax_6[0],
              Check_ID: selectedLottery?.Check_ID,
              Check_ID2: selectedLottery?.Check_ID2,
              Store_ID: selectedLottery?.Store_ID,
              ItemName_Extra: selectedLottery?.ItemName_Extra,
            };

            const applyDefault = applyDefaults(inventoryData);
            const result = await updateData<Inventory>({
              baseURL: (await getInventoryPort()).toString(),
              data: applyDefault,
              endpoint: '/updatebarcode',
            });
            if (result) {
              const getBookDetails = await GetItemsParamsNoFilter(
                (await getLotteryPort()).toString(),
                '/GetShiftBookTickets/:ItemNum/:Location',
                setExists,
                {
                  ItemNum: selectedLottery?.ItemNum,
                  Location: selectedLottery?.Location,
                },
              );

              const bookData: Partial<Activate_Book> = {
                Book_No: getBookDetails[0].Book_No,
                Book_Created: getBookDetails[0].Book_Created,
                Book_Tickets: getBookDetails[0].Book_Tickets,
                CreatedBy: getBookDetails[0].CreatedBy,
                Location: currentBarcode,
                ItemNum: getBookDetails[0].ItemNum,
              };

              const applyDefault = applyDefaultsActivateBook(bookData);
              const result = await updateData<Activate_Book>({
                baseURL: (await getLotteryPort()).toString(),
                data: applyDefault,
                endpoint: '/updateactivatebooks',
              });
              if (result) {
                setIsVisible(false);
                Alert.alert('Location Updated Success!');
                setLocation(currentBarcode);
                getInitDept();
                GetDepartmentsbyTickets();

                const isShiftStarted = await AsyncStorage.getItem(
                  'ShiftStarted',
                );
                if (isShiftStarted) {
                  const getShiftID = await AsyncStorage.getItem('Shift_ID');

                  const getMissingTickets =
                    await GetItemsParamsNoFilterNoReturn(
                      (await getLotteryPort()).toString(),
                      '/GetGameDetails/:Shift_ID/:Location',
                      {
                        Shift_ID: getShiftID,
                        Location: selectedLottery?.Location,
                      },
                    );

                  const shiftGameData: Partial<Game_Details> = {
                    Game_ID: getMissingTickets[0]?.Game_ID,
                    Open_Book: getMissingTickets[0]?.Open_Book,
                    Close_Book: getMissingTickets[0]?.Close_Book,
                    Stock_Level_Open: getMissingTickets[0]?.Stock_Level_Open,
                    Stock_Level_Close: getMissingTickets[0]?.Stock_Level_Close,
                    Shift_Open_Serial: getMissingTickets[0]?.Shift_Open_Serial,
                    Shift_Close_Serial:
                      getMissingTickets[0]?.Shift_Close_Serial,
                    Missing_Ticket: getMissingTickets[0]?.Missing_Ticket,
                    Location: currentBarcode,
                    Shift_ID: getMissingTickets[0]?.Shift_ID,
                    Date: getMissingTickets[0]?.Date,
                    Reset: getMissingTickets[0]?.Reset,
                  };
                  const applyDefault = applyDefaultsGameDetails(shiftGameData);
                  const result = await updateData<Game_Details>({
                    baseURL: (await getLotteryPort()).toString(),
                    data: applyDefault,
                    endpoint: '/updatelotgamesdetails',
                  });
                }
              } else {
                //Alert.alert('Not Updated Book');
              }
            } else {
              //Alert.alert('Not Updated Inventory');
            }

            if (gameDetails) {
              const inventoryData: Partial<Inventory> = {
                ItemNum: gameDetails[0]?.ItemNum,
                ItemName: gameDetails[0]?.ItemName,
                Dept_ID: gameDetails[0]?.Dept_ID,
                Cost: gameDetails[0]?.Cost,
                Price: gameDetails[0]?.Price,
                Retail_Price: gameDetails[0]?.Retail_Price,
                In_Stock: gameDetails[0]?.In_Stock,
                Date_Created: gameDetails[0]?.Date_Created,
                Last_Sold: selectedLottery?.Last_Sold,
                Location: oldLocation,
                Vendor_Number: gameDetails[0]?.Vendor_Number,
                Vendor_Part_Num: gameDetails[0]?.Vendor_Part_Num,
                Reorder_Level: gameDetails[0]?.Reorder_Level,
                Reorder_Quantity: gameDetails[0]?.Reorder_Quantity,
                ReOrder_Cost: gameDetails[0]?.ReOrder_Cost,
                Unit_Size: gameDetails[0]?.Unit_Size,
                Unit_Type: gameDetails[0]?.Unit_Type,
                FoodStampable: gameDetails[0]?.FoodStampable,
                Tax_1: gameDetails[0]?.Tax_1[0],
                Tax_2: gameDetails[0]?.Tax_2[0],
                Tax_3: gameDetails[0]?.Tax_3[0],
                Tax_4: gameDetails[0]?.Tax_4[0],
                Tax_5: gameDetails[0]?.Tax_5[0],
                Tax_6: gameDetails[0]?.Tax_6[0],
                Check_ID: gameDetails[0]?.Check_ID,
                Check_ID2: gameDetails[0]?.Check_ID2,
                Store_ID: gameDetails[0]?.Store_ID,
                ItemName_Extra: gameDetails[0]?.ItemName_Extra,
              };

              const applyDefault = applyDefaults(inventoryData);
              const result = await updateData<Inventory>({
                baseURL: (await getInventoryPort()).toString(),
                data: applyDefault,
                endpoint: '/updatebarcode',
              });
              if (result) {
                const getBookDetails = await GetItemsParamsNoFilter(
                  (await getLotteryPort()).toString(),
                  '/GetShiftBookTickets/:ItemNum/:Location',
                  setExists,
                  {
                    ItemNum: gameDetails[0]?.ItemNum,
                    Location: gameDetails[0]?.Location,
                  },
                );

                const bookData: Partial<Activate_Book> = {
                  Book_No: getBookDetails[0].Book_No,
                  Book_Created: getBookDetails[0].Book_Created,
                  Book_Tickets: getBookDetails[0].Book_Tickets,
                  CreatedBy: getBookDetails[0].CreatedBy,
                  Location: oldLocation,
                  ItemNum: getBookDetails[0].ItemNum,
                };

                const applyDefault = applyDefaultsActivateBook(bookData);
                const result = await updateData<Activate_Book>({
                  baseURL: (await getLotteryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updateactivatebooks',
                });
                if (result) {
                  setLocation(currentBarcode);
                  getInitDept();
                  GetDepartmentsbyTickets();
                } else {
                  //Alert.alert('Not Updated Book');
                }
              } else {
                //Alert.alert('Not Updated Inventory');
              }
            }
          } else {
            //Continue anyway
            const inventoryData: Partial<Inventory> = {
              ItemNum: selectedLottery?.ItemNum,
              ItemName: selectedLottery?.ItemName,
              Dept_ID: selectedLottery?.Dept_ID,
              Cost: selectedLottery?.Cost,
              Price: selectedLottery?.Price,
              Retail_Price: selectedLottery?.Retail_Price,
              In_Stock: selectedLottery?.In_Stock,
              Date_Created: selectedLottery?.Date_Created,
              Last_Sold: selectedLottery?.Last_Sold,
              Location: currentBarcode,
              Vendor_Number: selectedLottery?.Vendor_Number,
              Vendor_Part_Num: selectedLottery?.Vendor_Part_Num,
              Reorder_Level: selectedLottery?.Reorder_Level,
              Reorder_Quantity: selectedLottery?.Reorder_Quantity,
              ReOrder_Cost: selectedLottery?.ReOrder_Cost,
              Unit_Size: selectedLottery?.Unit_Size,
              Unit_Type: selectedLottery?.Unit_Type,
              FoodStampable: selectedLottery?.FoodStampable,
              Tax_1: selectedLottery?.Tax_1[0],
              Tax_2: selectedLottery?.Tax_2[0],
              Tax_3: selectedLottery?.Tax_3[0],
              Tax_4: selectedLottery?.Tax_4[0],
              Tax_5: selectedLottery?.Tax_5[0],
              Tax_6: selectedLottery?.Tax_6[0],
              Check_ID: selectedLottery?.Check_ID,
              Check_ID2: selectedLottery?.Check_ID2,
              Store_ID: selectedLottery?.Store_ID,
              ItemName_Extra: selectedLottery?.ItemName_Extra,
            };

            const applyDefault = applyDefaults(inventoryData);
            const result = await updateData<Inventory>({
              baseURL: (await getInventoryPort()).toString(),
              data: applyDefault,
              endpoint: '/updatebarcode',
            });
            if (result) {
              const getBookDetails = await GetItemsParamsNoFilter(
                (await getLotteryPort()).toString(),
                '/GetShiftBookTickets/:ItemNum/:Location',
                setExists,
                {
                  ItemNum: selectedLottery?.ItemNum,
                  Location: selectedLottery?.Location,
                },
              );

              const bookData: Partial<Activate_Book> = {
                Book_No: getBookDetails[0].Book_No,
                Book_Created: getBookDetails[0].Book_Created,
                Book_Tickets: getBookDetails[0].Book_Tickets,
                CreatedBy: getBookDetails[0].CreatedBy,
                Location: currentBarcode,
                ItemNum: getBookDetails[0].ItemNum,
              };

              const applyDefault = applyDefaultsActivateBook(bookData);
              const result = await updateData<Activate_Book>({
                baseURL: (await getLotteryPort()).toString(),
                data: applyDefault,
                endpoint: '/updateactivatebooks',
              });
              if (result) {
                setIsVisible(false);
                Alert.alert('Location Updated Success!');
                setLocation(currentBarcode);
                getInitDept();
                GetDepartmentsbyTickets();
                const isShiftStarted = await AsyncStorage.getItem(
                  'ShiftStarted',
                );
                if (isShiftStarted) {
                  const getShiftID = await AsyncStorage.getItem('Shift_ID');

                  const getMissingTickets =
                    await GetItemsParamsNoFilterNoReturn(
                      (await getLotteryPort()).toString(),
                      '/GetGameDetails/:Shift_ID/:Location',
                      {
                        Shift_ID: getShiftID,
                        Location: selectedLottery?.Location,
                      },
                    );

                  const shiftGameData: Partial<Game_Details> = {
                    Game_ID: getMissingTickets[0]?.Game_ID,
                    Open_Book: getMissingTickets[0]?.Open_Book,
                    Close_Book: getMissingTickets[0]?.Close_Book,
                    Stock_Level_Open: getMissingTickets[0]?.Stock_Level_Open,
                    Stock_Level_Close: getMissingTickets[0]?.Stock_Level_Close,
                    Shift_Open_Serial: getMissingTickets[0]?.Shift_Open_Serial,
                    Shift_Close_Serial:
                      getMissingTickets[0]?.Shift_Close_Serial,
                    Missing_Ticket: getMissingTickets[0]?.Missing_Ticket,
                    Location: currentBarcode,
                    Shift_ID: getMissingTickets[0]?.Shift_ID,
                    Date: getMissingTickets[0]?.Date,
                    Reset: getMissingTickets[0]?.Reset,
                  };
                  const applyDefault = applyDefaultsGameDetails(shiftGameData);
                  const result = await updateData<Game_Details>({
                    baseURL: (await getLotteryPort()).toString(),
                    data: applyDefault,
                    endpoint: '/updatelotgamesdetails',
                  });
                }
              } else {
                //Alert.alert('Not Updated Book');
              }
            } else {
              //Alert.alert('Not Updated Inventory');
            }

            if (gameDetails) {
              const inventoryData: Partial<Inventory> = {
                ItemNum: gameDetails[0]?.ItemNum,
                ItemName: gameDetails[0]?.ItemName,
                Dept_ID: gameDetails[0]?.Dept_ID,
                Cost: gameDetails[0]?.Cost,
                Price: gameDetails[0]?.Price,
                Retail_Price: gameDetails[0]?.Retail_Price,
                In_Stock: gameDetails[0]?.In_Stock,
                Date_Created: gameDetails[0]?.Date_Created,
                Last_Sold: selectedLottery?.Last_Sold,
                Location: 'NOTASSIGNED',
                Vendor_Number: gameDetails[0]?.Vendor_Number,
                Vendor_Part_Num: gameDetails[0]?.Vendor_Part_Num,
                Reorder_Level: gameDetails[0]?.Reorder_Level,
                Reorder_Quantity: gameDetails[0]?.Reorder_Quantity,
                ReOrder_Cost: gameDetails[0]?.ReOrder_Cost,
                Unit_Size: gameDetails[0]?.Unit_Size,
                Unit_Type: gameDetails[0]?.Unit_Type,
                FoodStampable: gameDetails[0]?.FoodStampable,
                Tax_1: gameDetails[0]?.Tax_1[0],
                Tax_2: gameDetails[0]?.Tax_2[0],
                Tax_3: gameDetails[0]?.Tax_3[0],
                Tax_4: gameDetails[0]?.Tax_4[0],
                Tax_5: gameDetails[0]?.Tax_5[0],
                Tax_6: gameDetails[0]?.Tax_6[0],
                Check_ID: gameDetails[0]?.Check_ID,
                Check_ID2: gameDetails[0]?.Check_ID2,
                Store_ID: gameDetails[0]?.Store_ID,
                ItemName_Extra: gameDetails[0]?.ItemName_Extra,
              };

              const applyDefault = applyDefaults(inventoryData);
              const result = await updateData<Inventory>({
                baseURL: (await getInventoryPort()).toString(),
                data: applyDefault,
                endpoint: '/updatebarcode',
              });
              if (result) {
                const getBookDetails = await GetItemsParamsNoFilter(
                  (await getLotteryPort()).toString(),
                  '/GetShiftBookTickets/:ItemNum/:Location',
                  setExists,
                  {
                    ItemNum: gameDetails[0]?.ItemNum,
                    Location: gameDetails[0]?.Location,
                  },
                );

                const bookData: Partial<Activate_Book> = {
                  Book_No: getBookDetails[0].Book_No,
                  Book_Created: getBookDetails[0].Book_Created,
                  Book_Tickets: getBookDetails[0].Book_Tickets,
                  CreatedBy: getBookDetails[0].CreatedBy,
                  Location: 'NOTASSIGNED',
                  ItemNum: getBookDetails[0].ItemNum,
                };

                const applyDefault = applyDefaultsActivateBook(bookData);
                const result = await updateData<Activate_Book>({
                  baseURL: (await getLotteryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updateactivatebooks',
                });
                if (result) {
                  setLocation(currentBarcode);
                  getInitDept();
                  GetDepartmentsbyTickets();
                } else {
                  //Alert.alert('Not Updated Book');
                }
              } else {
                //Alert.alert('Not Updated Inventory');
              }
            }
          }
        });
      }
      GetDepartmentsbyTickets();
    }
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      lottery,
      ['ItemNum', 'ItemName'],
      setLotteryFilter,
      setLoading,
    );
  };
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      justifyContent: 'space-between',
      flex: 1,
    },
    modalContainer: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    innerContainer: {
      paddingHorizontal: wp('2.5%'),
    },
  });

  return (
    <View
      style={{
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: wp('2.5%'),
        backgroundColor: colors.background,
      }}>
      <Header NavName={'Change Location'} />

      <Search
        textInputRef={textInputRef}
        PlaceHolder="Search..."
        Value={searchQuery}
        onChange={onSearchChange}
        keyboardON={true}
      />
      {false && (
        <AppDropDown
          label="Department"
          options={departmentOptions}
          selectedValue={selectedDepartment}
          onSelect={value => GetDepartmentsbyTickets(value)}
        />
      )}
      <View style={{height: '100%', width: '100%'}}>
        <View>
          <Text
            style={{
              textAlign: 'center',
              fontSize: 14,
              fontFamily: Fonts.OnestBold,
              color: colors.primary,
              margin: 15,
            }}>{`(${lottery.length}) Games Found`}</Text>
          <DataList
            data={lotteryFilter}
            renderItem={renderItem}
            loading={loading}
            Hight="75%"
            IsCreate={true}
            IsCreateMessage="Lottery Games Not Found Would You Like to Create New Game?"
            IsCreateBtnLabel="Add New Game"
            onIsCreate={async () => {
              const lotteryDepartment = await AsyncStorage.getItem(
                'LOTTERY_DEP_ID',
              );

              if (lotteryDepartment) {
                navigation.navigate('Lookup', {
                  isFromLottery: true,
                  LOTTERYDEPT: lotteryDepartment,
                });
              }
            }}
          />
        </View>
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={isVisible}
        onRequestClose={() => setIsVisible(false)}>
        <View style={styles.modalContainer}>
          <View
            style={{
              backgroundColor: colors.card,
              width: '93%',
              height: '70%',
              borderRadius: 15,
              paddingVertical: 10,
              paddingHorizontal: wp('2.5%'),
              shadowColor: colors.shadow,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: isDark ? 0.5 : 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: wp('2.5%'),
                paddingVertical: hp('1.5%'),
              }}>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('2.5%'),
                  color: colors.text,
                }}>
                Change Location
              </Text>

              <TouchableOpacity
                style={{paddingLeft: 10}}
                onPress={() => {
                  setCurrentBarcode('');
                  setIsVisible(false);
                  getInitDept();
                  GetDepartmentsbyTickets();
                }}>
                <AntDesign
                  name="closecircle"
                  color={colors.error}
                  size={hp('4%')}
                />
              </TouchableOpacity>
            </View>

            <View style={{paddingHorizontal: wp('2.5%')}}>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.9%'),
                  color: colors.text,
                }}>
                Game Name: {selectedLottery?.ItemName}
              </Text>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.9%'),
                  color: colors.text,
                }}>
                Current Location: {location}
              </Text>
            </View>
            <View
              style={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                left: 0,
                paddingHorizontal: wp('2.5%'),
                paddingBottom: 10,
              }}>
              <AppTextInput
                PlaceHolder="Enter Location"
                Title="Change Location"
                Value={currentBarcode}
                onChangeText={text => setCurrentBarcode(text)}
                Editable={isConfirm}
              />
              <AppButton
                Title={'Confirm'}
                OnPress={() => confirmChangeLocation()}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default LOT_OrganizeSlot;
