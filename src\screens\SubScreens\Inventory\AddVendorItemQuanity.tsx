import {View, Text, StyleSheet, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {RouteProp} from '@react-navigation/native';
import Header from '../../../components/Inventory/Header';
import {
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
  Reason_Codes,
  UpdatePurchaseOrder,
  VendorItem,
} from '../../../server/types';
import {Formik} from 'formik';
import * as Yup from 'yup';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  createData,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppButton from '../../../components/Inventory/AppButton';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsPurchaseOrderItem,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type CountItemRouteProp = RouteProp<any, 'AddVendorItemQuanity'>;

const validationSchema = Yup.object().shape({
  quanity: Yup.number().required('Vendor is required'),
  reason: Yup.string().required('reason is required'),
});

const AddVendorItemQuanity: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [vendorItems, setVendorItems] = useState<VendorItem>(
    route.params?.ItemData,
  );
  const [reasonCodes, setReasonCodes] = useState<Reason_Codes[]>([]);
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.Main);
  const [success, setSuccess] = useState<boolean>(false);
  const [inventory, setInventory] = useState<Inventory[]>([]);

  // Keeping all existing function logic unchanged
  useEffect(() => {
    getReasonCodes();
    getInvetoryDetails();
  }, []);

  const getReasonCodes = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/reasoncodes/:Reason_Type',
      setReasonCodes,
      {Reason_Type: 5},
      false,
    );
  };

  const getInvetoryDetails = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      setInventory,
      {ItemNum: vendorItems.ItemNum},
      false,
    );
  };

  const initialValues = {
    quanity: '',
    reason: '',
  };

  const reasonOptions = reasonCodes.map(dept => ({
    label: dept.Reason_Code,
    value: dept.Reason_Code,
  }));

  const AddPurchaseOrderItems = async (Values: any) => {
    if (success) {
      Alert.alert('Item Already Added!');
    } else {
      if (Number(Values.quanity) > 0) {
        if (Number(Values.quanity) > inventory[0].In_Stock) {
          Alert.alert('Entered Value More Than In stock');
        } else {
          AddItems(Values);
        }
      } else {
        Alert.alert('Please Enter Correct Value!');
      }
    }
  };

  const AddItems = async (Values: any) => {
    const storeId = await AsyncStorage.getItem('STOREID');

    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: vendorItems.ItemNum},
    );

    try {
      const OrderedDec = '-' + Number(Values.quanity);
      const poItmes: Partial<PurchaseOrderItems> = {
        PO_Number: mainPO.PO_Number,
        ItemNum: vendorItems.ItemNum,
        Quan_Ordered: Number(OrderedDec),
        CostPer: vendorItems.CostPer
          ? vendorItems.CostPer
          : getBarcode[0]?.Cost,
        Vendor_Part_Number: vendorItems.Vendor_Part_Num || '',
        CasePack: null,
        Store_ID: storeId === null ? '1001' : storeId,
        destStore_ID: storeId === null ? '1001' : storeId,
        NumberPerCase: 0,
        Reason: Values.reason,
      };
      const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);
      //setSuccess(true);

      const createResult = await createData<PurchaseOrderItems>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createpodetails',
      });

      if (createResult) {
        let TotalCost = 0;
        let ExpectedRecive = 0;
        if (mainPO.Total_Cost.toString()?.includes('-')) {
          const positiveTotal = Math.abs(mainPO.Total_Cost);
          const calTotals =
            Number(Values.quanity) *
            Number(
              vendorItems.CostPer ? vendorItems.CostPer : getBarcode[0]?.Cost,
            );
          const TotalCostCal = Number(positiveTotal) + Number(calTotals);
          const positiveExpected = Math.abs(mainPO.ExpectedAmountToReceive);
          const ExpectedReciveCal =
            Number(positiveExpected) + Number(Values.quanity);

          const TotalCostIN = '-' + TotalCostCal;
          const TotalExpectIN = '-' + ExpectedReciveCal;
          TotalCost = Number(TotalCostIN);
          ExpectedRecive = Number(TotalExpectIN);
        } else {
          const calTotals =
            Number(Values.quanity) *
            Number(
              vendorItems.CostPer ? vendorItems.CostPer : getBarcode[0]?.Cost,
            );
          const TotalCostCal = Number(mainPO.Total_Cost) + Number(calTotals);
          const ExpectedReciveCal =
            Number(mainPO.ExpectedAmountToReceive) + Number(Values.quanity);

          const TotalCostIN = '-' + TotalCostCal;
          const TotalExpectIN = '-' + ExpectedReciveCal;
          TotalCost = Number(TotalCostIN);
          ExpectedRecive = Number(TotalExpectIN);
        }

        updatePO(TotalCost, ExpectedRecive);
        //AdjustInventory(Values);

        setSuccess(true);
      } else {
        setSuccess(false);
        console.log('Error');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: TotalCost,
      ExpectedAmountToReceive: Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: mainPO.Status,
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: mainPO.POType,
      Dirty: mainPO.Dirty,
      Print_Notes_On_PO: mainPO.Print_Notes_On_PO,
      ShipTo_1: mainPO.ShipTo_1,
      ShipTo_2: mainPO.ShipTo_2,
      ShipTo_4: mainPO.ShipTo_4,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
        true,
      );
      getInvetoryDetails();
      navigation.navigate('ReturnToVendor');
    } else {
      console.log('Error!!');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      width: '100%',
      height: '100%',
      paddingHorizontal: wp('2.5%'),
      backgroundColor: colors.background,
    },
    itemCard: {
      backgroundColor: colors.card,
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('2%'),
      borderRadius: 12,
      marginTop: hp('1.5%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    itemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
    },
    stockRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    stockText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    priceText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
    },
    vendorText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    formContainer: {
      paddingVertical: hp('3.5%'),
    },
    formField: {
      // marginBottom: hp('2%'),
    },
    buttonContainer: {
      marginTop: hp('2%'),
    },
  });
  return (
    <View style={styles.container}>
      <Header NavName="Add Vendor Return Items" />

      <View style={styles.itemCard}>
        <Text style={styles.itemName}>{vendorItems.ItemName}</Text>
        <View style={styles.stockRow}>
          <Text style={styles.stockText}>
            In Stock: {inventory[0]?.In_Stock}
          </Text>
          <Text style={styles.priceText}>${vendorItems.CostPer}</Text>
        </View>
        <Text style={styles.vendorText}>
          Vendor Number: {vendorItems.Vendor_Number}
        </Text>
      </View>

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={values => {
          AddPurchaseOrderItems(values);
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View style={styles.formContainer}>
            <View style={styles.formField}>
              <AppTextInput
                PlaceHolder="Enter Quantity"
                Title="Quantity"
                Value={values.quanity}
                onChangeText={handleChange('quanity')}
                onBlur={handleBlur('quanity')}
                error={errors.quanity}
                touched={touched.quanity}
                isNumeric={true}
              />
            </View>

            <View style={styles.formField}>
              <AppDropDown
                label="Select Return Reason"
                options={reasonOptions}
                selectedValue={values.reason}
                onSelect={value => setFieldValue('reason', value)}
                error={errors.reason}
                touched={touched.reason}
                isAdd={true}
                onCreate={() => navigation.navigate('ReasonCode')}
                isBackRoudColor={true}
              />
            </View>

            <View style={styles.buttonContainer}>
              <AppButton Title="Save & Close" OnPress={handleSubmit} />
            </View>
          </View>
        )}
      </Formik>
    </View>
  );
};

export default AddVendorItemQuanity;
