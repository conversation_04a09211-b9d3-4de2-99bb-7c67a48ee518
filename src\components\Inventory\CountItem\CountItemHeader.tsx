import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Fonts, FontSizes } from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { useThemeColors } from '../../../Theme/useThemeColors';

interface CountItemHeaderProps {
  itemCount: number;
}

const CountItemHeader: React.FC<CountItemHeaderProps> = ({ itemCount }) => {
  const colors = useThemeColors();

  const styles = StyleSheet.create({
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('4%'),
      marginBottom: 5,
    },
    headerTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
    },
    headerTitle: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    itemCount: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
  });

  return (
    <View style={styles.headerRow}>
      <View style={styles.headerTitleContainer}>
        <Text style={styles.headerTitle}>Available Items</Text>
        <Text style={styles.itemCount}>{` (${itemCount})`}</Text>
      </View>
    </View>
  );
};

export default memo(CountItemHeader);
