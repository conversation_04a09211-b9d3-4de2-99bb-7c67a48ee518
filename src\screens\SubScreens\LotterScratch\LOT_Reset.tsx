import {View, Text, TouchableOpacity} from 'react-native';
import React, {useCallback, useState} from 'react';
import DataList from '../../../components/Inventory/AppList';
import {Department, Inventory} from '../../../server/types';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetAllItems,
  GetItemsParamsNoFilter,
  showAlert,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import TicketsDetails from '../../../components/LotteryScratch/TicketsDetails';
import {Backround, Primary} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AsyncStorage from '@react-native-async-storage/async-storage';

type BarcodeScreenRouteProp = RouteProp<any, 'ResetShift'>;
const LOT_Reset: React.FC<{route: BarcodeScreenRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [lottery, setLottery] = useState<Inventory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      getInitDept();
      GetDepartmentsbyTickets();
    }, []),
  );
  const getInitDept = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
    );
  };

  const GetDepartmentsbyTickets = async () => {
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/inventorybydepid/:Dept_ID',
        setLottery,
        {Dept_ID: lotteryDepartment},
      );

      setSelectedDepartment(lotteryDepartment);
    } else {
      showAlert(
        'Lottery Department Not Found Would You Like to Select the Lottery Department?',
        'Lottery Not Found!',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('LotterySetup');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const renderItem = ({item}: {item: Inventory}) => (
    <TicketsDetails
      isShift={false}
      Item={item}
      OnpressIN={() => navigation.navigate('ResetShiftEach', {ItemData: item})}
      Stock={item.In_Stock}
      Cost={Number(item?.Price)}
    />
  );

  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  return (
    <View
      style={{
        backgroundColor: Backround,
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName={'Reset Shift'} />

      {false && (
        <AppDropDown
          label="Department"
          options={departmentOptions}
          selectedValue={selectedDepartment}
          onSelect={value => GetDepartmentsbyTickets(value)}
        />
      )}

      <View>
        <View>
          <Text
            style={{
              textAlign: 'center',
              fontSize: 14,
              fontFamily: Fonts.OnestBold,
              color: Primary,
              margin: 15,
            }}>{`(${lottery.length}) Games Found`}</Text>
          <DataList
            data={lottery}
            renderItem={renderItem}
            loading={loading}
            Hight="75%"
          />
        </View>
      </View>
    </View>
  );
};

export default LOT_Reset;
