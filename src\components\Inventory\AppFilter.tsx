import {View, Text, Modal, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AppDropDown from './AppDropDown';
import {BrandAdd, Department, SubCategoryAdd, Vendor} from '../../server/types';
import {GetAllItems} from '../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../server/InstanceTypes';
import {useFocusEffect} from '@react-navigation/native';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface Props {
  Department: (value: string) => void;
  Vedor: (value: string) => void;
  Brand: (value: string) => void;
  Category: (value: string) => void;
  EnableFilter: (value: boolean) => void;
  setIsVisble: (value: boolean) => void;
  selectedDepartment: string;
  selectedVendor: string;
  selectedBrand: string;
  selectedSubCategory: string;
  isEnableFilter: boolean;
  isVisible: boolean;
}

const AppFilter = ({
  Department,
  Vedor,
  Brand,
  Category,
  EnableFilter,
  selectedDepartment,
  selectedVendor,
  selectedBrand,
  selectedSubCategory,
  isEnableFilter,
  isVisible,
  setIsVisble,
}: Props) => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [getBrands, setGetBrands] = useState<BrandAdd[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategoryAdd[]>(
    [],
  );
  const [vendors, setVendors] = useState<Vendor[]>([]);

  useFocusEffect(
    useCallback(() => {
      getInitDept();
      getVendor();
      setIsVisble(false);
      getSubCatogoryOrBrand();
    }, []),
  );

  const getSubCatogoryOrBrand = async () => {
    GetAllItems<BrandAdd[]>(
      (await getLotteryPort()).toString(),
      '/GetAllBrands',
      setGetBrands,
      setLoading,
    );

    console.log('AppFilter');

    GetAllItems<SubCategoryAdd[]>(
      (await getLotteryPort()).toString(),
      '/GetAllSubCategories',
      setGetSubCategories,
      setLoading,
    );
  };

  const getInitDept = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
    );
  };

  const getVendor = async () => {
    GetAllItems<Vendor[]>(
      (await getInventoryPort()).toString(),
      '/GetVendors',
      setVendors,
      setLoading,
    );
  };

  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  const vendorOptions = vendors.map(vent => ({
    label: vent.Company,
    value: vent.Vendor_Number,
  }));

  const brandOptions = getBrands.map(vent => ({
    label: vent.Brand,
    value: vent.Brand,
  }));

  const subCategoryOptions = getSubCategories.map(vent => ({
    label: vent.SubCategory,
    value: vent.SubCategory,
  }));

  const handleClearFilters = () => {
    Department('');
    Vedor('');
    Brand('');
    Category('');
    EnableFilter(false);
    setIsVisble(false);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      justifyContent: 'space-between',
      flex: 1,
    },
    modalContent: {
      backgroundColor: colors.surface,
      width: '85%',
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 8,
      elevation: 5,
    },
    modalContainer: {
      backgroundColor: 'rgba(0,0,0,0.6)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.surface,
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    closeButton: {
      padding: 8,
      borderRadius: 20,
    },
    filtersContainer: {
      paddingHorizontal: wp('5%'),
      paddingTop: hp('2%'),
      paddingBottom: hp('1%'),
    },
    clearFilterButton: {
      marginHorizontal: wp('5%'),
      marginBottom: hp('2%'),
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.5%'),
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      backgroundColor: colors.primary + '20',
      borderRadius: 8,
    },
    clearFilterText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.primary,
      marginLeft: 8,
    },
  });

  return (
    <Modal
      animationType="fade"
      transparent
      visible={isVisible}
      statusBarTranslucent
      onRequestClose={() => setIsVisble(false)}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Items</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setIsVisble(false)}>
              <AntDesign name="closecircle" color={colors.error} size={20} />
            </TouchableOpacity>
          </View>

          <View style={styles.filtersContainer}>
            <AppDropDown
              label="Department"
              options={departmentOptions}
              selectedValue={selectedDepartment}
              FixHeight="100%"
              onSelect={value => {
                EnableFilter(true);
                Department(value);
              }}
            />

            <AppDropDown
              label="Vendors"
              options={vendorOptions}
              selectedValue={selectedVendor}
              FixHeight="100%"
              onSelect={value => {
                EnableFilter(true);
                Vedor(value);
              }}
            />

            <AppDropDown
              label="Brand"
              options={brandOptions}
              selectedValue={selectedBrand}
              FixHeight="100%"
              onSelect={value => {
                EnableFilter(true);
                Brand(value);
              }}
            />

            <AppDropDown
              label="SubCategory"
              options={subCategoryOptions}
              selectedValue={selectedSubCategory}
              FixHeight="100%"
              onSelect={value => {
                EnableFilter(true);
                Category(value);
              }}
            />
          </View>

          {isEnableFilter && (
            <TouchableOpacity
              style={styles.clearFilterButton}
              onPress={handleClearFilters}>
              <MaterialIcons
                name="filter-list-off"
                size={18}
                color={colors.primary}
              />
              <Text style={styles.clearFilterText}>Clear All Filters</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default AppFilter;
