﻿import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  StatusBar,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useFocusEffect} from '@react-navigation/native';
import DateTimePicker from '@react-native-community/datetimepicker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import Header from '../../../components/Inventory/Header';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import {Fonts} from '../../../styles/fonts';
import AppButton from '../../../components/Inventory/AppButton';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import AppLoader from '../../../components/Inventory/AppLoader';
import {
  WorkingDay,
  NotificationSettings as NotificationSettingsType,
  getNotificationSettings,
  saveNotificationSettings,
  hasExistingData,
  getOrgId,
} from '../../../utils/NotificationHelper';

const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
];

const formatTime = (date: Date) => {
  return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
};

const WorkingDayCard: React.FC<{
  day: WorkingDay;
  index: number;
  colors: any;
  onToggle: (index: number) => void;
  onTimeChange: (index: number, type: 'open' | 'close') => void;
}> = ({day, index, colors, onToggle, onTimeChange}) => {
  return (
    <View style={[styles.dayCard, {backgroundColor: colors.surface}]}>
      <View style={styles.dayHeader}>
        <Text style={[styles.dayName, {color: colors.text}]}>{day.day}</Text>
        <CustomCheckbox
          isChecked={day.enabled}
          onChange={() => onToggle(index)}
        />
      </View>

      {day.enabled && (
        <View style={styles.timeContainer}>
          <TouchableOpacity
            style={[styles.timeButton, {borderColor: colors.border}]}
            onPress={() => onTimeChange(index, 'open')}>
            <MaterialCommunityIcons
              name="clock-start"
              size={hp('2%')}
              color={colors.primary}
            />
            <Text style={[styles.timeText, {color: colors.text}]}>
              Open: {formatTime(day.openTime)}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.timeButton, {borderColor: colors.border}]}
            onPress={() => onTimeChange(index, 'close')}>
            <MaterialCommunityIcons
              name="clock-end"
              size={hp('2%')}
              color={colors.primary}
            />
            <Text style={[styles.timeText, {color: colors.text}]}>
              Close: {formatTime(day.closeTime)}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const NotificationSettings: React.FC<{navigation: any}> = () => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const [settings, setSettings] = useState<NotificationSettingsType>({
    workingDays: DAYS_OF_WEEK.map(day => ({
      day,
      enabled: true,
      openTime: new Date(2023, 0, 1, 9, 0), // 9:00 AM
      closeTime: new Date(2023, 0, 1, 18, 0), // 6:00 PM
    })),
    reportSendTime: new Date(2023, 0, 1, 17, 0), // 5:00 PM
  });

  const [showTimePicker, setShowTimePicker] = useState<{
    visible: boolean;
    type: 'open' | 'close' | 'report';
    dayIndex?: number;
  }>({visible: false, type: 'open'});

  const [loading, setLoading] = useState(false);
  const [fetchingData, setFetchingData] = useState(false);
  const [hasData, setHasData] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute to refresh status
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const getCurrentStatus = () => {
    const now = currentTime;
    const currentDay = now.toLocaleDateString('en-US', {weekday: 'long'});
    const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();

    console.log('Current day:', currentDay);
    console.log('Current time minutes:', currentTimeMinutes);
    console.log('Settings workingDays:', settings.workingDays);

    const todaySettings = settings.workingDays.find(
      day => day.day === currentDay,
    );

    console.log('Today settings:', todaySettings);

    if (!todaySettings || !todaySettings.enabled) {
      return {isOpen: false, status: 'Closed - Not a working day'};
    }

    const openTime =
      todaySettings.openTime.getHours() * 60 +
      todaySettings.openTime.getMinutes();
    const closeTime =
      todaySettings.closeTime.getHours() * 60 +
      todaySettings.closeTime.getMinutes();

    console.log('Open time minutes:', openTime);
    console.log('Close time minutes:', closeTime);

    if (currentTimeMinutes >= openTime && currentTimeMinutes <= closeTime) {
      return {isOpen: true, status: 'Open'};
    } else if (currentTimeMinutes < openTime) {
      return {
        isOpen: false,
        status: `Closed - Opens at ${formatTime(todaySettings.openTime)}`,
      };
    } else {
      return {isOpen: false, status: 'Closed for today'};
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadSettings();
    }, []),
  );

  const loadSettings = async () => {
    try {
      setFetchingData(true);
      const savedSettings = await getNotificationSettings();
      if (savedSettings) {
        setSettings(savedSettings);
      }

      // Check if data exists on server
      const orgId = await getOrgId();
      if (orgId) {
        const dataExists = await hasExistingData(orgId);
        setHasData(!!dataExists);
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setFetchingData(false);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      const success = await saveNotificationSettings(settings);
      Alert.alert(
        success ? 'Success' : 'Error',
        success ? 'Settings saved!' : 'Failed to save settings',
      );

      // Update hasData state after successful save
      if (success) {
        const orgId = await getOrgId();
        if (orgId) {
          const dataExists = await hasExistingData(orgId);
          setHasData(!!dataExists);
        }
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const toggleWorkingDay = (dayIndex: number) => {
    setSettings(prev => ({
      ...prev,
      workingDays: prev.workingDays.map((day, index) =>
        index === dayIndex ? {...day, enabled: !day.enabled} : day,
      ),
    }));
  };

  const handleTimeChange = (dayIndex: number, type: 'open' | 'close') => {
    setShowTimePicker({visible: true, type, dayIndex});
  };

  const updateTime = (
    dayIndex: number | undefined,
    type: 'open' | 'close' | 'report',
    time: Date,
  ) => {
    if (type === 'report') {
      setSettings(prev => ({...prev, reportSendTime: time}));
    } else if (dayIndex !== undefined) {
      setSettings(prev => ({
        ...prev,
        workingDays: prev.workingDays.map((day, index) =>
          index === dayIndex
            ? {...day, [type === 'open' ? 'openTime' : 'closeTime']: time}
            : day,
        ),
      }));
    }
  };

  const isReportTimeValid = () => {
    const enabledDays = settings.workingDays.filter(day => day.enabled);

    // Allow saving when no days are enabled (empty settings)
    if (enabledDays.length === 0) {
      return true;
    }

    return enabledDays.some(day => {
      const reportMinutes =
        settings.reportSendTime.getHours() * 60 +
        settings.reportSendTime.getMinutes();
      const openMinutes =
        day.openTime.getHours() * 60 + day.openTime.getMinutes();
      const closeMinutes =
        day.closeTime.getHours() * 60 + day.closeTime.getMinutes();
      return reportMinutes >= openMinutes && reportMinutes <= closeMinutes;
    });
  };

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      <Header NavName="Notification Settings" />

      {/* Online Status Indicator */}
      <View style={[styles.statusContainer, {backgroundColor: colors.surface}]}>
        <View style={styles.statusContent}>
          <View style={styles.statusIconContainer}>
            <MaterialCommunityIcons
              name="circle"
              size={hp('1.5%')}
              color={getCurrentStatus().isOpen ? '#4CAF50' : '#F44336'}
            />
            <MaterialCommunityIcons
              name={getCurrentStatus().isOpen ? 'store' : 'store-off'}
              size={hp('2.5%')}
              color={colors.primary}
              style={styles.statusIcon}
            />
          </View>
          <View style={styles.statusTextContainer}>
            <Text style={[styles.statusTitle, {color: colors.text}]}>
              Current Status
            </Text>
            <Text
              style={[
                styles.statusText,
                {
                  color: getCurrentStatus().isOpen
                    ? colors.success || '#4CAF50'
                    : colors.error || '#F44336',
                },
              ]}>
              {getCurrentStatus().status}
            </Text>
          </View>
        </View>
      </View>

      {fetchingData ? (
        <View style={styles.loadingContainer}>
          <AppLoader modalVisible={true} />
          <Text style={[styles.loadingText, {color: colors.text}]}>
            Loading availability settings...
          </Text>
        </View>
      ) : (
        <>
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}>
            {/* Working Days Section */}
            <View style={[styles.section, {backgroundColor: colors.surface}]}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons
                  name="calendar-check"
                  size={hp('2.5%')}
                  color={colors.primary}
                />
                <Text style={[styles.sectionTitle, {color: colors.text}]}>
                  Working Days & Hours
                </Text>
              </View>
              <Text
                style={[
                  styles.sectionDescription,
                  {color: colors.textSecondary},
                ]}>
                Configure your restaurant's operating days and hours
              </Text>

              {settings.workingDays.map((day: WorkingDay, index: number) => (
                <WorkingDayCard
                  key={day.day}
                  day={day}
                  index={index}
                  colors={colors}
                  onToggle={toggleWorkingDay}
                  onTimeChange={handleTimeChange}
                />
              ))}
            </View>

            {/* Report Send Time Section */}
            <View style={[styles.section, {backgroundColor: colors.surface}]}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons
                  name="bell-outline"
                  size={hp('2.5%')}
                  color={colors.primary}
                />
                <Text style={[styles.sectionTitle, {color: colors.text}]}>
                  Report Send Time
                </Text>
              </View>
              <Text
                style={[
                  styles.sectionDescription,
                  {color: colors.textSecondary},
                ]}>
                Reports will only be sent during working hours
              </Text>

              <TouchableOpacity
                style={[
                  styles.reportTimeButton,
                  {
                    borderColor: isReportTimeValid()
                      ? colors.success || colors.primary
                      : colors.error,
                    backgroundColor: colors.background,
                  },
                ]}
                onPress={() =>
                  setShowTimePicker({visible: true, type: 'report'})
                }>
                <MaterialCommunityIcons
                  name="bell-ring-outline"
                  size={hp('2.5%')}
                  color={colors.primary}
                />
                <Text style={[styles.reportTimeText, {color: colors.text}]}>
                  {formatTime(settings.reportSendTime)}
                </Text>
                {!isReportTimeValid() && (
                  <MaterialCommunityIcons
                    name="alert-circle"
                    size={hp('2%')}
                    color={colors.error}
                  />
                )}
              </TouchableOpacity>

              {!isReportTimeValid() && (
                <Text style={[styles.warningText, {color: colors.error}]}>
                  Report time must be within working hours
                </Text>
              )}
            </View>

            {/* Bottom padding to ensure content doesn't hide behind fixed button */}
            <View style={styles.bottomPadding} />
          </ScrollView>

          {/* Fixed Save Button */}
          <View
            style={[
              styles.fixedSaveButton,
              {
                backgroundColor: colors.background,
                borderTopColor: colors.border,
              },
            ]}>
            <AppButton
              Title={hasData ? 'Update' : 'Save'}
              OnPress={saveSettings}
              disabled={loading || !isReportTimeValid()}
            />
          </View>
        </>
      )}

      {/* Time Picker Modal */}
      {showTimePicker.visible && (
        <DateTimePicker
          value={
            showTimePicker.type === 'report'
              ? settings.reportSendTime
              : showTimePicker.type === 'open'
              ? settings.workingDays[showTimePicker.dayIndex!].openTime
              : settings.workingDays[showTimePicker.dayIndex!].closeTime
          }
          mode="time"
          is24Hour={false}
          display="default"
          onChange={(event, selectedTime) => {
            setShowTimePicker({visible: false, type: 'open'});
            if (selectedTime) {
              updateTime(
                showTimePicker.dayIndex,
                showTimePicker.type,
                selectedTime,
              );
            }
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    borderRadius: 15,
    padding: hp('2%'),
    marginBottom: hp('2%'),
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp('1%'),
  },
  sectionTitle: {
    fontFamily: Fonts.OnestBold,
    fontSize: hp('2%'),
    marginLeft: wp('2%'),
  },
  sectionDescription: {
    fontFamily: Fonts.OnestRegular,
    fontSize: hp('1.6%'),
    marginBottom: hp('2%'),
  },
  dayCard: {
    borderRadius: 10,
    padding: hp('1.5%'),
    marginBottom: hp('1%'),
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp('1%'),
  },
  dayName: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.8%'),
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: wp('2%'),
  },
  timeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: hp('1%'),
    borderRadius: 8,
    borderWidth: 1,
  },
  timeText: {
    fontFamily: Fonts.OnestRegular,
    fontSize: hp('1.6%'),
    marginLeft: wp('2%'),
  },
  reportTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: hp('1.5%'),
    borderRadius: 10,
    borderWidth: 2,
    gap: wp('2%'),
  },
  reportTimeText: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.8%'),
  },
  warningText: {
    fontFamily: Fonts.OnestRegular,
    fontSize: hp('1.4%'),
    marginTop: hp('1%'),
    textAlign: 'center',
  },
  bottomPadding: {
    height: hp('10%'),
  },
  fixedSaveButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: wp('4%'),
    paddingVertical: hp('2%'),
    borderTopWidth: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp('10%'),
    paddingHorizontal: wp('5%'),
  },
  loadingText: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.8%'),
    marginTop: hp('3%'),
    textAlign: 'center',
  },
  statusContainer: {
    marginHorizontal: wp('4%'),
    marginVertical: hp('2%'),
    borderRadius: 15,
    padding: hp('2%'),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  statusContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIconContainer: {
    position: 'relative',
    marginRight: wp('3%'),
  },
  statusIcon: {
    marginLeft: wp('0.5%'),
  },
  statusTextContainer: {
    flex: 1,
  },
  statusTitle: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.6%'),
    marginBottom: hp('0.5%'),
  },
  statusText: {
    fontFamily: Fonts.OnestBold,
    fontSize: hp('1.8%'),
  },
});

export default NotificationSettings;
