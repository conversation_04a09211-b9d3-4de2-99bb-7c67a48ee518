import React from 'react';
import {Switch, View, Text, StyleSheet} from 'react-native';
import {FontSizes} from '../../styles/fonts';

// Define types for props
interface CustomSwitchProps {
  name?: string; // Name of the switch
  value?: boolean; // Whether the switch is ON or OFF
  onValueChange?: (value: boolean) => void; // Handler for change event
  label?: string; // Optional label
}

const AppSwitch: React.FC<CustomSwitchProps> = ({
  name,
  value,
  onValueChange,
  label,
}) => {
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.switchWrapper}>
        <View style={[styles.switchContainer, value ? styles.on : styles.off]}>
          <Switch
            value={value}
            onValueChange={onValueChange}
            disabled={!value}
          />
          <Text style={styles.switchStatus}>{value ? 'Open' : 'Closed'}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
  },
  switchWrapper: {
    alignItems: 'flex-end',
    width: '100%',
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    // borderWidth: 2,
    // borderColor: '#ddd',
    // width: 100,
    justifyContent: 'space-between',
    paddingVertical: 7,
    paddingHorizontal: 5,
  },
  on: {
    backgroundColor: '#d7fff1',
    // borderColor: '#388E3C',
  },
  off: {
    backgroundColor: '#ffccd5',
    // borderColor: '#ac1c1e',
  },
  switchStatus: {
    fontSize: FontSizes.medium,
    fontWeight: 'bold',
    color: '#000',
  },
});

export default AppSwitch;
