import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
  ScrollView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DropDownPicker from 'react-native-dropdown-picker';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {Employee, Lottery_Permissions} from '../../../server/types';
import {
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {App_User} from '../../../Types/Lottery/Lottery_Types';
import {createItem, updateItem} from '../../../server/service';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {Fonts} from '../../../styles/fonts';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../../constants/MaterialColors';

const LOTTERY_PERMISSION_LABELS = {
  StartOrEndShift: 'Start/End Shift',
  ActivateBook: 'Activate Books',
  CreateNewGame: 'Create New Game',
  OrganizeSlot: 'Organize Slots',
  ViewShift: 'View Shift Report',
  ResetShift: 'Reset Shift',
  CFA_Inven_Add: 'Add Inventory',
  CFA_Inven_Edit: 'Edit Inventory',
  CFA_Vendors_Add: 'Add Vendors',
  CFA_Depts_Add: 'Add Department',
  CFA_Depts_Edit: 'Edit Department',
  CFA_INVEN_VIEW: 'View Inventory',
  CFA_HH_Create_PO: 'Create PO ',
  CFA_HH_DSD: 'Direct Store Delivery',
  CFA_HH_Inv_Count: 'Inventory Count',
  CFA_HH_PO_Receive: 'Return Vendor ',
  CFA_HH_Inv_Adjust: 'Adjust Inventory',
  CFA_HH_PRINT_LABELS: 'Print Labels ',
};

const PermissionGrid = ({title, permissions, state, setState, labels}) => {
  return (
    <View style={styles.permissionSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.permissionGrid}>
        {permissions.map((item, index) => (
          <View key={index} style={styles.permissionItem}>
            <CustomCheckbox
              isChecked={state[item] || false}
              onChange={value => setState(prev => ({...prev, [item]: value}))}
            />
            <Text style={styles.permissionLabel}>
              {labels[item] || item.replace('CFA_', '').replace('_', ' ')}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const AddUserModal = ({visible, onClose, onUserAdded}) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('manager'); // Default role
  const [password, setPassword] = useState('');
  const [employeeId, setEmployeeId] = useState('');
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState([
    {label: 'Manager', value: 'manager'},
    {label: 'Cashier', value: 'cashier'},
  ]);

  const [allEmployee, setAllEmployee] = useState<Employee[]>([]);
  const [selectedValue, setSelectedValue] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedEmployeeDetail, setSelectedEmployeeDetail] =
    useState<string>('');
  const [permissions, setPermissions] = useState({});
  const [storedPermissions, setStoredPermissions] = useState({});
  const [storedLotteryPermissions, setStoredLotteryPermissions] = useState({});
  const [lotteryPermissions, setLotteryPermissions] = useState({});
  const lotteryPermissionList = Object.keys(LOTTERY_PERMISSION_LABELS);

  // Keep existing functions
  const resetForm = () => {
    setName('');
    setEmail('');
    setPassword('');
    setRole('manager');
    setSelectedValue('');

    const resetPermissions = {};
    lotteryPermissionList.forEach(permission => {
      resetPermissions[permission] = false;
    });
    setLotteryPermissions(resetPermissions);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const createLotteryPermission = async () => {
    // Keep existing implementation
    const InventoryRef: Lottery_Permissions = {
      Emp_ID: selectedValue,
      Emp_Name:
        selectedEmployeeDetail.EmpName ||
        selectedEmployeeDetail.name ||
        name ||
        '',
      StartOrEndShift: lotteryPermissions['StartOrEndShift'] || false,
      ActivateBook: lotteryPermissions['ActivateBook'] || false,
      CreateNewGame: lotteryPermissions['CreateNewGame'] || false,
      OrganizeSlot: lotteryPermissions['OrganizeSlot'] || false,
      ViewShift: lotteryPermissions['ViewShift'] || false,
      ResetShift: lotteryPermissions['ResetShift'] || false,
      CFA_Inven_Add: lotteryPermissions['CFA_Inven_Add'] || false,
      CFA_Inven_Edit: lotteryPermissions['CFA_Inven_Edit'] || false,
      CFA_Vendors_Add: lotteryPermissions['CFA_Vendors_Add'] || false,
      CFA_Depts_Add: lotteryPermissions['CFA_Depts_Add'] || false,
      CFA_Depts_Edit: lotteryPermissions['CFA_Depts_Edit'] || false,
      CFA_INVEN_VIEW: lotteryPermissions['CFA_INVEN_VIEW'] || false,
      CFA_HH_Create_PO: lotteryPermissions['CFA_HH_Create_PO'] || false,
      CFA_HH_DSD: lotteryPermissions['CFA_HH_DSD'] || false,
      CFA_HH_Inv_Count: lotteryPermissions['CFA_HH_Inv_Count'] || false,
      CFA_HH_PO_Receive: lotteryPermissions['CFA_HH_PO_Receive'] || false,
      CFA_HH_Inv_Adjust: lotteryPermissions['CFA_HH_Inv_Adjust'] || false,
      CFA_HH_PRINT_LABELS: lotteryPermissions['CFA_HH_PRINT_LABELS'] || false,
    };
    console.log('Data', InventoryRef);
    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: selectedValue},
      );

      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        const result = await createItem(
          (await getLotteryPort()).toString(),
          '/createlotpermission',
          InventoryRef,
        );
        if (result) {
          //Alert.alert('Permission Updated!');
        }
      } else {
        const result = await updateItem(
          (await getLotteryPort()).toString(),
          '/updatelotpermission',
          InventoryRef,
        );
        if (result) {
          Alert.alert(
            'Permission Updated!',
            'Permission Updated Successfully!',
          );
        }
      }
    } catch (error) {
      console.error('Error saving lottery permissions:', error);
    }
  };

  const handleAddUser = async () => {
    // Keep existing implementation
    if (!name || !password || !selectedValue) {
      Alert.alert(
        'Please Fill The Required Details!',
        'All fields are required!',
      );
      return;
    }

    try {
      const getExistUser = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetUserExist/:Cashier_ID',
        {Cashier_ID: selectedValue},
      );

      if (Array.isArray(getExistUser) && getExistUser.length === 0) {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;

        const organizationData = await AsyncStorage.getItem('organizationData');
        if (organizationData) {
          const parsedOrgData = JSON.parse(organizationData);
          const AddUser: App_User = {
            Cashier_ID: selectedValue,
            Emp_Name: name,
            Password: password,
            Role: role,
            Station_ID: '01',
            Store_ID: ValidStore,
            Organization_ID: parsedOrgData?.id,
            Status: true,
          };

          const result = await createItem(
            (await getLotteryPort()).toString(),
            '/createuser',
            AddUser,
          );
          if (result) {
            resetForm();
            onClose();
            await createLotteryPermission();
            Alert.alert('User Added Success!', 'User Created Successfully!');
          } else {
            onClose();
            Alert.alert('User Added Faild!', 'User Not Created!');
          }
        }
      } else {
        Alert.alert('User Already Exists');
      }
    } catch (error) {
      console.log('Error', error);
    }
  };

  useEffect(() => {
    getAllEmployee();
  }, []);

  useEffect(() => {
    if (!visible) {
      resetForm();
    }
  }, [visible]);

  const allEmployeeOptions = allEmployee.map(employee => ({
    label: employee.Cashier_ID,
    value: employee.Cashier_ID,
  }));

  const getAllEmployee = async () => {
    GetAllItems<Employee[]>(
      (await getInventoryPort()).toString(),
      '/getAllEmployee',
      setAllEmployee,
      setLoading,
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={handleClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Add New User</Text>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <MaterialCommunityIcons
                name="close"
                size={20}
                color={MaterialColors.text.secondary}
              />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            <View style={styles.modalBody}>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Name</Text>
                <View style={styles.textInputContainer}>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter user name"
                    value={name}
                    onChangeText={setName}
                    placeholderTextColor={MaterialColors.text.light}
                  />
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Password</Text>
                <View style={styles.textInputContainer}>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    placeholderTextColor={MaterialColors.text.light}
                  />
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>User Role</Text>
                <DropDownPicker
                  open={open}
                  value={role}
                  items={items}
                  setOpen={setOpen}
                  setValue={setRole}
                  setItems={setItems}
                  style={styles.picker}
                  dropDownContainerStyle={styles.dropDownPickerContainer}
                  labelStyle={styles.pickerLabel}
                  placeholderStyle={styles.pickerPlaceholder}
                  listItemLabelStyle={styles.pickerItemLabel}
                  selectedItemLabelStyle={styles.pickerSelectedItemLabel}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Employee ID</Text>
                <View style={styles.dropdownContainer}>
                  <AppDropDown
                    options={allEmployeeOptions}
                    selectedValue={selectedValue}
                    onSelect={value => setSelectedValue(value)}
                    isRequired={true}
                  />
                </View>
              </View>

              <PermissionGrid
                title="App Permissions"
                permissions={lotteryPermissionList}
                state={lotteryPermissions}
                setState={setLotteryPermissions}
                labels={LOTTERY_PERMISSION_LABELS}
              />
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={[styles.button, styles.outlineButton]}
              onPress={handleClose}>
              <Text style={styles.outlineButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={handleAddUser}>
              <Text style={styles.buttonText}>Add User</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxWidth: 500,
    backgroundColor: MaterialColors.surface,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[200],
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: MaterialColors.text.primary,
  },
  closeButton: {
    padding: 6,
  },
  scrollContent: {
    maxHeight: '70%',
  },
  modalBody: {
    padding: 20,
  },
  inputContainer: {
    marginBottom: 20,
    width: '100%',
  },
  inputLabel: {
    fontSize: 14,
    color: MaterialColors.text.primary,
    fontWeight: '500',
    marginBottom: 6,
  },
  textInputContainer: {
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    borderRadius: 12,
    backgroundColor: MaterialColors.surface,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  input: {
    width: '100%',
    fontSize: 16,
    paddingVertical: 14,
    paddingHorizontal: 16,
    color: MaterialColors.text.primary,
  },
  dropdownContainer: {
    zIndex: 1000,
    borderRadius: 12,
    overflow: 'hidden',
  },
  picker: {
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: MaterialColors.surface,
  },
  dropDownPickerContainer: {
    borderColor: MaterialColors.grey[300],
    borderRadius: 12,
    backgroundColor: MaterialColors.surface,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  pickerLabel: {
    color: MaterialColors.text.primary,
    fontSize: 16,
  },
  pickerPlaceholder: {
    color: MaterialColors.text.light,
    fontSize: 16,
  },
  pickerItemLabel: {
    color: MaterialColors.text.primary,
    fontSize: 16,
  },
  pickerSelectedItemLabel: {
    color: MaterialColors.primary.main,
    fontWeight: '600',
  },
  permissionSection: {
    marginTop: 10,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: MaterialColors.text.primary,
    marginBottom: 12,
  },
  permissionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: MaterialColors.grey[50],
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: MaterialColors.grey[200],
  },
  permissionItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  permissionLabel: {
    marginLeft: 8,
    color: MaterialColors.text.secondary,
    fontSize: 12,
    flex: 1,
    flexWrap: 'wrap',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: MaterialColors.grey[200],
  },
  button: {
    flex: 1,
    padding: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  primaryButton: {
    backgroundColor: MaterialColors.primary.main,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonText: {
    color: MaterialColors.surface,
    fontSize: 16,
    fontWeight: '600',
  },
  outlineButtonText: {
    color: MaterialColors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default AddUserModal;
