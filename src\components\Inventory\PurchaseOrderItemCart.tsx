import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

type Props = {
  Name?: string;
  Ordered?: number;
  RecivedNow?: number;
  Recived?: number;
  DamagedNow?: number;
  Delete?: () => void;
  Edit?: () => void;
  OnPress?: () => void;
  ItemCost?: number;
  POType?: boolean;
};
const PurchaseOrderItemCart = ({
  Name,
  Ordered,
  DamagedNow,
  Recived,
  RecivedNow,
  OnPress,
  Delete,
  Edit,
  ItemCost,
  POType = false,
}: Props) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const truncatedName = Name.length > 20 ? Name.substring(0, 23) + '...' : Name;

  return (
    <TouchableOpacity
      style={{
        marginVertical: 3,
        alignItems: 'center',
        paddingVertical: 7,
        paddingHorizontal: 10,
        backgroundColor: colors.card,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        borderRadius: 15,
        gap: 5,
        shadowColor: colors.shadow,
        shadowOpacity: isDark ? 0.3 : 0.1,
        elevation: 2,
      }}
      onPress={OnPress}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '93%',
        }}>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.small,
            width: wp('68%'),
            color: colors.text,
          }}>
          {truncatedName || 'Not Available'}
        </Text>
        {POType && !RecivedNow && (
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 5,
              backgroundColor: Primary,
              paddingVertical: 5,
              paddingHorizontal: 8,
              borderRadius: 15,
              marginLeft: -20,
            }}
            onPress={Edit}>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                color: Backround,
                fontSize: hp('1.5%'),
              }}>
              Edit Qty
            </Text>
            <MaterialIcons name="mode-edit" color={Backround} size={15} />
          </TouchableOpacity>
        )}
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '93%',
        }}>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.small,
            color: '#A1A1A1',
          }}>
          Orderd: {Ordered || 0}
        </Text>
        <TouchableOpacity onPress={Delete}>
          <MaterialCommunityIcons
            name="delete-outline"
            color={'tomato'}
            size={24}
          />
        </TouchableOpacity>
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '93%',
        }}>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.small,
            color: '#A1A1A1',
            paddingRight: 15,
          }}>{`Cost: $${ItemCost?.toFixed(2)}`}</Text>
        <Text
          style={
            RecivedNow > 0
              ? {
                  color: 'green',
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                }
              : {
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                  color: '#A1A1A1',
                }
          }>
          Recived: {RecivedNow || 0}
        </Text>
        <Text
          style={
            DamagedNow > 0
              ? {
                  color: 'red',
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                  paddingLeft: 15,
                }
              : {
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.small,
                  color: '#A1A1A1',
                  paddingLeft: 15,
                }
          }>
          Damaged: {DamagedNow || 0}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default PurchaseOrderItemCart;
