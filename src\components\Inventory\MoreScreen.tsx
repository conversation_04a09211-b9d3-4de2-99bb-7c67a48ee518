import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Fonts} from '../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';

type Props = {
  PageName: string;
  Description: string;
  Icon?: React.ReactNode;
  Onpress: () => void;
};

const MoreScreen = ({PageName, Description, Icon, Onpress}: Props) => {
  const truncatedDescription =
    Description.length > 45
      ? Description.substring(0, 45) + '...'
      : Description;

  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 14,
    },
    iconContainer: {
      marginRight: wp('3%'),
      width: hp('4.5%'),
      height: hp('4.5%'),
      alignItems: 'center',
      justifyContent: 'center',
    },
    textContainer: {
      flex: 1,
    },
    title: {
      fontSize: hp('2%'),
      fontFamily: Fonts.OnestMedium,
      marginBottom: hp('0.3%'),
    },
    description: {
      fontSize: hp('1.6%'),
      fontFamily: Fonts.OnestRegular,
    },
  });

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={Onpress}
      activeOpacity={0.7}>
      {Icon && <View style={styles.iconContainer}>{Icon}</View>}

      <View style={styles.textContainer}>
        <Text style={[styles.title, {color: colors.text}]}>{PageName}</Text>
        <Text style={[styles.description, {color: colors.textSecondary}]}>
          {truncatedDescription}
        </Text>
      </View>

      <Ionicons
        name="chevron-forward"
        color={colors.textSecondary}
        size={hp('2.3%')}
      />
    </TouchableOpacity>
  );
};

export default MoreScreen;
