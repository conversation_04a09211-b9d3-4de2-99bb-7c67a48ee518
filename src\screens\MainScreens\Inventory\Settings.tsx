import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  StatusBar,
} from 'react-native';
import React, {useCallback, useState, useEffect} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useFocusEffect} from '@react-navigation/native';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts} from '../../../styles/fonts';
import {GetAllItems, showAlert} from '../../../utils/PublicHelper';
import {Department, Employee} from '../../../server/types';
import {getInventoryPort} from '../../../server/InstanceTypes';

// Icons
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Feather from 'react-native-vector-icons/Feather';
import Header from '../../../components/Inventory/Header';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import {ThemeSwitch} from '../../../components/common/ThemeSwitch';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

type SettingsItemProps = {
  icon: React.ReactNode;
  title: string;
  onPress: () => void;
  isLast?: boolean;
};

const Settings: React.FC<NavProps> = ({navigation}) => {
  const [lottery, setLottery] = useState<Department[]>([]);
  const [selectedLottery, setSelectedLottery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [employee, setEmployee] = useState();
  const [userRole, setUserRole] = useState<string>('');
  const [userData, setUserData] = useState<any>(null);

  useFocusEffect(
    useCallback(() => {
      getAllDepartment();
      getUserData();
    }, []),
  );

  useEffect(() => {
    const fetchUserRole = async () => {
      const userData = await AsyncStorage.getItem('userData');
      console.log('USERROLED', userData);

      if (userData) {
        const parsedUserData = JSON.parse(userData);
        setUserRole(parsedUserData.role || parsedUserData.Role);
      }
    };
    fetchUserRole();
  }, []);

  const getUserData = async () => {
    try {
      const data = await AsyncStorage.getItem('userData');
      if (data) {
        setUserData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error getting user data', error);
    }
  };

  const getAllDepartment = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setLottery,
      setLoading,
    );

    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      setSelectedLottery(lotteryDepartment);
    } else {
      selectLotterDepartment('');
    }

    const employeeData = await AsyncStorage.getItem('userData');
    if (employeeData) {
      const parsedEmployee = JSON.parse(employeeData);
      setEmployee(parsedEmployee.name || parsedEmployee.Emp_Name);
    }
  };

  const Logout = () => {
    showAlert('Are you sure you want to Logout?')
      .then(async result => {
        if (result) {
          await AsyncStorage.removeItem('userToken');
          await AsyncStorage.removeItem('userData');
          await AsyncStorage.removeItem('organizationData');
          await AsyncStorage.removeItem('SWIPEID');
          await AsyncStorage.removeItem('EMPLOYEE');
          await AsyncStorage.removeItem('IsShiftStarted');
          await AsyncStorage.removeItem('SWIPEID');
          await AsyncStorage.removeItem('EMPLOYEE');
          await AsyncStorage.removeItem('ISEMPLOYEELOGGED');
          navigation.reset({
            index: 0,
            routes: [{name: 'LoginScreen'}],
          });
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const ConfigureIP = () => {
    showAlert('Are you sure you want to Configure Ip Again?')
      .then(async result => {
        if (result) {
          await AsyncStorage.removeItem('LOCALIP');
          await AsyncStorage.removeItem('STOREID');
          await AsyncStorage.removeItem('STATIONID');
          navigation.navigate('ConfigureIp');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const selectLotterDepartment = async (selectLottery: string) => {
    await AsyncStorage.setItem('LOTTERY_DEP_ID', selectLottery);
    setSelectedLottery(selectLottery);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const SettingsItem = ({
    icon,
    title,
    onPress,
    isLast = false,
  }: SettingsItemProps) => (
    <TouchableOpacity
      style={[
        styles.settingItem,
        !isLast && [styles.withBorder, {borderBottomColor: colors.border}],
      ]}
      onPress={onPress}
      activeOpacity={0.7}>
      <View style={styles.settingItemContent}>
        <View style={styles.iconContainer}>{icon}</View>
        <Text style={[styles.settingTitle, {color: colors.text}]}>{title}</Text>
      </View>
      <Ionicons
        name="chevron-forward"
        size={hp('2.2%')}
        color={colors.textSecondary}
      />
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      {/* Common Header */}
      <Header NavName="Settings" />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <TouchableOpacity
          style={[styles.profileCard, {backgroundColor: colors.surface}]}
          activeOpacity={0.7}>
          <View style={styles.profileAvatar}>
            <FontAwesome
              name="user-circle"
              size={hp('7%')}
              color={colors.primary}
            />
          </View>
          <View style={styles.profileInfo}>
            <Text style={[styles.profileName, {color: colors.text}]}>
              {employee ? employee : 'User'}
            </Text>

            <Text style={[styles.profileRole, {color: colors.primary}]}>
              {userRole || 'Unknown'}
            </Text>
            {employee?.email && (
              <Text
                style={[styles.profileEmail, {color: colors.textSecondary}]}>
                {employee.email}
              </Text>
            )}
          </View>
        </TouchableOpacity>

        {/* Settings Options */}
        <View style={[styles.settingsGroup, {backgroundColor: colors.surface}]}>
          <SettingsItem
            icon={
              <MaterialCommunityIcons
                name="cog-sync-outline"
                size={hp('2.5%')}
                color={colors.primary}
              />
            }
            title="Configure IP"
            onPress={ConfigureIP}
          />

          <SettingsItem
            icon={
              <FontAwesome
                name="ticket"
                size={hp('2.5%')}
                color={colors.primary}
              />
            }
            title="Lottery Setup"
            onPress={() => navigation.navigate('LotterySetup')}
            isLast={false}
          />

          <SettingsItem
            icon={
              <MaterialCommunityIcons
                name="bell-outline"
                size={hp('2.5%')}
                color={colors.primary}
              />
            }
            title="Notification Settings"
            onPress={() => navigation.navigate('NotificationSettings')}
            isLast={userRole !== 'owner'}
          />

          {userRole === 'owner' && (
            <SettingsItem
              icon={
                <MaterialCommunityIcons
                  name="account-group"
                  size={hp('2.5%')}
                  color={colors.primary}
                />
              }
              title="User Management"
              onPress={() => navigation.navigate('UserSettings')}
              isLast={true}
            />
          )}
        </View>

        <ThemeSwitch />

        {/* Log out option */}
        <View style={[styles.settingsGroup, {backgroundColor: colors.surface}]}>
          <SettingsItem
            icon={
              <FontAwesome
                name="sign-out"
                size={hp('2.5%')}
                color={colors.error}
              />
            }
            title="Log Out"
            onPress={Logout}
            isLast={true}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scrollContent: {
    // paddingHorizontal: wp('5%'),
    // paddingBottom: hp('5%'),
  },
  profileCard: {
    borderRadius: 15,
    padding: hp('2%'),
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp('2%'),
  },
  profileAvatar: {
    marginRight: wp('4%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontFamily: Fonts.OnestBold,
    fontSize: 14,
  },
  profileRole: {
    fontFamily: Fonts.OnestMedium,
    fontSize: 10,
    marginBottom: hp('0.5%'),
  },
  profileEmail: {
    fontFamily: Fonts.OnestRegular,
    fontSize: hp('1.6%'),
  },
  settingsGroup: {
    borderRadius: 15,
    overflow: 'hidden',
    marginBottom: hp('2%'),
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp('1.8%'),
    paddingHorizontal: wp('4%'),
  },
  settingItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: wp('4%'),
    width: hp('3.5%'),
    height: hp('3.5%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingTitle: {
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.8%'),
  },
  withBorder: {
    borderBottomWidth: 1,
  },
});

export default Settings;
