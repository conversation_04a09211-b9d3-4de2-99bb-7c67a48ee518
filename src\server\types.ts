// src/api/types.ts

export interface Inventory {
  ItemNum?: string;
  ItemName: string;
  Store_ID?: string;
  Cost: number;
  Price?: number;
  Retail_Price?: number;
  In_Stock: number;
  Reorder_Level?: number;
  Reorder_Quantity?: number;
  Tax_1?: boolean;
  Tax_2?: boolean;
  Tax_3?: boolean;
  Vendor_Number?: string;
  Dept_ID?: string;
  IsKit?: boolean;
  IsModifier?: boolean;
  Kit_Override?: number;
  Inv_Num_Barcode_Labels?: number;
  Use_Serial_Numbers?: boolean;
  Num_Bonus_Points?: number;
  IsRental?: boolean;
  Use_Bulk_Pricing?: boolean;
  Print_Ticket?: boolean;
  Print_Voucher?: boolean;
  Num_Days_Valid?: number;
  IsMatrixItem?: boolean;
  Vendor_Part_Num?: string;
  Location?: string | null;
  AutoWeigh?: boolean;
  numBoxes?: number;
  Dirty?: boolean;
  Tear?: number;
  NumPerCase?: number;
  FoodStampable?: boolean;
  ReOrder_Cost?: number;
  Helper_ItemNum?: string;
  ItemName_Extra?: string;
  Exclude_Acct_Limit?: boolean;
  Check_ID?: boolean;
  Old_InStock?: number;
  Date_Created?: Date | string;
  ItemType?: number;
  Prompt_Price?: boolean;
  Prompt_Quantity?: boolean;
  Inactive?: number;
  Allow_BuyBack?: boolean;
  Last_Sold?: Date | string;
  Unit_Type?: string;
  Unit_Size?: number;
  Fixed_Tax?: number;
  DOB?: number;
  Special_Permission?: boolean;
  Prompt_Description?: boolean;
  Check_ID2?: boolean;
  Count_This_Item?: boolean;
  Transfer_Cost_Markup?: number;
  Print_On_Receipt?: boolean;
  Transfer_Markup_Enabled?: boolean;
  As_Is?: boolean;
  InStock_Committed?: number;
  RequireCustomer?: boolean;
  PromptCompletionDate?: boolean;
  PromptInvoiceNotes?: boolean;
  Prompt_DescriptionOverDollarAmt?: number;
  Exclude_From_Loyalty?: boolean;
  BarTaxInclusive?: boolean;
  ScaleSingleDeduct?: boolean;
  GLNumber?: string;
  ModifierType?: number;
  Position?: number;
  numberOfFreeToppings?: number;
  ScaleItemType?: number;
  DiscountType?: number;
  AllowReturns?: boolean;
  SuggestedDeposit?: number;
  Liability?: boolean;
  IsDeleted?: boolean;
  ItemLocale?: number;
  QuantityRequired?: number;
  AllowOnDepositInvoices?: boolean;
  Import_Markup?: number;
  PricePerMeasure?: number;
  UnitMeasure?: number;
  ShipCompliantProductType?: string;
  AlcoholContent?: number;
  AvailableOnline?: boolean;
  AllowOnFleetCard?: boolean;
  DoughnutTax?: boolean;
  DisplayTaxInPrice?: boolean;
  NeverPrintInKitchen?: boolean;
  RowID?: string;
  Tax_4?: boolean;
  Tax_5?: boolean;
  Tax_6?: boolean;
  DisableInventoryUpload?: boolean;
  InvoiceLimitQty?: number;
  ItemCategory?: number;
  IsRestrictedPerInvoice?: boolean;
  TagStatus?: string;
}

export interface Inventory_Filter {
  ItemNum?: string;
  ItemName: string;
  Store_ID?: string;
  Cost?: number;
  Price?: number;
  Retail_Price?: number;
  In_Stock: number;
  Reorder_Level?: number;
  Reorder_Quantity?: number;
  Tax_1?: boolean;
  Tax_2?: boolean;
  Tax_3?: boolean;
  Vendor_Number?: string;
  Dept_ID?: string;
  IsKit?: boolean;
  IsModifier?: boolean;
  Kit_Override?: number;
  Inv_Num_Barcode_Labels?: number;
  Use_Serial_Numbers?: boolean;
  Num_Bonus_Points?: number;
  IsRental?: boolean;
  Use_Bulk_Pricing?: boolean;
  Print_Ticket?: boolean;
  Print_Voucher?: boolean;
  Num_Days_Valid?: number;
  IsMatrixItem?: boolean;
  Vendor_Part_Num?: string;
  Location?: string;
  AutoWeigh?: boolean;
  numBoxes?: number;
  Dirty?: boolean;
  Tear?: number;
  NumPerCase?: number;
  FoodStampable?: boolean;
  ReOrder_Cost?: number;
  Helper_ItemNum?: string;
  ItemName_Extra?: string;
  Exclude_Acct_Limit?: boolean;
  Check_ID?: boolean;
  Old_InStock?: number;
  Date_Created?: Date | string;
  ItemType?: number;
  Prompt_Price?: boolean;
  Prompt_Quantity?: boolean;
  Inactive?: number;
  Allow_BuyBack?: boolean;
  Last_Sold?: Date | string;
  Unit_Type?: string;
  Unit_Size?: number;
  Fixed_Tax?: number;
  DOB?: number;
  Special_Permission?: boolean;
  Prompt_Description?: boolean;
  Check_ID2?: boolean;
  Count_This_Item?: boolean;
  Transfer_Cost_Markup?: number;
  Print_On_Receipt?: boolean;
  Transfer_Markup_Enabled?: boolean;
  As_Is?: boolean;
  InStock_Committed?: number;
  RequireCustomer?: boolean;
  PromptCompletionDate?: boolean;
  PromptInvoiceNotes?: boolean;
  Prompt_DescriptionOverDollarAmt?: number;
  Exclude_From_Loyalty?: boolean;
  BarTaxInclusive?: boolean;
  ScaleSingleDeduct?: boolean;
  GLNumber?: string;
  ModifierType?: number;
  Position?: number;
  numberOfFreeToppings?: number;
  ScaleItemType?: number;
  DiscountType?: number;
  AllowReturns?: boolean;
  SuggestedDeposit?: number;
  Liability?: boolean;
  IsDeleted?: boolean;
  ItemLocale?: number;
  QuantityRequired?: number;
  AllowOnDepositInvoices?: boolean;
  Import_Markup?: number;
  PricePerMeasure?: number;
  UnitMeasure?: number;
  ShipCompliantProductType?: string;
  AlcoholContent?: number;
  AvailableOnline?: boolean;
  AllowOnFleetCard?: boolean;
  DoughnutTax?: boolean;
  DisplayTaxInPrice?: boolean;
  NeverPrintInKitchen?: boolean;
  RowID?: string;
  Tax_4?: boolean;
  Tax_5?: boolean;
  Tax_6?: boolean;
  DisableInventoryUpload?: boolean;
  InvoiceLimitQty?: number;
  ItemCategory?: number;
  IsRestrictedPerInvoice?: boolean;
  TagStatus?: string;
  Brand?:string;
  SubCategory?: string; 
}

export interface Department {
  Dept_ID: string;
  Description: string;
}

export interface Printers {
  deviceId: string;
  name: string;
}

export interface BrandOrSubCategory {
  Brand: string;
  SubCategory: string;
}

export interface Brands {
  Brand: string;
}

export interface SubCategories {
  SubCategory: string;
}

export interface TagAlong {
  ItemNum: string;
  Store_ID: string;
  TagAlong_ItemNum: string;
  Quantity: number;
}

export interface TagAlong_View {
  ItemNum: string;
  Store_ID: string;
  TagAlong_ItemNum: string;
  Quantity: number;
  ItemName: string;
  Dept_ID: string;
  Vendor_Number: string;
  Brand: string;
  SubCategory: string;
  
}



export interface Reason_Codes {
  Store_ID: string;
  Reason_Code: string;
  Reason_Type: number;
}

export interface Inventory_In {
  ItemNum: string | null;
  Store_ID: string | null;
  Quantity: string | number | null;
  CostPer: string | number | null;
  DateTime: string | null;
  Vendor_Number: string | null;
  Dirty: boolean | null;
  TransType: string | null;
  Destination: string | null;
  Description: string | null;
  Cashier_ID: string | null;
  PO_Number: number | null;
  Delivery_Number: string | null;
}

export interface Get_Max_ID {
  MaxValue: number;
}

export interface Get_Value {
  TotalDamaged: number;
}

export interface Inventory_Reference {
  ID: number ;
  ItemNum: string;
  Store_ID: string;
}
export interface Lottery_Permissions {
  Emp_ID: string ;
  Emp_Name: string;
  StartOrEndShift: boolean;
  ActivateBook:boolean
  CreateNewGame:boolean
  OrganizeSlot:boolean
  ViewShift:boolean
  ResetShift:boolean
  CFA_Inven_Add: boolean
  CFA_Inven_Edit: boolean
  CFA_Vendors_Add: boolean
  CFA_Depts_Add: boolean
  CFA_Depts_Edit: boolean
  CFA_INVEN_VIEW: boolean
  CFA_HH_Create_PO: boolean
  CFA_HH_DSD: boolean
  CFA_HH_Inv_Count: boolean
  CFA_HH_PO_Receive: boolean
  CFA_HH_Inv_Adjust: boolean
  CFA_HH_PRINT_LABELS: boolean
}

export interface Setup_TS_Buttons  {
  Store_ID: string  | '';           
  Station_ID: string  | '' ;        
  Index: number | 0;              
  Caption: string | '';            
  Picture: string | '';           
  Function: number | 0;           
  Option1: string | '';           
  BackColor: number | 0;         
  ForeColor: number | null | 0;      
  Visible: boolean | true;           
  BtnType: number | 0;           
  Ident: string | '';              
  ScheduleIndex: number | 0;                  
  Option2: string | '';            
  Option3: string | '';            
  Option4: boolean | false;           
  HideCaption: boolean | false;       
};
export interface Department_CRUD {
  Dept_ID: string | '';
  Store_ID: string | '';
  Description: string | '';
  Type: number | 0;
  TSDisplay: boolean | false;
  Cost_MarkUp: number | 0;
  Dirty: boolean | false;
  SubType: string | '';
  Print_Dept_Notes: boolean | false;
  Dept_Notes: string | '';
  Require_Permission: boolean | false;
  Require_Serials: boolean | false;
  BarTaxInclusive: boolean | false;
  Cost_Calculation_Percentage: number | 0;
  Square_Footage: number | 0;
  AvailableOnline: boolean | false;
  IncludeInScaleExport: boolean | false;
}


export interface Kit_Index {
  Kit_ID: string | '',
  Store_ID: string | '',
  ItemNum: string| '',
  Discount: number | 0,
  Quantity: number | 0,
  Index: number | 0,
  Price: number | 0,
  Description: string | '',
  InvoiceMethodToUse: number | 0,
  ChoiceLockdown: number | 0
}

export interface Bump_Bar_Settings {
  Store_ID: string | '',
  ItemNum: string | '',
  Backcolor: number | 0,
  Forecolor: number | 0
}

export interface Setup_Reason_Codes {
  Store_ID: string | '',
  Reason_Code: string | '',
  Reason_Type: number | 0,
}

export interface AdditionalInfo{
  Store_ID: string | '',
  ItemNum: string | '',
  ExtendedDescription: string | '',
  Keywords: string | '',
  Brand: string | '',
  Theme: string | '',
  SubCategory: string | '',
  LeadTime: string | '',
  ProductOnPromotionPreOrder: boolean | false,
  ProductOnSpecialOffer: boolean | false,
  NewProduct: boolean | false,
  Discountable: boolean | false,
  WebPrice: number | 0,
  ReleaseDate: string | null,
  Weight: number | 0,
  NoWebSales: boolean | false,
  IsPrimaryMatrixItem: boolean | false,
  Priority: number | 0,
  Rating: number | 0,
  CustomNumber1: number | 0,
  CustomNumber2: number | 0,
  CustomNumber3: number | 0,
  CustomNumber4: number | 0,
  CustomNumber5: number | 0,
  CustomText1: string | '',
  CustomText2: string | '',
  CustomText3: string | '',
  CustomText4: string | '',
  CustomText5: string | '',
  CustomExtendedText1: string | '',
  CustomExtendedText2: string | '',
  SubDescription1: string | '',
  SubDescription2: string | '',
  SubDescription3: string | ''

} 

export interface Invoice_Totals {
  Invoice_Number: number;
  Store_ID: string;
  CustNum: string;
  DateTime: Date | string;
  Total_Cost: number;
  Discount: number;
  Total_Price: number;
  Total_Tax1: number;
  Total_Tax2: number;
  Total_Tax3: number;
  Grand_Total: number;
  Amt_Tendered: number;
  Amt_Change: number;
  ShipToUsed: boolean;
  InvoiceNotesUsed: boolean;
  Status: string;
  Cashier_ID: string;
  Station_ID: string;
  Payment_Method: string;
  Acct_Balance_Due: number;
  Acct_FullyPaid_Date: Date | null;
  Taxed_1: number;
  Taxed_Sales: number;
  NonTaxed_Sales: number;
  Tax_Exempt_Sales: number;
  CA_Amount: number;
  CH_Amount: number;
  CC_Amount: number;
  OA_Amount: number;
  GC_Amount: number;
  Tip_Amount: number;
  Old_Balance: number;
  Num_People_Party: number;
  AcctBalanceBefore: number;
  Salesperson: string;
  Dirty: boolean;
  Zip_Code: string;
  InvType: string;
  FS_Amount: number;
  Amt_FS_AmtTend: number;
  Amt_FS_Change: number;
  DC_Amount: number;
  OA_Amount_Limited: number;
  Cost_Center_Index: number;
  Orig_OnHoldID: string | null;
  Total_FixedTax: number;
  Total_GC_Sold: number;
  Tax_Rate_ID: number;
  Tax_Rate1_Percent: number;
  Amt_CA_Sec: number;
  Exchange_Rate: number;
  IsLayaway: boolean;
  Amt_Deposit: number;
  LAY_Amount: number;
  Total_GC_Free: number;
  MacromatixSyncStatus: number;
  TotalLiability: number;
  ReferenceInvoiceNumber: string | null;
  CourseOrderingProgress: string;
  Amt_CA_Sec_Tendered: number;
  OnlineOrderID: string;
  OrderSource: number;
  OP_Amount: number;
  MP_Amount: number;
  TaxCategory: number;
  MPDiscount_Amount: number;
  Donation_Amount: number;
  Total_UndiscountedSale: number;
  EBTCashBenefit_Amount: number;
  Split_Check_Type: number;
  OnlineLoyalty_Contact_ID: string;
  Total_Tax4: number;
  Total_Tax5: number;
  Total_Tax6: number;
  AgeVerificationMethod: number;
  AgeVerification: number;
  CP_Amount: number;
  OnHoldID?: string
}

export interface Invoice_Itemized {
  Invoice_Number: number;
  LineNum?: number;
  ItemNum: string;
  Quantity: number;
  CostPer: number;
  PricePer: number;
  Tax1Per: number;
  Tax2Per: number;
  Tax3Per: number;
  Serial_Num: boolean;
  Kit_ItemNum: string;
  BC_Invoice_Number: number;
  LineDisc: number;
  DiffItemName: string;
  NumScans: number;
  numBonus: number;
  Line_Tax_Exempt: boolean;
  Commission: number;
  Store_ID: string;
  origPricePer: number;
  Allow_Discounts: boolean;
  Person: string;
  Sale_Type: number;
  Ticket_Number: string;
  IsRental: boolean;
  FixedTaxPer: number;
  GC_Sold: number;
  Special_Price_Lock: boolean;
  As_Is: boolean;
  Returned: boolean;
  DOB: number;
  UserDefined: string;
  Cashier_ID_Itemized: string;
  IsLayaway: boolean;
  ReturnedQuantity: number;
  GC_Free: number;
  ScaleItemType: number;
  ParentObjectID: string;
  BulkRate: string;
  SecurityDeposit: number;
  Liability: number;
  SalePricePer: number;
  Line_Tax_Exempt_2: boolean;
  Line_Tax_Exempt_3: boolean;
  modifierPriceLock: boolean;
  Salesperson: string;
  ComboApplied: boolean;
  KitchenQuantityPrinted: number;
  PricePerBeforeDiscount: number;
  OrigPriceSetBy: number;
  PriceChangedBy: number;
  Kit_Override: number;
  KitTotal: number;
  SentToKitchen: boolean;
  OnlineLoyalty_OfferId: string;
  Tax4Per: number;
  Tax5Per: number;
  Tax6Per: number;
  Line_Tax_Exempt_4: boolean;
  Line_Tax_Exempt_5: boolean;
  Line_Tax_Exempt_6: boolean;
  Tare: number;
}

export interface Invoice_OnHold {
  Invoice_Number: number;
  OnHoldID: string;
  Cashier_ID: string;
  Store_ID: string;
  Occupied: boolean;
  Section_ID: string;
  Status: number;
  Identifier: string;
  PreAuthorized: boolean;
  Name: string;
  Station_ID: string;
}

export interface AltSKU{
  Store_ID: string,
  ItemNum: string,
  AltSKU: string
}

export interface Unit_Types{
  ID: string;
  Unit_Type: string;
}

export interface BrandAdd{
  ID: string;
  Brand: string;
}

export interface SubCategoryAdd{
  ID: string;
  SubCategory: string;
}

export interface PurchaseOrder {
  PO_Number: number;
  Store_ID: string;
  DateTime: string;
  Reference: string;
  Vendor_Number: string;
  Total_Cost: number;
  Total_Cost_Received: number;
  Terms: string;
  Due_Date: string;
  Ship_Via: string;
  ShipTo_1: string;
  ShipTo_2: string;
  ShipTo_3: string;
  ShipTo_4: string;
  ShipTo_5: string;
  Instructions: string;
  Status: string;
  Last_Modified: string;
  Dirty: boolean;
  Cashier_ID: string;
  Billable_Department: string;
  ShipTo_Destination: string;
  Ordering_Mode: number;
  Fully_Authorized: boolean;
  Print_Notes_On_PO: boolean;
  Cancel_Date: string;
  Total_Charges: number;
  Fully_Paid: boolean;
  POType: number;
  ExpectedAmountToReceive: number;
  Order_Reason: string;
  Distributor: string;
  First_Name: string;
  Last_Name: string;
  Company: string;
  Address_1: string;
  Address_2: string;
  City: string;
  State: string;
  Zip_Code: string;
  Phone: string;
  Fax: string;
  Vendor_Tax_ID: string;
  Vendor_Terms: string;
  SSN: string;
  Commission: number;
  Rent: number;
  County: string;
  Country: string;
  Email: string;
  Website: string;
  Minimum_Order: number;
  Default_Ordering_Mode: number;
  Default_Billable_Department: string;
  Default_PO_Delivery: number;
}

export interface UpdatePurchaseOrder {
  PO_Number: number;
  Store_ID: string;
  DateTime: string;
  Reference: string;
  Vendor_Number: string;
  Total_Cost: number;
  Total_Cost_Received: number;
  Terms: string;
  Due_Date: string;
  Ship_Via: string;
  ShipTo_1: string;
  ShipTo_2: string;
  ShipTo_3: string;
  ShipTo_4: string;
  ShipTo_5: string;
  Instructions: string;
  Status: string;
  Last_Modified: string;
  Dirty: boolean;
  Cashier_ID: string;
  Billable_Department: string;
  ShipTo_Destination: string;
  Ordering_Mode: number;
  Fully_Authorized: boolean;
  Print_Notes_On_PO: boolean;
  Cancel_Date: string;
  Total_Charges: number;
  Fully_Paid: boolean;
  POType: number;
  ExpectedAmountToReceive: number;
  Order_Reason: string;
  Distributor: string;
}

export interface PurchaseOrderInsert {
  Store_ID: string;
  DateTime: string;
  Reference: string;
  Vendor_Number: string;
  Total_Cost: number;
  Total_Cost_Received: number;
  Terms: string;
  Due_Date: string;
  Ship_Via: string;
  ShipTo_1: string;
  ShipTo_2: string;
  ShipTo_3: string;
  ShipTo_4: string;
  ShipTo_5: string;
  Instructions: string;
  Status: string;
  Last_Modified: string;
  Dirty: boolean;
  Cashier_ID: string;
  Billable_Department: string;
  ShipTo_Destination: string;
  Ordering_Mode: number;
  Fully_Authorized: boolean;
  Print_Notes_On_PO: boolean;
  Cancel_Date: string;
  Total_Charges: number;
  Fully_Paid: boolean;
  POType: number;
  ExpectedAmountToReceive: number;
  Order_Reason: string;
  Distributor: string;
  First_Name: string;
  Last_Name: string;
  Company: string;
  Address_1: string;
  Address_2: string;
  City: string;
  State: string;
  Zip_Code: string;
  Phone: string;
  Fax: string;
  Vendor_Tax_ID: string;
  Vendor_Terms: string;
  SSN: string;
  Commission: number;
  Rent: number;
  County: string;
  Country: string;
  Email: string;
  Website: string;
  Minimum_Order: number;
  Default_Ordering_Mode: number;
  Default_Billable_Department: string;
  Default_PO_Delivery: number;
}

export interface Vendor {
  Vendor_Number: string;
  First_Name: string;
  Last_Name: string;
  Company: string;
  Address_1: string;
  Address_2: string;
  City: string;
  State: string;
  Zip_Code: string;
  Phone: string;
  Fax: string;
  Vendor_Tax_ID: string;
  Vendor_Terms: string;
  SSN: string;
  Commission: number;
  Rent: number;
  Dirty: boolean;
  County: string;
  Country: string;
  Email: string;
  Website: string;
  Minimum_Order: number;
  Default_Ordering_Mode: number;
  Default_Billable_Department: string;
  Default_PO_Delivery: number;
}

export interface PurchaseOrderItems {
  PO_Number: number;
  ItemNum: string;
  Quan_Ordered: number;
  CostPer: number;
  Quan_Received: number;
  Vendor_Part_Number: string;
  CasePack: number | null;
  Store_ID: string;
  destStore_ID: string;
  Current_Batch_Quan: number;
  Quan_Damaged: number;
  Reason: string;
  NumberPerCase: number;
  OverrideCommission: boolean;
  Quan_OutofDate: number;
  ItemName?: string;
}

export interface UpdatePurchaseOrderItems {
  PO_Number: number;
  ItemNum: string;
  Quan_Ordered: number;
  CostPer: number;
  Quan_Received: number;
  Vendor_Part_Number: string;
  CasePack: number;
  Store_ID: string;
  destStore_ID: string;
  Current_Batch_Quan: number;
  Quan_Damaged: number;
  Reason: string;
  NumberPerCase: number;
  OverrideCommission: boolean;
  Quan_OutofDate: number;
}

export interface Inventory_Vendors {
  ItemNum: string;
  Store_ID: string;
  Vendor_Number: string;
  CostPer: number;
  Case_Cost: number;
  NumPerVenCase: number;
  Vendor_Part_Num: string;
  CubeCost: number;
  WeightCost: number;
  OverrideCommission: boolean;
  LandedCost: number;
}


export interface VendorItem {
  ItemNum: string;
  Store_ID: string;
  Vendor_Number: string;
  CostPer: number;
  Case_Cost: number;
  NumPerVenCase: number;
  Vendor_Part_Num: string;
  CubeCost: number;
  WeightCost: number;
  OverrideCommission: boolean;
  LandedCost: number;
  ItemName?: string;
  Dept_ID?: string;
  Brand?: string;
  SubCategory?: string

}

export interface InventoryVendor {
  ItemNum: string;
  Store_ID: string;
  Vendor_Number: string;
  CostPer: number;
  Case_Cost: number;
  NumPerVenCase: number;
  Vendor_Part_Num: string;
  CubeCost: number;
  WeightCost: number;
  OverrideCommission: boolean;
  LandedCost: number;
};


export interface Employee {
  Cashier_ID: string,
  CustNum: string,
  Dept_ID: string,
  Password: string,
  Swipe_ID: string,
  Hourly_Wage: number,  // "money" type could be represented as "number"
  Form_Color: number,
  CDL: string,
  Name: string,
  CFA_Setup_Company: string,
  CFA_Setup_Tax: string,
  CFA_Setup_Bonus: string,
  CFA_Setup_Accounting: string,
  CFA_Setup_Discounts: string,
  CFA_Setup_Display: string,
  CFA_Setup_DefPrinter: string,
  CFA_Inven_Add: string,
  CFA_Inven_Edit: string,
  CFA_Vendors_Add: string,
  CFA_Vendors_Edit: string,
  CFA_Depts_Add: string,
  CFA_Depts_Edit: string,
  CFA_Inven_TickVouch: string,
  CFA_Cust_add: string,
  CFA_Cust_Edit: string,
  CFA_Reports_Display: string,
  CFA_Reports_DDR: string,
  CFA_Reports_Print: string,
  CFA_Invoice_Discount: string,
  CFA_Invoice_PriceChange: string,
  CFA_Invoice_DeleteItems: string,
  CFA_Invoice_Void: string,
  CFA_CRE_Acct: string,
  CFA_CRE_Exit: string,
  Dirty: boolean,
  Last_DDR: string,  // "datetime" type could be represented as a string or Date
  CFA_Display_Balance: string,
  CFA_Refund_Item: string,
  Disp_Pay_Option: boolean,
  Disp_Item_Option: boolean,
  EmpName: string,
  CFA_Receive_Items: string,
  CFA_DO_POS: string,
  CFA_INSTANT_POS: string,
  Section_ID: string,
  CFA_Other_Tables: string,
  CFA_Accept_Cash: string,
  CFA_TRANSFER_NOSWIPE: string,
  CFA_ADD_CCTIPS: string,
  Disabled: boolean,
  CFA_PRINT_HOLD: string,
  CFA_Open_Cash_Drawer: string,
  CCTipsNow: boolean,
  ReqClockIn: boolean,
  CFA_Split_Checks: string,
  CFA_Transfer_Tables: string,
  CFA_Extra_Item: string,
  CFA_Tax_Exempt: string,
  CFA_GC_Sell: string,
  CFA_GC_Redeem: string,
  CFA_SELL_SPECIAL_ITEM: string,
  CFA_VENDOR_PAYOUT: string,
  CFA_APPLY_GRATUITY: string,
  First_Name: string,
  Middle_Name: string,
  Last_Name: string,
  SSN: string,
  Address_1: string,
  Address_2: string,
  City: string,
  State: string,
  Zip_Code: string,
  Phone_1: string,
  EMail: string,
  Birthday: string,  // "datetime" type could be represented as a string or Date
  Picture: string,
  CFA_BUYBACKS_TRADES: string,
  CFA_CC_Force: string,
  CFA_CC_Below_Floor: string,
  Current_Cash: number,  // "money" type could be represented as "number"
  CFA_Cash_Alerts: string,
  CFA_Cash_Pickup: string,
  CDL_Stations_ID: string,
  CFA_Issue_Credit_Slip: string,
  CFA_Redeem_Credit_Slip: string,
  CFA_REFUND_OVERRIDE: string,
  CFA_DRAWER_TRANSFER: string,
  CFA_LARGE_PURCHASES: string,
  CFA_AUCTION_PHOTO: string,
  CFA_AUCTION_LISTREDEEM: string,
  CFA_AUCTION_SHIP: string,
  CFA_APPROVE_CASHCOUNT: string,
  Orig_Emp_ID: string,
  Orig_Store_ID: string,
  CD_Name: string,
  CFA_APPROVE_OLD_RETURNS: string,
  CFA_APPROVE_EMERGENCY_CLOCKOUT: string,
  TimeWorkedThisPeriod: number,  // "float" type is "number"
  OvertimeThreshold: number,  // "smallint" type is "number"
  CFA_PULLBACK_INVOICE: string,
  CFA_MANAGE_TIMECLOCK: string,
  CFA_PERFORM_ENDOFDAY: string,
  CFA_HOST_LOGIN: string,
  CFA_REST_OPENTABS: string,
  CFA_REST_TAKEOUT: string,
  CFA_REST_DELIVERY: string,
  CFA_INVOICE_DELETESENT: string,
  CFA_INVEN_VIEW: string,
  CFA_INVEN_VIEWCOST: string,
  CFA_INVEN_NEGATIVE_INSTANTPOS: string,
  CFA_ENDTRANS_CASH: string,
  CFA_ENDTRANS_ACCOUNT: string,
  CFA_REST_COMP: string,
  CFA_CH_FORCE: string,
  CFA_TS_CONFIG: string,
  CFA_TRANSFER_SERVER: string,
  CFA_BACKUP_DATABASE: string,
  CFA_CREDIT_CARD_SETTLEMENT: string,
  CFA_KITCHEN_REPRINT: string,
  CFA_SETUP_RECEIPT_NOTES: string,
  CFA_MANAGE_TIMECLOCK_OWNTIME: string,
  CFA_SETUP_ADD_EMPLOYEES: string,
  CFA_SETUP_EDIT_EMPLOYEES: string,
  CFA_INVENTORY_PROMOTIONS: string,
  CFA_INVOICE_DISCOUNTS_BELOW_X: string,
  CFA_BUYBACKTRADE_ABOVE_SET_AMOUNT: string,
  CFA_REPORTS_VIEW_HISTORICAL_DATA: string,
  CFA_INVEN_MISC_FIELD_LOCKDOWN: string,
  CFA_HH_Create_PO: string,
  CFA_HH_DSD: string,
  CFA_HH_DSD_Credit: string,
  CFA_HH_PO_Receive: string,
  CFA_HH_Inv_Edit: string,
  CFA_HH_Inv_Adjust: string,
  CFA_HH_Inv_Count: string,
  CFA_HH_Setup: string,
  CFA_CASHIER_OVERRIDE_LICENSESCAN: string,
  CFA_INVEN_DELETE: string,
  CFA_CASHIER_MANUALY_ENTER_AGE: string,
  CreateDate: string,  // "datetime" type could be represented as a string or Date
  DateDisabled: string,  // "datetime" type could be represented as a string or Date
  CFA_INVEN_ADD_COUPON: string,
  CFA_INVEN_GLOBALPRICING: string,
  CFA_EMP_SCHEDULE_OVERRIDE: string,
  CFA_LABOR_SCHEDULER: string,
  GLNumber: string,
  CFA_NEGATIVE_PRICE_CHANGE: string,
  CFA_CUSTOMER_EDIT_CHARGEATCOST: string,
  CFA_GPI_FUEL_DRIVE_OFF: string,
  CFA_SETUP_VPDCONFIGURATION: string,
  CFA_CLOSE_SHIFT: string,
  CFA_REPRINT_RECEIPT: string,
  Locked_Time: string,  // "datetime" type could be represented as a string or Date
  Retry_Count: number,  // "int" type is "number"
  Password_Hash: string,
  Salt_Key: string,
  EnableMobileInventory: boolean,
  CFA_INVOICE_LIMIT_ITEMS: string,
  CFA_HH_SHARE_PURCHASEORDERS: string,
  CFA_HH_ADD_ITEM: string,
  CFA_HH_VIEW_COST: string,
  CFA_HH_PRINT_LABELS: string,
  CFA_DELETE_CUSTOMER: string,
  CFA_RECALL_INVOICE: string
}
