import {View, Text, Alert, TouchableOpacity} from 'react-native';
import React, {useCallback, useState} from 'react';
import {Reason_Codes, Setup_Reason_Codes} from '../../../server/types';
import {createItem, deleteItem} from '../../../server/service';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetAllItems,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
} from '../../../utils/PublicHelper';
import Header from '../../../components/Inventory/Header';
import DataList from '../../../components/Inventory/AppList';
import AppButton from '../../../components/Inventory/AppButton';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AsyncStorage from '@react-native-async-storage/async-storage';

type AdjustStockScreenRouteProp = RouteProp<any, 'LotteryReason'>;

const LOT_Reason: React.FC<{
  route: AdjustStockScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [reasonCodes, setReasonCodes] = useState<Reason_Codes[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectReason, setSelectReason] = useState<string>('5');
  const [reasoncode, setReasoncode] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      geResonInit();
    }, []),
  );

  const geResonInit = async () => {
    GetItemsParamsNoFilter(
      (await getLotteryPort()).toString(),
      '/GetLotteryReasonCode/:Reason_Type',
      setReasonCodes,
      {Reason_Type: 55},
      false,
    );
  };
  const DeleteReason = (RemoveRason: Reason_Codes) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const result = await deleteItem(
            (await getLotteryPort()).toString(),
            '/DeleteReasonCode/:Reason_Code/:Reason_Type',
            {
              Reason_Code: RemoveRason.Reason_Code,
              Reason_Type: 55,
            },
          );
          if (result) {
            geResonInit();
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };
  const renderItem = ({item}: {item: Reason_Codes}) => (
    <View
      style={{
        backgroundColor: Secondary,
        paddingHorizontal: '2.5%',
        marginBottom: hp('1%'),
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
      <Text
        style={{
          fontFamily: Fonts.OnestBold,
          fontSize: 17,
          paddingVertical: '2.5%',
        }}>
        {item.Reason_Code}
      </Text>

      <TouchableOpacity onPress={() => DeleteReason(item)}>
        <MaterialCommunityIcons name="delete" color={'tomato'} size={30} />
      </TouchableOpacity>
    </View>
  );

  const reasonOptions = [
    {label: 'Return To Vendor', value: '8'},
    {label: 'Inventory Adjustment', value: '5'},
  ];

  const getReasonCodesBySelection = async (SelectedReason: string) => {
    if (SelectedReason === '' || !SelectedReason) {
      setSelectReason('');
    } else {
      setSelectReason(SelectedReason);
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/reasoncodes/:Reason_Type',
        setReasonCodes,
        {Reason_Type: SelectedReason},
        false,
      );
    }
  };

  const CreateReason = async () => {
    if (reasoncode === '' || !reasoncode) {
      Alert.alert('Please Enter the reason code');
      return;
    }
    const checkExists = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetCheckExist/:Reason_Type/:Reason_Code',
      {Reason_Type: 55, Reason_Code: reasoncode},
    );

    if (Array.isArray(checkExists) && checkExists.length === 0) {
      const storeId = await AsyncStorage.getItem('STOREID');

      const AddUnitTypes: Reason_Codes = {
        Store_ID: storeId === null ? '1001' : storeId,
        Reason_Code: reasoncode,
        Reason_Type: 55,
      };

      const result = await createItem(
        (await getLotteryPort()).toString(),
        '/createreasoncode',
        AddUnitTypes,
      );

      if (result) {
        setReasoncode('');
        geResonInit();
        Alert.alert('Reason Code Added');
      }
    } else {
      Alert.alert('Reason Code Already Exists');
    }
  };

  const handNavigation = () => {
    if (route.params?.ISFROMSHIFT) {
      //navigation.navigate('LOT_Scratch');
      navigation.navigate('IntialSetup', {screen: 'IntialSetup'});
    } else {
      navigation.goBack();
    }
  };
  return (
    <View
      style={{
        backgroundColor: Backround,
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: '2.5%'}}>
        <Header
          NavName="Resaon Codes"
          isProvid={true}
          Onpress={() => handNavigation()}
        />

        {false && (
          <AppDropDown
            label="Reason Codes Types"
            options={reasonOptions}
            selectedValue={selectReason}
            onSelect={value => {
              getReasonCodesBySelection(value);
            }}
          />
        )}

        <View style={{marginTop: 20}}>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: hp('2.2%'),
              color: Primary,
              paddingVertical: hp('1.5%'),
            }}>
            Total Reason Codes: {reasonCodes.length || 0}
          </Text>
          <DataList
            data={reasonCodes}
            renderItem={renderItem}
            loading={loading}
          />
        </View>
      </View>

      <View
        style={{
          paddingVertical: '1%',
          paddingHorizontal: '2.5%',
          position: 'absolute',
          right: 0,
          bottom: 0,
          left: 0,
        }}>
        <AppTextInput
          PlaceHolder="Enter Reason Code"
          Title="Reason Code"
          Value={reasoncode}
          onChangeText={text => setReasoncode(text)}
          isRequired={true}
        />

        <AppButton Title="Create Reason Code" OnPress={() => CreateReason()} />
      </View>
    </View>
  );
};

export default LOT_Reason;
