// components/CustomDatePicker.tsx
import React, {useState} from 'react';
import {View, Text, TouchableOpacity, Platform, StyleSheet} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {Secondary} from '../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface CustomDatePickerProps {
  label: string;
  value: Date;
  onDateChange: (date: Date) => void;
}

const AppDatePicker: React.FC<CustomDatePickerProps> = ({
  label,
  value,
  onDateChange,
}) => {
  const [show, setShow] = useState(false);

  const onChange = (event: any, selectedDate: Date | undefined) => {
    setShow(false);
    if (selectedDate) {
      onDateChange(selectedDate);
    }
  };

  const handleDatePress = () => {
    setShow(true);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      marginBottom: hp('1%'),
    },
    label: {
      fontSize: FontSizes.medium,
      marginBottom: 8,
      fontFamily: Fonts.OnestMedium,
      color: colors.text, // Changed from MaterialColors.text.primary
    },
    input: {
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.9%'),
      borderRadius: 10,
      backgroundColor: colors.surface, // Changed from MaterialColors.background
      width: wp('95%'),
      borderColor: colors.border, // Changed from MaterialColors.grey[300]
      borderWidth: 1,
    },
    dateText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text, // Added this line for text color
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity style={styles.input} onPress={handleDatePress}>
        <Text style={styles.dateText}>{value.toLocaleDateString()}</Text>
      </TouchableOpacity>

      {show && (
        <DateTimePicker
          value={value}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onChange}
        />
      )}
    </View>
  );
};

export default AppDatePicker;
