import React, { memo, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AppLoader from '../AppLoader';
import { Fonts } from '../../../styles/fonts';
import { useThemeColors } from '../../../Theme/useThemeColors';

interface CountItemListFooterProps {
  loading: boolean;
}

const CountItemListFooter: React.FC<CountItemListFooterProps> = ({ loading }) => {
  const colors = useThemeColors();
  const [appLoader, setAppLoader] = useState<boolean>(true);

  const styles = StyleSheet.create({
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
  });

  if (!loading) return null;

  return (
    <View style={styles.footer}>
      <AppLoader
        modalVisible={appLoader}
        setModalVisible={setAppLoader}
        isLookup={true}
      />
      <Text style={styles.footerText}>Loading more items...</Text>
    </View>
  );
};

export default memo(CountItemListFooter);
