import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {
  GetAllItems,
  GetItemsParamsNoFilter,
  handleSearch,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {VendorItem} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import Header from '../../../components/Inventory/Header';

type CountItemRouteProp = RouteProp<any, 'VendorItems'>;

const VendorItems: React.FC<{route: CountItemRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      vendorDetails();
    }, [page]),
  );

  useEffect(() => {
    getInitial();
  }, []);

  const vendorDetails = async () => {
    GetAllItems(
      (await getInventoryPort()).toString(),
      `/vendoritemspage?Vendor_Number=${route.params?.ItemData}&page=${page}&pageSize=${pageSize}`,
      (value: any) => setVendorItemsFilter([...vendorItemsFilter, ...value]),
      setLoading,
      true,
    );
  };

  const getInitial = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getvendoritems/:Vendor_Number',
      setVendorItems,
      {Vendor_Number: route.params?.ItemData},
    );
  };

  const handleLoadMore = () => {
    if (!loading) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const renderItem = ({item}: {item: VendorItem}) => (
    <TouchableOpacity
      style={{
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 20,
        paddingHorizontal: 20,
        justifyContent: 'space-between',
        borderWidth: 2,
        borderColor: 'dimgray',
        marginTop: 10,
        marginHorizontal: 10,
      }}
      onPress={() =>
        navigation.navigate('AddVendorItemQuanity', {ItemData: item})
      }>
      <Text style={{}}>{item.ItemNum}</Text>
      <Text style={{}}>{item.ItemName}</Text>
    </TouchableOpacity>
  );

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    setPage(1);
    handleSearch(
      text,
      vendorItems,
      ['ItemName', 'ItemNum'],
      setVendorItemsFilter,
      setLoading,
    );
  };
  return (
    <View>
      <Header NavName="Select Vendor Items" />
      <View style={{width: '100%'}}>
        <TextInput
          placeholder="Search Item here"
          placeholderTextColor={'gray'}
          value={searchQuery}
          onChangeText={onSearchChange}
          style={styles.searchInput}
        />
      </View>
      <DataList
        data={vendorItemsFilter}
        loading={loading}
        renderItem={renderItem}
        Hight="100%"
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  searchInput: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 10,
    marginBottom: 10,
    marginHorizontal: 10,
  },
});
export default VendorItems;
