import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {Primary, Secondary, SecondaryHint} from '../../constants/Color';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';

type Props = {
  OnPress?: () => void;
  Title?: string;
  Barcode?: string;
  Price?: number;
  inStock?: number;
  isCreate?: boolean;
};

const ChoieItemCard = ({
  OnPress,
  Barcode,
  Price,
  Title,
  inStock,
  isCreate,
}: Props) => {
  const colors = useThemeColors();
  return (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 6,
        paddingVertical: hp('1%'),
        paddingHorizontal: 6,
        backgroundColor: colors.surface,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}
      onPress={OnPress}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 6,
          width: wp('70%'),
        }}>
        <View
          style={{
            backgroundColor: colors.primary + '30',
            paddingHorizontal: 5,
            paddingVertical: 5,
            borderRadius: 30,
          }}>
          <MaterialCommunityIcons
            name={isCreate ? 'plus-box' : 'package-variant'}
            color={colors.primary}
            size={hp('1.9%')}
          />
        </View>
        <View style={{gap: 2, marginLeft: 6}}>
          <Text
            style={
              isCreate
                ? {marginBottom: 3, color: colors.text}
                : {
                    fontSize: FontSizes.small,
                    fontFamily: Fonts.OnestBold,
                    color: colors.text,
                  }
            }>
            {Title}
          </Text>

          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <MaterialCommunityIcons
                name="warehouse"
                color={colors.primary}
                size={hp('1.8%')}
              />
              <Text
                style={{
                  fontSize: FontSizes.small,
                  fontFamily: Fonts.OnestMedium,
                  color: colors.primary,
                  marginLeft: 3,
                }}>
                <Text>{inStock}</Text>
                <Text> in Stock</Text>
              </Text>
            </View>
            {!isCreate && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginLeft: 10,
                }}>
                <MaterialCommunityIcons
                  name="barcode"
                  color={colors.textSecondary}
                  size={hp('1.8%')}
                />
                <Text
                  style={{
                    fontSize: FontSizes.small,
                    fontFamily: Fonts.OnestBold,
                    color: colors.textSecondary,
                    paddingLeft: 3,
                  }}>
                  {Barcode}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: colors.primary + '30',
          paddingHorizontal: 10,
          paddingVertical: 5,
          borderRadius: 10,
        }}>
        <Text
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestBold,
            color: colors.primary,
          }}>
          {!Price || Price === undefined || Price === null || Price === 0
            ? '$0'
            : '$' + Price.toFixed(2)}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default ChoieItemCard;
