import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, Primary, Secondary} from '../../../constants/Color';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import DashBoardCart from '../../../components/Inventory/DashBoardCart';
import DashBoardItemCart from '../../../components/Inventory/DashBoardItemCart';
import {
  CommonActions,
  RouteProp,
  useFocusEffect,
} from '@react-navigation/native';
import {
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  Department,
  Employee,
  Get_Max_ID,
  Inventory,
  Invoice_Itemized,
  PurchaseOrder,
} from '../../../server/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Activate_Book,
  Game_Details,
} from '../../../Types/Lottery/Lottery_Types';
import AppLoader from '../../../components/Inventory/AppLoader';
import TrialPeriodModal from '../../SubScreens/Auth/TrialPeriodModal';
import {getOrganizationStatus} from '../../../services/apiService';
import {MaterialColors} from '../../../constants/MaterialColors';
import DashboardLotteryCard from '../../../components/Inventory/DashboardLotteryCard';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'Home'>;

const Dashboard: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [inventoryData, setInventoryData] = useState<Inventory[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory[]>([]);
  const [lowStock, setLowStock] = useState<number>(0);
  const [outofStock, setOutofStock] = useState<number>(0);
  const [lotteryGames, setLotteryGames] = useState<number>(0);
  const [missingTicket, setMissingTicket] = useState<string>('');
  const [lotteryGamesTotal, setLotteryGamesTotal] = useState<number>(0);
  const [notAssignVendor, setNotAssignVendor] = useState<number>(0);
  const [openedPurchase, setOpenedPurchase] = useState<number>(0);
  const [totalLotteryItemsSales, setTotalLotteryItemsSales] =
    useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [lastSold, setLastSold] = useState<Invoice_Itemized[]>([]);
  const [totalLotterySale, setTotalLotterySale] = useState<Invoice_Itemized[]>(
    [],
  );
  const [totalLotterySaleFil, setTotalLotterySaleFil] = useState<
    Invoice_Itemized[]
  >([]);
  const [greeting, setGreeting] = useState<string>('');
  const [currentDateTime, setCurrentDateTime] = useState<string>('');
  const [employee, setEmployee] = useState<Employee>();
  const [purchaseOrd, setPurchaseOrd] = useState<PurchaseOrder[]>([]);

  const [reorderlow, setReorderlow] = useState<Get_Max_ID[]>([]);
  const [reorderoutofstock, setReorderoutofstock] = useState<Get_Max_ID[]>([]);
  const [status, setStatus] = useState<boolean>(false);
  const [lotterGame, setLotterGame] = useState<Game_Details[]>([]);
  const [lotterGameFil, setLotterGameFil] = useState<Game_Details[]>([]);

  const [allGames, setAllGames] = useState<Activate_Book[]>([]);
  const [appLoader, setAppLoader] = useState<boolean>(false);

  const [isModalVisible, setModalVisible] = useState(false);
  const [trialPeriod, setTrialPeriod] = useState('');
  const [isActivated, setIsActivated] = useState(false);
  const [userRole, setUserRole] = useState<string>('');
  const [userName, setUserName] = useState<string>('');
  const [organizationStatus, setOrganizationStatus] = useState<string>('');

  useEffect(() => {
    checkServerConnection();
  }, []);

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      invNoPage();
      getInitDept();
      getLastSoldItems();
      fetchUserRole();
      getOrgStatus();
      const checkActivationStatus = async () => {
        try {
          const userData = await AsyncStorage.getItem('userData');
          if (userData) {
            const parsedUserData = JSON.parse(userData);
            if (parsedUserData.status === 'activated') {
              setIsActivated(true);
            } else if (parsedUserData.status === 'requested') {
              setIsActivated(false);
            } else if (parsedUserData.status === 'approved') {
              setIsActivated(false);
            } else if (parsedUserData.status === null) {
              setIsActivated(false);
            }
          }
        } catch (error) {
          console.error('Error checking activation status:', error);
        }
      };

      checkActivationStatus();
    }
  };
  const OnRefresh = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      invNoPage();
      getInitDept();
      getLastSoldItems();
      getShiftStatus();
      fetchUserRole();
      getOrgStatus();
    }
  };

  useEffect(() => {
    const currentTime = new Date();
    const hours = currentTime.getHours();

    // Set the greeting based on the time of day
    if (hours < 12) {
      setGreeting('Good Morning');
    } else if (hours < 18) {
      setGreeting('Good Afternoon');
    } else {
      setGreeting('Good Evening');
    }

    // Format the date as "Thursday 9th Jan 2025"
    const daysOfWeek = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const day = currentTime.getDate();
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const dayOfWeek = daysOfWeek[currentTime.getDay()];
    const month = monthNames[currentTime.getMonth()];
    const year = currentTime.getFullYear();

    const daySuffix = (d: number) => {
      if (d === 1 || d === 21 || d === 31) return 'st';
      if (d === 2 || d === 22) return 'nd';
      if (d === 3 || d === 23) return 'rd';
      return 'th';
    };

    setCurrentDateTime(`${dayOfWeek} ${day}${daySuffix(day)} ${month} ${year}`);
  }, []);
  const getInitDept = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
      false,
    );
  };

  useFocusEffect(
    useCallback(() => {
      setTimeout(() => {
        getShiftStatus();
        getOrgStatus();
        OnRefresh();
      }, 1500);
    }, []),
  );

  const handleModalClose = () => {
    setModalVisible(false);
  };

  const getOrgStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (!userData) return;

      const parsedUserData = JSON.parse(userData);
      let organizationId;

      // Get organization ID based on user role
      if (parsedUserData.role === 'owner') {
        organizationId = parsedUserData.id;
      } else if (parsedUserData.Organization_ID) {
        organizationId = parsedUserData.Organization_ID;
      }

      if (!organizationId) return;

      // Call the API to get organization status first
      const responseData = await getOrganizationStatus(organizationId);

      // Check trial end date
      let isTrialExpired = false;
      if (parsedUserData.trialEndDate) {
        const trialEndDate = new Date(parsedUserData.trialEndDate);
        const currentDate = new Date();
        isTrialExpired = currentDate > trialEndDate;
      }

      if (responseData.success) {
        const orgStatus = responseData.data.status;

        // Update local state with the status
        setOrganizationStatus(orgStatus);

        // Store the latest status in AsyncStorage only if it's not null/undefined
        if (orgStatus !== null && orgStatus !== undefined) {
          await AsyncStorage.setItem('organizationStatus', orgStatus);
        } else {
          // Remove the key if status is null/undefined
          await AsyncStorage.removeItem('organizationStatus');
        }

        if (orgStatus === 'revoked') {
          Alert.alert('Disconnected', 'Organization Revoked', [
            {
              text: 'OK',
              onPress: async () => {
                // Clear all storage data
                await AsyncStorage.multiRemove([
                  'userToken',
                  'userData',
                  'organizationData',
                  'SWIPEID',
                  'EMPLOYEE',
                  'IsShiftStarted',
                  'organizationStatus',
                ]);

                // Navigate to login screen
                navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [{name: 'LoginScreen'}],
                  }),
                );
              },
            },
          ]);
          return;
        } else if (orgStatus === 'activated') {
          setIsActivated(true);
          // If organization is activated, don't show trial expired message
          // The activated status overrides trial expiration
        } else if (
          orgStatus === 'requested' ||
          orgStatus === 'approved' ||
          orgStatus === null
        ) {
          setIsActivated(false);

          // Only show trial expired message if organization is NOT activated
          if (isTrialExpired) {
            Alert.alert(
              'Trial Expired',
              'Your trial period has ended. Please contact administrator for activation.',
              [
                {
                  text: 'OK',
                  onPress: async () => {
                    await AsyncStorage.multiRemove([
                      'userToken',
                      'userData',
                      'organizationData',
                      'SWIPEID',
                      'EMPLOYEE',
                      'IsShiftStarted',
                      'organizationStatus',
                    ]);

                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [{name: 'LoginScreen'}],
                      }),
                    );
                  },
                },
              ],
            );
            return;
          }
        }
      } else {
        console.error('Failed to get organization status');

        // Only check trial expiration if organization status check fails
        // and we don't have a cached activated status
        const cachedOrgStatus = await AsyncStorage.getItem(
          'organizationStatus',
        );
        if (cachedOrgStatus !== 'activated' && isTrialExpired) {
          Alert.alert(
            'Trial Expired',
            'Your trial period has ended. Please contact administrator for activation.',
            [
              {
                text: 'OK',
                onPress: async () => {
                  await AsyncStorage.multiRemove([
                    'userToken',
                    'userData',
                    'organizationData',
                    'SWIPEID',
                    'EMPLOYEE',
                    'IsShiftStarted',
                    'organizationStatus',
                  ]);

                  navigation.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [{name: 'LoginScreen'}],
                    }),
                  );
                },
              },
            ],
          );
          return;
        }
      }
    } catch (error: any) {
      console.error('Error checking organization status:', error);
      // Check if error is 404 (organization not found)
      if (error?.response?.status === 404 || error?.status === 404) {
        Alert.alert('Disconnected', 'Organization Not Found', [
          {
            text: 'OK',
            onPress: async () => {
              // Clear all storage data
              await AsyncStorage.multiRemove([
                'userToken',
                'userData',
                'organizationData',
                'SWIPEID',
                'EMPLOYEE',
                'IsShiftStarted',
                'organizationStatus',
              ]);

              // Navigate to login screen
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{name: 'LoginScreen'}],
                }),
              );
            },
          },
        ]);
      }
    }
  };

  const getShiftStatus = async () => {
    await AsyncStorage.getItem('IsShiftStarted')
      .then(value => {
        const isShiftStarted = JSON.parse(value);
        if (isShiftStarted) {
          setStatus(isShiftStarted);
        } else {
          setStatus(false);
        }
      })
      .catch(error => console.error('Failed to fetch data', error));
    setAppLoader(false);
  };

  const getLastSoldItems = async () => {
    GetAllItems<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getLatestSoldItems',
      setLastSold,
      setLoading,
      false,
    );

    GetAllItems<Get_Max_ID[]>(
      (await getInventoryPort()).toString(),
      '/getReorderCount',
      setReorderlow,
      setLoading,
      false,
    );

    GetAllItems<Get_Max_ID[]>(
      (await getInventoryPort()).toString(),
      '/getLowStockCount',
      setReorderoutofstock,
      setLoading,
      false,
    );
  };

  const invNoPage = async () => {
    setAppLoader(true);
    const data = await GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/inventorynopg',
      setInventoryData,
      setFilteredData,
      setLoading,
      false,
    );

    try {
      const lottery =
        (await GetAllItemsWithFilter(
          (await getLotteryPort()).toString(),
          '/GetAllGames',
          setLotterGame,
          setLotterGameFil,
          setLoading,
          false,
        )) || [];

      const lotteryTotalSales =
        (await GetAllItemsWithFilter(
          (await getInventoryPort()).toString(),
          '/getTotalLotterySale',
          setTotalLotterySale,
          setTotalLotterySaleFil,
          setLoading,
          false,
        )) || [];

      GetAllItems<Activate_Book[]>(
        (await getLotteryPort()).toString(),
        '/GetAllActiveBooks',
        setAllGames,
        setLoading,
      );

      const totalPriceLotterySale = lotteryTotalSales.reduce((acc, item) => {
        if (item.Quantity !== undefined && item.PricePer !== undefined) {
          return acc + item.Quantity * item.PricePer;
        }
        return acc;
      }, 0);
      setTotalLotteryItemsSales(totalPriceLotterySale);

      const now = new Date();
      const past24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const totalMissings = lottery.reduce((acc, item) => {
        const itemDate = new Date(item.Date);
        if (
          item.Missing_Ticket !== undefined &&
          item.Missing_Ticket > 0 &&
          itemDate >= past24Hours
        ) {
          return acc + item.Missing_Ticket;
        }
        return acc;
      }, 0);

      setMissingTicket(totalMissings);

      const filteredData = data.filter(
        item => item.In_Stock > 0 && item.In_Stock <= item.Reorder_Level,
      );

      const Dep_ID = await AsyncStorage.getItem('LOTTERY_DEP_ID');
      const filterLotteryDepart = data.filter(
        item => item.Dept_ID === Dep_ID?.toString(),
      );
      setLotteryGames(filterLotteryDepart.length);

      const totalPriceLottery = data.reduce((acc, item) => {
        if (
          item.Price !== undefined &&
          item.In_Stock !== undefined &&
          item.Dept_ID === Dep_ID
        ) {
          return acc + item.Price * item.In_Stock;
        }
        return acc;
      }, 0);

      const TotalLotValue = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getLotteryTotals/:Dept_ID',
        {Dept_ID: Dep_ID},
      );

      setLotteryGamesTotal(
        !TotalLotValue[0]?.TotalPrice ? 0 : TotalLotValue[0]?.TotalPrice,
      );

      const filteredDataOutofStock = data.filter(item => item.In_Stock <= 0);
      setOutofStock(filteredDataOutofStock.length);
      setLowStock(filteredData.length);

      const filteredDataVendor = data.filter(
        item =>
          item.Vendor_Number === '' ||
          item.Vendor_Number === null ||
          item.Vendor_Number === undefined,
      );
      setNotAssignVendor(filteredDataVendor.length);
      const employeeData = await AsyncStorage.getItem('EMPLOYEE');
      const parsedEmployee = JSON.parse(employeeData);
      setEmployee(parsedEmployee);

      const openPo = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpurchaseorder/:POType',
        setPurchaseOrd,
        {
          POType: 0,
        },
        false,
      );

      const OpenOnly = openPo.filter(item => item.Status === 'O');
      setPurchaseOrd(OpenOnly);

      setOpenedPurchase(OpenOnly?.length);
    } catch (error) {
      console.log(error);
    }
  };

  const totalPrice = inventoryData.reduce((acc, item) => {
    if (item.Price !== undefined && item.In_Stock !== undefined) {
      return acc + item.Price * item.In_Stock;
    }
    return acc;
  }, 0);

  const totalOpenPO = purchaseOrd.reduce((acc, item) => {
    if (item.Total_Cost !== undefined) {
      return acc + item.Total_Cost;
    }
    return acc;
  }, 0);

  const handleCheckTrialPeriod = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsedUserData = JSON.parse(userData);

        const trialEndDate = new Date(parsedUserData.trialEndDate);
        const currentDate = new Date();
        const remainingDays = Math.ceil(
          (trialEndDate.getTime() - currentDate.getTime()) /
            (1000 * 60 * 60 * 24),
        );
        setTrialPeriod(remainingDays.toString());
        // setIsActivated(true);
        setModalVisible(true);
      }
    } catch (error) {
      console.error('Error fetching trial period:', error);
    }
  };

  const handleActivate = () => {
    setIsActivated(true);
  };

  const fetchUserRole = async () => {
    const userData = await AsyncStorage.getItem('userData');

    if (userData) {
      const parsedUserData = JSON.parse(userData);

      setUserRole(parsedUserData.role);
      setUserName(parsedUserData.name || parsedUserData.Emp_Name);
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
      paddingTop: 6,
    },
    headerTitle: {
      fontSize: FontSizes.xLarge,
      fontFamily: Fonts.OnestBold,
      marginBottom: 2,
    },
    headerDate: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestRegular,
    },
    statusLabel: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
    },
    statusText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: '#FFFFFF',
      textTransform: 'uppercase',
    },
    sectionTitle: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      marginVertical: 16,
      marginHorizontal: 4,
    },
    viewAllText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    headerButton: {
      padding: 6,
      borderRadius: 6,
      ...Platform.select({
        ios: {
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    statusCard: {
      borderRadius: 12,
      padding: 10,
      marginVertical: 8,
      marginHorizontal: 8,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      ...Platform.select({
        ios: {
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 20,
    },
    gridContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16,
    },
    recentSales: {
      borderRadius: 12,
      padding: 16,
      marginTop: 16,
      ...Platform.select({
        ios: {
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 4,
        },
      }),
    },
    saleItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 12,
      borderBottomWidth: 1,
    },
    viewAllButton: {
      paddingVertical: 6,
      paddingHorizontal: 12,
      borderRadius: 20,
    },
  });

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      {/* Header Section */}
      <View style={[styles.header, {backgroundColor: colors.background}]}>
        <View>
          <Text style={[styles.headerTitle, {color: colors.text}]}>
            Hello, {userName || 'User'}!
          </Text>
          <Text style={[styles.headerDate, {color: colors.textSecondary}]}>
            {currentDateTime}
          </Text>
        </View>
        <View style={styles.headerActions}>
          {!isActivated && userRole === 'owner' && (
            <TouchableOpacity
              style={[
                styles.headerButton,
                {backgroundColor: colors.surface, shadowColor: colors.shadow},
              ]}
              onPress={handleCheckTrialPeriod}>
              <MaterialCommunityIcons
                name="calendar-clock"
                color={colors.primary}
                size={20}
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[
              styles.headerButton,
              {backgroundColor: colors.surface, shadowColor: colors.shadow},
            ]}
            onPress={() => {
              setAppLoader(true);
              setTimeout(OnRefresh, 1200);
            }}>
            <MaterialCommunityIcons
              name="sync"
              color={colors.primary}
              size={20}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.headerButton,
              {backgroundColor: colors.surface, shadowColor: colors.shadow},
            ]}
            onPress={() => navigation.navigate('Settings')}>
            <FontAwesome5 name="cog" color={colors.primary} size={18} />
          </TouchableOpacity>
        </View>
      </View>

      <View
        style={{
          height: hp('100%'),
        }}>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 10,
            marginHorizontal: 4,
          }}>
          <DashBoardCart
            Name="TOTAL ITEMS"
            Value={Number(inventoryData.length) || 0}
            Background={colors.card}
            Description="TOTAL VALUE"
            DescValue={'$' + Number(totalPrice.toFixed(2))}
            IconName="boxes"
            IconType="FontAwesome5"
            OnPress={() => navigation.navigate('TotalItems', {Type: 1})}
          />
          <DashBoardCart
            Name="OUT OF STOCK"
            Value={outofStock || 0}
            Background={colors.surface}
            Description="RE-ORDERED"
            DescValue={reorderoutofstock[0]?.MaxValue}
            IconName="alert-circle-outline"
            IconType="MaterialCommunityIcons"
            OnPress={() => navigation.navigate('TotalItems', {Type: 2})}
          />
          <DashBoardCart
            Name="LOW STOCK ITEMS"
            Value={lowStock || 0}
            Background={colors.surface}
            Description="RE-ORDERED"
            DescValue={reorderlow[0]?.MaxValue}
            IconName="trending-down"
            IconType="MaterialCommunityIcons"
            OnPress={() => navigation.navigate('TotalItems', {Type: 3})}
          />
        </View>

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            marginTop: 8,
            marginHorizontal: 4,
          }}>
          <DashBoardCart
            Name="OPEN PO"
            Value={openedPurchase || 0}
            Background={colors.surface}
            Description="TOTAL VALUE"
            DescValue={'$' + Number(totalOpenPO.toFixed(2))}
            IconName="file-invoice-dollar"
            IconType="FontAwesome5"
            OnPress={() => navigation.navigate('PurchaseOrder', {ItemData: 0})}
          />
          <DashBoardCart
            Name="MISSING VENDOR"
            Value={notAssignVendor || 0}
            Background={colors.surface}
            IconName="user-slash"
            IconType="FontAwesome5"
            OnPress={() => navigation.navigate('TotalItems', {Type: 4})}
          />
          <DashBoardCart
            Name="DEPARTMENTS"
            Value={departments.length || 0}
            Background={colors.surface}
            IconName="building"
            IconType="FontAwesome5"
            OnPress={() => navigation.navigate('TotalDeparments')}
          />
        </View>

        <View
          style={[
            styles.statusCard,
            {backgroundColor: colors.surface, shadowColor: colors.shadow},
          ]}>
          <Text style={[styles.statusLabel, {color: colors.text}]}>
            Lottery Shift Status:
          </Text>

          <View
            style={[
              styles.statusBadge,
              status
                ? {backgroundColor: colors.success}
                : {backgroundColor: colors.error},
            ]}>
            <Text style={styles.statusText}>{status ? 'open' : 'closed'}</Text>
          </View>
        </View>

        <ScrollView showsVerticalScrollIndicator={false} style={{}}>
          <View
            style={{
              flexDirection: 'column',
              gap: 12,
              justifyContent: 'space-between',
              marginTop: hp('1%'),
            }}>
            <DashboardLotteryCard
              Name="ACTIVE BOOKS"
              Value={allGames.length || 0}
              Background={colors.surface}
              Description="TOTAL VALUE"
              DescValue={lotteryGamesTotal}
              IconName="book-open"
              IconType="MaterialCommunityIcons"
              OnPress={() => navigation.navigate('ActivaBooks')}
            />
            <DashboardLotteryCard
              Name="MISSING TICKETS"
              Value={Number(missingTicket) > 0 ? '-' + missingTicket : 0}
              Background={colors.surface}
              IconName="ticket-outline"
              IconType="MaterialCommunityIcons"
              OnPress={() => navigation.navigate('ToatalMissings')}
            />
            <DashboardLotteryCard
              Name="LOTTERY SALES"
              Value={totalLotterySale.length || 0}
              Background={colors.surface}
              Description="TOTAL SALES"
              DescValue={totalLotteryItemsSales}
              IconName="cash-multiple"
              IconType="MaterialCommunityIcons"
              OnPress={() => navigation.navigate('TotalLotterySales')}
            />
          </View>

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginHorizontal: 8,
            }}>
            <Text style={[styles.sectionTitle, {color: colors.text}]}>
              Recently Sold Items
            </Text>
            <TouchableOpacity
              style={[
                styles.viewAllButton,
                {backgroundColor: colors.primary + '20'},
              ]}
              onPress={() => navigation.navigate('ViewAllTransaction')}>
              <Text style={[styles.viewAllText, {color: colors.text}]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>

          <View
            style={{
              borderRadius: 12,
              backgroundColor: colors.surface,
              marginHorizontal: 8,
            }}>
            {lastSold.map((last, index) => (
              <View
                key={`sold-item-${index}-${last.ItemNum || ''}`}
                style={{
                  padding: 4,
                }}>
                <DashBoardItemCart
                  Name={last.DiffItemName}
                  Department={last.Quantity}
                  Price={last.PricePer.toFixed(2)}
                  Date={last.ItemNum}
                />
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
      <TrialPeriodModal
        visible={isModalVisible}
        trialPeriod={trialPeriod}
        onClose={handleModalClose}
        onActivate={handleActivate}
      />
      <AppLoader modalVisible={appLoader} setModalVisible={setAppLoader} />
    </View>
  );
};

export default Dashboard;
