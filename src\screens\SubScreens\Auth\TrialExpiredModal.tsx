import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  activateOrganization,
  requestActivationKey,
} from '../../../services/apiService';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

interface TrialExpiredModalProps {
  visible: boolean;
  onClose: () => void;
  onActivate: () => void;
  userEmail?: string;
}

const TrialExpiredModal: React.FC<TrialExpiredModalProps> = ({
  visible,
  onClose,
  onActivate,
  userEmail,
}) => {
  const [activationKey, setActivationKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleRequestActivationKey = async () => {
    try {
      setIsLoading(true);
      const organizationData = await AsyncStorage.getItem('organizationData');

      if (organizationData) {
        const parsedOrgData = JSON.parse(organizationData);
        console.log(parsedOrgData, 'Parsed Organization Data');

        if (parsedOrgData.id) {
          const responseData = await requestActivationKey(
            parsedOrgData.id.toString(),
          );
          console.log(responseData, 'Request Key Response');

          if (responseData.success) {
            Alert.alert('Success', responseData.data.message);
          } else {
            Alert.alert(
              'Error',
              responseData.error || 'Failed to request activation key',
            );
          }
        }
      }
    } catch (error) {
      console.error('Error requesting activation key:', error);
      Alert.alert(
        'Error',
        'An error occurred while requesting the activation key',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleActivateOrganization = async () => {
    if (!activationKey.trim()) {
      Alert.alert('Error', 'Please enter an activation key');
      return;
    }

    try {
      setIsLoading(true);
      const userData = await AsyncStorage.getItem('userData');
      
      if (userData) {
        const parsedUserData = JSON.parse(userData);
        const responseData = await activateOrganization({
          email: userEmail || parsedUserData.email,
          activation_key: activationKey.trim(),
          subscription_type: 'monthly',
        });

        console.log(responseData, 'Activation Response');

        if (responseData.success) {
          Alert.alert('Success', responseData.data.message, [
            {
              text: 'OK',
              onPress: () => {
                onActivate();
                onClose();
                setActivationKey('');
              },
            },
          ]);
        } else {
          Alert.alert(
            'Error',
            responseData.error || 'Failed to activate organization',
          );
        }
      }
    } catch (error) {
      console.error('Error activating organization:', error);
      Alert.alert('Error', 'Invalid activation key. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      width: '90%',
      maxWidth: 400,
      backgroundColor: colors.surface,
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: isDark ? 0.3 : 0.25,
      shadowRadius: 8,
      elevation: 8,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingVertical: 16,
      paddingHorizontal: 20,
      backgroundColor: colors.error + '10',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.error,
    },
    closeButton: {
      padding: 6,
    },
    modalBody: {
      padding: 20,
      alignItems: 'center',
    },
    modalMessage: {
      fontSize: 15,
      color: colors.textSecondary,
      marginBottom: 24,
      textAlign: 'center',
      lineHeight: 22,
    },
    highlightText: {
      color: colors.error,
      fontWeight: '600',
    },
    inputContainer: {
      width: '100%',
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '500',
      marginBottom: 6,
    },
    textInputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.card,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    input: {
      width: '100%',
      fontSize: 16,
      paddingVertical: 14,
      paddingHorizontal: 16,
      color: colors.text,
    },
    button: {
      width: '100%',
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    secondaryButton: {
      backgroundColor: colors.secondary,
    },
    disabledButton: {
      opacity: 0.6,
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    secondaryButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header with close button */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Trial Period Expired</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon name="close" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalBody}>
            <Text style={styles.modalMessage}>
              Your <Text style={styles.highlightText}>trial period has ended</Text>.
              {'\n'}Please enter an activation key to continue using the application.
            </Text>

            {/* Input field with label */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Activation Key</Text>
              <View style={styles.textInputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your activation key"
                  value={activationKey}
                  onChangeText={setActivationKey}
                  placeholderTextColor={colors.placeholder}
                  autoCapitalize="none"
                />
              </View>
            </View>

            {/* Action buttons */}
            <TouchableOpacity
              style={[
                styles.button,
                styles.primaryButton,
                isLoading && styles.disabledButton,
              ]}
              onPress={handleActivateOrganization}
              disabled={isLoading}>
              <Text style={styles.buttonText}>Activate License</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.secondaryButton,
                isLoading && styles.disabledButton,
              ]}
              onPress={handleRequestActivationKey}
              disabled={isLoading}>
              <Text style={styles.secondaryButtonText}>
                Request Activation Key
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default TrialExpiredModal;
