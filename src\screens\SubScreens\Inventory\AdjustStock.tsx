import {View, Text, Alert, StyleSheet} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {MaterialColors} from '../../../constants/MaterialColors';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {Inventory, Inventory_In, Reason_Codes} from '../../../server/types';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {createItem, fetchSingleItem, updateItem} from '../../../server/service';
import {
  GetAllItems,
  getFormateDate,
  GetItemsParamsNoFilter,
  RefreshStockDetails,
  showAlert,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const validationSchema = Yup.object().shape({
  inStock: Yup.string().required('Stock is required'),
  action: Yup.string().required('Action is required'),
  reasonCodes: Yup.string().required('reasonCodes is required'),
});

type AdjustStockScreenRouteProp = RouteProp<any, 'AdjustStock'>;

const AdjustStock: React.FC<{
  route: AdjustStockScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [stockData, setStockData] = useState<Inventory[]>(
    route.params?.StockData,
  );
  const [reasonCodes, setReasonCodes] = useState<Reason_Codes[]>([]);
  const [stock, setStock] = useState<number>(
    route.params?.StockData[0]?.In_Stock || 0,
  );
  const [loading, setLoading] = useState<boolean>(false);

  const Action = [
    {label: 'Increase', value: 'Increase'},
    {label: 'Decrease', value: 'Decrease'},
  ];

  const getInitValue = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/reasoncodes/:Reason_Type',
      setReasonCodes,
      {Reason_Type: 5},
      false,
    );
  };

  const departmentOptions = reasonCodes.map(dept => ({
    label: dept.Reason_Code,
    value: dept.Reason_Code,
  }));

  useFocusEffect(
    useCallback(() => {
      getInitValue();
    }, []),
  );

  const updateAdjustedStock = async ({
    stockedData,
  }: {
    stockedData: Partial<Inventory>;
  }) => {
    const inventoryWithDefaults = applyDefaults(stockedData);

    try {
      const result = await updateItem(
        (await getInventoryPort()).toString(),
        '/updatebarcode',
        inventoryWithDefaults,
      );
    } catch (error) {
      console.log(error);
      Alert.alert('Error', 'There was an issue updating the barcode.');
    }
  };

  const create_InventoryIN = async ({
    inventoryAdjustData,
    isExit,
  }: {
    inventoryAdjustData: Partial<Inventory_In>;
    isExit: boolean;
  }) => {
    const inventoryAdjustWithDefaults =
      applyDefaultsInventoryAdjust(inventoryAdjustData);

    try {
      const result = await createItem(
        (await getInventoryPort()).toString(),
        '/createinvetoryin',
        inventoryAdjustWithDefaults,
      );
      if (isExit) {
        navigation.goBack();
      } else {
        Alert.alert('Stock Adjusted');
      }
    } catch (error) {
      console.log(error);
      Alert.alert('Error', 'There was an issue updating the barcode.');
    }
  };

  const updataStock = async (adjustedStock: any, isExit: boolean) => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;
    let adjustedAction: number;

    const currentStock = Number(stockData[0]?.In_Stock);
    const adjustedInStock = Number(adjustedStock?.inStock);

    if (isNaN(currentStock) || isNaN(adjustedInStock)) {
      console.error(
        'Invalid stock data',
        stockData[0]?.In_Stock,
        adjustedStock?.inStock,
      );
      return;
    }

    if (adjustedStock?.action === 'Increase') {
      adjustedAction = currentStock + adjustedInStock;
    } else if (adjustedStock?.action === 'Decrease') {
      adjustedAction = currentStock - adjustedInStock;
    } else {
      console.error('Invalid action type', adjustedStock?.action);
      return;
    }

    const stockedData: Partial<Inventory> = {
      ItemNum: stockData[0]?.ItemNum,
      ItemName: stockData[0]?.ItemName,
      Dept_ID: stockData[0]?.Dept_ID,
      Cost: Number(stockData[0]?.Cost),
      Price: Number(stockData[0]?.Price),
      Retail_Price: Number(stockData[0]?.Retail_Price),
      In_Stock: adjustedAction,
      Reorder_Level: 2,
      Reorder_Quantity: 12,
      Tax_1: true,
      Date_Created: stockData[0].Date_Created,
      Last_Sold: stockData[0].Last_Sold,
    };
    updateAdjustedStock({stockedData});

    const inventoryAdjustData: Partial<Inventory_In> = {
      ItemNum: stockData[0]?.ItemNum,
      Store_ID: ValidStore,
      Quantity:
        adjustedStock?.action === 'Increase'
          ? adjustedInStock
          : '-' + adjustedInStock,
      DateTime: getFormateDate(Date()),
      Dirty: true,
      TransType: 'A',
      Description: adjustedStock?.reasonCodes,
      Cashier_ID: ValideCashier,
    };

    const itemNum = stockData[0]?.ItemNum;

    // Check if itemNum is a valid string
    if (typeof itemNum !== 'string') {
      // Handle invalid itemNum, return early or show an error

      return;
    }

    create_InventoryIN({inventoryAdjustData, isExit})
      .then(async () => {
        if (adjustedStock?.action === 'Increase') {
          setStock(Number(stock) + Number(adjustedStock?.inStock));
        } else {
          setStock(Number(stock) - Number(adjustedStock?.inStock));
        }
        RefreshStockDetails(
          (await getInventoryPort()).toString(),
          itemNum,
          setStockData,
        );
      }) // Ensure RefreshStockDetails is called after async operations
      .catch(error => console.error('Error updating inventory:', error));
  };

  const ReasonCode_Validation = () => {
    showAlert('Reason Codes Not Found, Would You Like to Add New Reason Code?')
      .then(async result => {
        if (result) {
          navigation.navigate('ReasonCode');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const initialValues = {
    inStock: 0,
    action: '',
    reasonCodes: '',
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
      position: 'relative',
    },
    itemInfoCard: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('2%'),
      borderRadius: 12,
      marginTop: hp('1.5%'),
      marginBottom: hp('2.5%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    itemTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      marginBottom: hp('0.8%'),
    },
    stockCount: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    formContainer: {
      paddingVertical: hp('1%'),
    },
    formField: {
      marginBottom: hp('2%'),
    },
    bottomButtonContainer: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
  });
  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={values => {
        if (values.actionPressed === 'repeat') {
          updataStock(values, false);
        } else if (values.actionPressed === 'exit') {
          updataStock(values, true);
        }
        // updataStock(values);
        // updataStock(values);
        // updataStock(values);
      }}>
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        values,
        errors,
        touched,
      }) => (
        <View style={styles.container}>
          <Header NavName="Adjust Stock" />

          <View style={styles.itemInfoCard}>
            <Text style={styles.itemTitle}>
              Item: {stockData[0]?.ItemNum || 'Unknown Item'}
            </Text>
            <Text style={styles.stockCount}>In Stock: {stock}</Text>
          </View>

          <View style={styles.formContainer}>
            <View style={styles.formField}>
              <AppDropDown
                label="Action"
                options={Action}
                selectedValue={values.action}
                onSelect={value => setFieldValue('action', value)}
                error={errors.action}
                touched={touched.action}
                isRequired={true}
              />
            </View>

            <View style={styles.formField}>
              <AppTextInput
                Value={values.inStock}
                PlaceHolder="Enter Adjust Stock"
                Title="Adjust Stock"
                onChangeText={handleChange('inStock')}
                onBlur={handleBlur('inStock')}
                error={errors.inStock}
                touched={touched.inStock}
                isNumeric
                isRequired={true}
              />
            </View>

            <View style={styles.formField}>
              <AppDropDown
                label="Reason Codes"
                options={departmentOptions}
                selectedValue={values.reasonCodes}
                onSelect={value => setFieldValue('reasonCodes', value)}
                isAdd={true}
                onCreate={() => navigation.navigate('ReasonCode')}
                isRequired={true}
              />
            </View>
          </View>

          <View style={styles.bottomButtonContainer}>
            {false && (
              <AppButton
                Title="Save And Repeat"
                OnPress={() => {
                  setFieldValue('actionPressed', 'repeat');
                  handleSubmit();
                }}
              />
            )}
            <FAB
              label={'Save & Close'}
              position="bottomRight"
              onPress={() => {
                setFieldValue('actionPressed', 'exit');
                handleSubmit();
              }}
            />
          </View>
        </View>
      )}
    </Formik>
  );
};

export default AdjustStock;
