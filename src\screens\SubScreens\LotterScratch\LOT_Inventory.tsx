import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import TopTabNavigation from '../../../components/LotteryScratch/TopTabNaviagation'
import AppButton from '../../../components/Inventory/AppButton'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'

type NavProps = {
    navigation: NativeStackNavigationProp<any>; 
  };
  
  
  
  const LOT_Inventory: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>

      <Header NavName='Inventory'/>
      <View style={{width: '100%', height: '80%'}}>
        <TopTabNavigation />
      </View>

      <View style={{width: '100%', height: '20%', paddingHorizontal: 10, marginTop: 50}}>
        <AppButton Title='Add Book to Inventory' OnPress={() => navigation.navigate("AddInventory")}/>
      </View>

    </View>
  )
}

export default LOT_Inventory