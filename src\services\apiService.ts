import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'https://api.nurpos.com/api';

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('userToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// export const login = async (email: string, password: string) => { 
//   const response = await apiClient.post('/organization/login', { email, password });
//   console.log('Login response:', response.data);
  
//   return response.data;
// };
export const login = async (email: string, password: string) => {
  try {
    const response = await apiClient.post('/organization/login', { email, password });
    console.log('Login response:', response.data);
    return response.data;
  } catch (error) {
    if (error.response) {
      return { error: true, ...error.response.data };
    } else if (error.request) {
      return { error: true, message: 'No response received from server' };
    } else {
      return { error: true, message: error.message };
    }
  }
};

export const register = async (data: any) => {
    try {
  const response = await apiClient.post('/organization/register', data);
  return response.data;
    } catch (error) {
    if (error.response) {
      return { error: true, ...error.response.data };
    } else if (error.request) {
      return { error: true, message: 'No response received from server' };
    } else {
      return { error: true, message: error.message };
    }
  }
};

export const fetchUsers = async () => {
  const response = await apiClient.get('/users');
  return response.data;
};

export const addUser = async (userData: any) => {
  const response = await apiClient.post('/users', userData);
  return response.data;
};

export const requestActivationKey = async (organizationId: number) => {
  const response = await apiClient.post(`/organization/${organizationId}/request-key`);
  return response.data;
};

export const activateOrganization = async (data: any) => {
  const response = await apiClient.post('/organization/activate', data);
  return response.data;
};

export const getOrganizationStatus = async (id: any) => {
  const response = await apiClient.get(`/organization/${id}/status`);
  return response.data;
};

export const requestPasswordReset = async (email: string) => {
  const response = await apiClient.post('/organization/request-password-reset', { email });
  return response.data;
};

export const validateOTP = async (email: string, reset_code: string) => {
  const response = await apiClient.post('/organization/validate-reset-code', {
    email,
    reset_code
  });
  return response.data;
};

export const updatePassword = async (email: string, reset_code: string, new_password: string) => {
  const response = await apiClient.post('/organization/update-password', {
    email,
    reset_code,
    new_password
  });
  return response.data;
};
