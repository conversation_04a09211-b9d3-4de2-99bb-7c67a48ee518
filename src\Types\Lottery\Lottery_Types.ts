import { Inventory } from "../../server/types";

export interface Shift_Details {
    Shift_ID: string;
    Shift_Cashier_ID: string;
    Shift_Start_Time: Date | string;
    Shift_End_Time: Date | string | null;
    Shift_Status: boolean;
  }

  export interface Activate_Book {
    Game_ID: string;
    Book_No: string;
    Book_Created: Date | string;
    Book_Tickets: number;
    CreatedBy: string;
    Location: string | null;
    ItemNum: string;
    Open_Serial: string | null;
  }
   
  export interface App_User {
    Cashier_ID: string;
    Store_ID: string;
    Station_ID: string;
    Emp_Name: string;
    Role: string;
    Password: string;
    Organization_ID: string;
    Status: boolean;
  }
  

  export interface Game_Details {
    Game_ID: string | null;
    Open_Book: string | null;
    Close_Book: string | null;
    Stock_Level_Open: number | null;
    Stock_Level_Close: number | null;
    Shift_Open_Serial: string | null;
    Shift_Close_Serial: string | null;
    Missing_Ticket: number | null;
    Location: string | null;
    Shift_ID: string;
    Date?: Date | string
    Reset?: string | null
    Shift_Start_Skip?: string | null
    Shift_End_Skip?: string | null
  }

  export interface Send_Email {
    ShiftGames: Game_Details[];
    ShiftDetails:Shift_Details
  }
  
  export interface Reorder_Status {
    PO_Number: number;
    IsReorderd: boolean;
  }

  export interface Remove_Book {
    Store_ID: string;
    Remove_Stock_Level: number | undefined;
    DateTime: Date | string;
    EndSerial: string;
  }
  
