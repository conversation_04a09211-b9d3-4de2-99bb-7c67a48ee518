import {
  View,
  Text,
  Alert,
  TouchableOpacity,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DataList from '../../../components/Inventory/AppList';
import {Unit_Types} from '../../../server/types';
import {
  GetAllItems,
  GetCommonLatestID,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {createItem, deleteItem} from '../../../server/service';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const UnitTypes = () => {
  const [addUnitType, setAddUnitType] = useState<string>('');
  const [unitTypes, setUnitTypes] = useState<Unit_Types[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [keyboardVisible, setKeyboardVisible] = useState<boolean>(false);
  useEffect(() => {
    getAllUnitTypes();
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const getAllUnitTypes = async () => {
    GetAllItems<Unit_Types[]>(
      (await getLotteryPort()).toString(),
      '/GetAllUnits',
      setUnitTypes,
      setLoading,
    );
  };

  const addUnitTypes = async () => {
    if (!addUnitType || addUnitType === '') {
      Alert.alert('Please Enter the UnitType');
      return;
    }
    const checkExists = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetUnitDetails/:Unit_Type',
      {Unit_Type: addUnitType},
    );

    if (Array.isArray(checkExists) && checkExists.length === 0) {
      const UnitID = await GetCommonLatestID(
        (await getLotteryPort()).toString(),
        '/GetUnitTypeID',
      );

      const AddUnitTypes: Unit_Types = {
        ID: UnitID?.toString(),
        Unit_Type: addUnitType,
      };

      const result = await createItem(
        (await getLotteryPort()).toString(),
        '/createunittype',
        AddUnitTypes,
      );

      if (result) {
        GetAllItems<Unit_Types[]>(
          (await getLotteryPort()).toString(),
          '/GetAllUnits',
          setUnitTypes,
          setLoading,
        );
        setAddUnitType('');
        Alert.alert('Unit Type Added');
      }
    } else {
      Alert.alert('Unit Type Already Exists');
    }
  };

  const removeUnitTypes = async (RemoveUnitTypes: Unit_Types) => {
    showAlert('Are you sure you want to delete Unit Type?')
      .then(async result => {
        if (result) {
          const result = await deleteItem(
            (await getLotteryPort()).toString(),
            '/DeleteUnitType/:ID',
            {ID: RemoveUnitTypes.ID},
          );
          if (result) {
            const updatedArraySel = unitTypes.filter(
              item => item.ID !== RemoveUnitTypes.ID,
            );
            setUnitTypes(updatedArraySel);
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };
  const renderItemSkus = ({item}: {item: Unit_Types}) => (
    <View style={styles.unitTypeCard}>
      <Text style={styles.unitTypeText}>{item.Unit_Type}</Text>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => removeUnitTypes(item)}>
        <MaterialCommunityIcons
          name="delete-outline"
          color="#fff" // Keep white since it's on the error background
          size={16}
        />
      </TouchableOpacity>
    </View>
  );

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
    },
    listContainer: {
      flex: 1,
    },
    unitTypeCard: {
      backgroundColor: colors.card,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.8%'),
      marginBottom: hp('1.2%'),
      borderRadius: 12,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    unitTypeText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    deleteButton: {
      backgroundColor: colors.error,
      padding: 8,
      borderRadius: 8,
    },
    inputContainer: {
      backgroundColor: colors.surface,
      padding: hp('2%'),
      borderRadius: 12,
      marginTop: hp('1%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
  });
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 40}>
      <SafeAreaView style={styles.safeArea}>
        <View
          style={[
            styles.listContainer,
            {height: keyboardVisible ? hp('30%') : hp('60%')},
          ]}>
          <Header NavName="Unit Types" />
          <DataList
            data={unitTypes}
            renderItem={renderItemSkus}
            loading={loading}
          />
        </View>

        <View style={styles.inputContainer}>
          <AppTextInput
            PlaceHolder="Enter Unit Type"
            Title="Unit Type"
            Value={addUnitType}
            onChangeText={text => setAddUnitType(text)}
          />

          <AppButton Title={'Add Unit Type'} OnPress={() => addUnitTypes()} />
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default UnitTypes;
