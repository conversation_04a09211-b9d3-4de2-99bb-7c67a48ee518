import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Platform} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6';
import Octicons from 'react-native-vector-icons/Octicons';
import {Fonts, FontSizes} from '../../styles/fonts';
import {truncateNumber} from '../../utils/PublicHelper';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';

interface Props {
  Name: string;
  Value: number | string;
  Background: string;
  Description?: string;
  DescValue?: number;
  IconName: string;
  IconType:
    | 'FontAwesome5'
    | 'FontAwesome6'
    | 'MaterialIcons'
    | 'MaterialCommunityIcons'
    | 'Octicons';
  OnPress?: () => void;
}

const DashboardLotteryCard = ({
  Name,
  Value,
  Background,
  Description,
  DescValue = 0,
  IconName,
  IconType,
  OnPress,
}: Props) => {
  const renderIcon = () => {
    const iconProps = {
      name: IconName,
      color: colors.primary,
      size: hp('2%'),
    };

    switch (IconType) {
      case 'FontAwesome5':
        return <FontAwesome5 {...iconProps} />;
      case 'FontAwesome6':
        return <FontAwesome6 {...iconProps} />;
      case 'MaterialIcons':
        return <MaterialIcons {...iconProps} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons {...iconProps} />;
      case 'Octicons':
        return <Octicons {...iconProps} />;
      default:
        return null;
    }
  };

  const colors = useThemeColors();

  const styles = StyleSheet.create({
    card: {
      borderRadius: 16,
      paddingVertical: 8,
      paddingHorizontal: 12,
      justifyContent: 'space-between',
      marginHorizontal: 8,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    topContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    },
    iconContainer: {
      width: 30,
      height: 30,
      borderRadius: 20,
      backgroundColor: colors.primary + '20',
      justifyContent: 'center',
      alignItems: 'center',
    },
    value: {
      fontSize: 14,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    contentContainer: {
      alignItems: 'flex-start',
      marginTop: 8,
      flexDirection: 'row',
    },
    name: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginRight: 4,
      marginLeft: 8,
    },
    description: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestRegular,
      color: colors.textSecondary,
    },
  });

  return (
    <TouchableOpacity
      style={[styles.card, {backgroundColor: Background}]}
      onPress={OnPress}>
      <View style={styles.topContainer}>
        <View style={styles.iconContainer}>{renderIcon()}</View>
        <Text style={styles.name}>{Name}</Text>
        <View style={{flex: 1}} />
        <Text style={styles.value}>{Value}</Text>
      </View>
      {/* <View style={styles.contentContainer}>
        {Description && (
          <Text style={styles.description}>
            {Description}: {truncateNumber(DescValue)}
          </Text>
        )}
      </View> */}
    </TouchableOpacity>
  );
};

export default DashboardLotteryCard;
