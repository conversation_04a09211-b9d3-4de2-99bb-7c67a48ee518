import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {
  formatNumber,
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
} from '../../utils/PublicHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Inventory} from '../../server/types';
import {getLotteryPort} from '../../server/InstanceTypes';
import {Activate_Book} from '../../Types/Lottery/Lottery_Types';
import {useFocusEffect} from '@react-navigation/native';
import {MaterialColors} from '../../constants/MaterialColors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface Props {
  Onpress?: () => void;
  OnSerialChange?: (serial: string, Item: Inventory) => void;
  Item?: Inventory;
  OnpressIN?: () => void;
  ResetStock?: () => void;
  Skip?: () => void;
  Stock?: number;
  Cost?: number;
  IsActive?: boolean;
  IsCompleted?: boolean;
  IsLocation?: boolean;
  inputRef?: React.RefObject<TextInput>;
  isBadItem?: boolean;
  status?: boolean;
  isGoodItem?: boolean;
  isShift?: boolean;
  LotteryBook?: string;
  MissingValue?: string;
  ResetScanned?: string;
  isLocation?: boolean;
  IsLastMissings?: boolean;
  ActiveBook?: string;
  onFocus?: () => void;
}
const TicketsDetails = ({
  Onpress,
  OnpressIN,
  ResetStock,
  Skip,
  OnSerialChange,
  Item,
  Stock,
  Cost,
  IsActive,
  IsCompleted,
  IsLocation = false,
  inputRef,
  status,
  isBadItem = false,
  isGoodItem = false,
  isShift = true,
  LotteryBook,
  isLocation,
  IsLastMissings = false,
  ActiveBook,
  MissingValue,
  ResetScanned,
  onFocus,
}: Props) => {
  const [serialInput, setSerialInput] = useState('');
  const [serialEnd, setSerialEnd] = useState('');
  const [lotteryBook, setLotteryBook] = useState('');
  const [lotteryBookDif, setLotteryBookDif] = useState('');
  const [allGames, setAllGames] = useState<Activate_Book[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isClearing, setIsClearing] = useState(false);

  const getBorderColor = () => {
    if (isBadItem) return colors.error;
    if (isGoodItem) return colors.success;
    if (IsCompleted) return colors.success;
    if (IsActive) return colors.warning;
    return colors.border;
  };

  const getStatusIcon = () => {
    if (isBadItem) return 'alert-circle';
    if (isGoodItem || IsCompleted) return 'check-circle';
    if (IsActive) return 'clock-outline';
    return 'information-outline';
  };

  const getStatusColor = () => {
    if (isBadItem) return colors.error;
    if (isGoodItem || IsCompleted) return colors.success;
    if (IsActive) return colors.warning;
    return colors.textSecondary;
  };
  const getBookNumber = allGames.filter(
    allgame =>
      allgame.ItemNum === Item?.ItemNum && allgame.Location === Item?.Location,
  );
  const handleSerialChange = async (text: string) => {
    if (
      getBookNumber[0]?.Book_No === 'N/A' ||
      !getBookNumber[0]?.Book_No ||
      getBookNumber[0]?.Book_No === undefined ||
      getBookNumber[0]?.Book_No === null
    ) {
      Alert.alert('Please Activate the Book!');
      return;
    } else {
      const formattedBarcode = formatNumber(text);
      let endGameSerial = formattedBarcode.split('-');
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetShiftBookTickets/:ItemNum/:Location',
        {ItemNum: Item?.ItemNum, Location: Item?.Location},
      );

      if (endGameSerial[1] !== getBarcode[0]?.Book_No) {
        Alert.alert('Please Scan Valid Book!');
      } else {
        setSerialInput(formattedBarcode);
        const extractedSerial = formattedBarcode.split('-').pop() || '';
        setSerialEnd(extractedSerial);
        OnSerialChange(formattedBarcode, Item);

        const extractbook = formattedBarcode.substring(4, 10);
        const extractbookExists = lotteryBook?.substring(0, 6);

        if (extractbook !== extractbookExists) {
          if (status) {
            const getUpdated = formattedBarcode.substring(4, 14);
            setLotteryBookDif(getUpdated);
          } else {
            const extractbook = formattedBarcode.substring(4, 14);
            setLotteryBook(extractbook);
          }
        }
      }
    }
  };

  useEffect(() => {
    const loadAsync = async () => {
      const getShiftID = await AsyncStorage.getItem('Shift_ID');
      const getLottyBook = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        {Shift_ID: getShiftID, Location: Item?.Location},
      );

      if (
        (Array.isArray(getLottyBook) && getLottyBook.length === 0) ||
        !getLottyBook ||
        getLottyBook === undefined
      ) {
        setLotteryBook('N/A');
      } else {
        setLotteryBook(
          getLottyBook[0]?.Open_Book + '-' + getLottyBook[0]?.Shift_Open_Serial,
        );
      }
    };
    if (status === true) {
      if (Item?.Location) {
        setTimeout(() => {
          loadAsync();
        }, 1200);
      }
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      getInitDept();
    }, []),
  );

  const getInitDept = async () => {
    GetAllItems<Activate_Book[]>(
      (await getLotteryPort()).toString(),
      '/GetAllActiveBooks',
      setAllGames,
      setLoading,
    );
  };

  useEffect(() => {
    if (ResetScanned !== undefined) {
      if (ResetScanned === '') {
        // Set clearing flag and clear everything
        setIsClearing(true);
        setSerialInput('');
        setSerialEnd('');
        setLotteryBook(''); // Clear SSN
        setLotteryBookDif('');

        // Reset clearing flag after a short delay
        setTimeout(() => {
          setIsClearing(false);
        }, 100);
      } else if (ResetScanned) {
        setLotteryBook(ResetScanned);
      }
    }
  }, [ResetScanned]);

  useEffect(() => {
    const loadAsync = async () => {
      // Don't load if we're clearing
      if (isClearing) return;

      const getShiftID = await AsyncStorage.getItem('Shift_ID');
      const getLottyBook = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        {Shift_ID: getShiftID, Location: Item?.Location},
      );

      if (
        (Array.isArray(getLottyBook) && getLottyBook.length === 0) ||
        !getLottyBook ||
        getLottyBook === undefined
      ) {
        setLotteryBook('N/A');
      } else {
        setLotteryBook(
          getLottyBook[0]?.Open_Book + '-' + getLottyBook[0]?.Shift_Open_Serial,
        );
      }
    };

    if (status === true && !isClearing) {
      if (Item?.Location) {
        setTimeout(() => {
          loadAsync();
        }, 1200);
      }
    }
  }, [status, Item?.Location, isClearing]);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    cardContainer: {
      marginVertical: 4,
      width: '100%',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 10,
      borderWidth: 1.5,
      elevation: 1,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: 2,
    },
    statusIconContainer: {
      marginRight: 8,
      paddingTop: 1,
    },
    headerContainer: {
      flex: 1,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    activeBookTitle: {
      fontSize: FontSizes.small,
      fontWeight: '600',
      fontFamily: Fonts.OnestBold,
      marginLeft: 4,
    },
    gameInfoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      marginBottom: 4,
    },
    gameTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    gameTitle: {
      fontSize: FontSizes.small,
      fontWeight: '600',
      fontFamily: Fonts.OnestBold,
      marginLeft: 4,
    },
    hiddenInput: {
      borderWidth: 1,
      padding: 6,
      borderRadius: 8,
      width: wp('12%'),
      height: hp('2.2%'),
      opacity: 0,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    locationText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      marginLeft: 4,
    },
    skipButton: {
      paddingHorizontal: 10,
      paddingVertical: 4,
      alignItems: 'center',
      borderRadius: 6,
      flexDirection: 'row',
      elevation: 1,
    },
    skipButtonText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: '#FFFFFF',
      marginLeft: 2,
    },
    divider: {
      height: 1,
      marginVertical: 5,
      width: '100%',
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 6,
    },
    infoItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoLabel: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      marginLeft: 2,
    },
    infoValue: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
    },
    priceText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      marginLeft: 2,
    },
    footerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    },
    serialsContainer: {
      flex: 1,
    },
    actionsContainer: {
      alignItems: 'flex-end',
    },
    missingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    missingText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      marginLeft: 2,
    },
    serialInfoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 1,
    },
    serialLabel: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      paddingHorizontal: 2,
    },
    serialValue: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
    },
    resetButton: {
      borderRadius: 4,
      paddingVertical: 3,
      paddingHorizontal: 8,
      flexDirection: 'row',
      alignItems: 'center',
      elevation: 1,
    },
    resetButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      marginLeft: 2,
    },
  });

  // Remove automatic focusing - cards will only be focused when manually selected
  // The focus will be handled by the parent component when user touches the card

  return (
    <TouchableOpacity
      style={[
        styles.cardContainer,
        {
          borderColor: getBorderColor(),
          backgroundColor: colors.surface,
          shadowColor: colors.shadow,
        },
      ]}
      onPress={() => {
        if (onFocus) {
          onFocus(); // Call the manual selection handler
        }
        if (isShift ? Onpress : OnpressIN) {
          (isShift ? Onpress : OnpressIN)?.();
        }
      }}
      activeOpacity={0.7}>
      <View style={styles.cardContent}>
        <View style={styles.statusIconContainer}>
          <Icon name={getStatusIcon()} size={20} color={getStatusColor()} />
        </View>

        <View style={styles.headerContainer}>
          <View style={styles.titleRow}>
            <Icon name="book-open-variant" size={16} color={colors.primary} />
            <Text style={[styles.activeBookTitle, {color: colors.text}]}>
              Active Book: {getBookNumber[0]?.Book_No || 'N/A'}
            </Text>
          </View>

          <View style={styles.gameInfoRow}>
            <View style={styles.gameTitleContainer}>
              <Icon name="gamepad-variant" size={14} color={colors.primary} />
              <Text style={[styles.gameTitle, {color: colors.text}]}>
                {Item.ItemName.length > 11
                  ? Item.ItemName.substring(0, 15) + '...'
                  : Item.ItemName}
              </Text>
            </View>

            {isShift && (
              <TextInput
                ref={inputRef}
                showSoftInputOnFocus={false}
                editable={IsActive}
                style={[
                  styles.hiddenInput,
                  {
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                    color: colors.text,
                  },
                ]}
                placeholder="Serial #"
                placeholderTextColor={colors.placeholder}
                value={serialInput}
                onChangeText={handleSerialChange}
                onFocus={onFocus}
              />
            )}

            {isLocation && (
              <View style={styles.locationContainer}>
                <Icon
                  name="map-marker"
                  size={16}
                  color={colors.textSecondary}
                />
                <Text style={[styles.locationText, {color: colors.text}]}>
                  {Item?.Location === 'NOTASSIGNED' ? 'N/A' : Item?.Location}
                </Text>
              </View>
            )}

            {isShift && IsActive && (
              <TouchableOpacity
                style={[styles.skipButton, {backgroundColor: colors.primary}]}
                onPress={Skip}>
                <Icon name="skip-next" size={12} color="#FFFFFF" />
                <Text style={styles.skipButtonText}>SKIP</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={[styles.divider, {backgroundColor: colors.border}]} />

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Icon
                name={IsLocation ? 'map-marker' : 'ticket-confirmation'}
                size={14}
                color={colors.textSecondary}
              />
              <Text style={[styles.infoLabel, {color: colors.textSecondary}]}>
                {IsLocation ? 'Location: ' : 'Tickets: '}
              </Text>
              <Text style={[styles.infoValue, {color: colors.textSecondary}]}>
                {Stock}
              </Text>
            </View>

            <View style={styles.infoItem}>
              <Icon
                name="currency-usd"
                size={14}
                color={colors.textSecondary}
              />
              <Text
                style={[
                  styles.priceText,
                  {color: colors.textSecondary},
                ]}>{`${Cost?.toFixed(2)}`}</Text>
            </View>
          </View>

          <View style={styles.footerRow}>
            <View style={styles.serialsContainer}>
              {IsLastMissings && (
                <View style={styles.missingContainer}>
                  <Icon name="alert" size={12} color={colors.error} />
                  <Text style={[styles.missingText, {color: colors.error}]}>
                    Last Shift Missing: {MissingValue}
                  </Text>
                </View>
              )}

              {lotteryBook.length > 0 && (
                <View style={styles.serialInfoContainer}>
                  <Icon name="barcode" size={14} color={colors.warning} />
                  <Text style={[styles.serialLabel, {color: colors.warning}]}>
                    SSN#:
                  </Text>
                  <Text style={[styles.serialValue, {color: colors.text}]}>
                    {!lotteryBook ||
                    lotteryBook === '-null' ||
                    lotteryBook === '-000'
                      ? 'N/A'
                      : lotteryBook}
                  </Text>
                </View>
              )}

              {lotteryBookDif.length > 0 && (
                <View style={styles.serialInfoContainer}>
                  <Icon name="barcode" size={14} color={colors.warning} />
                  <Text style={[styles.serialLabel, {color: colors.warning}]}>
                    NBS#:
                  </Text>
                  <Text style={[styles.serialValue, {color: colors.text}]}>
                    {lotteryBookDif}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.actionsContainer}>
              {serialEnd && (
                <View style={styles.serialInfoContainer}>
                  {status && (
                    <Icon
                      name="barcode-scan"
                      size={14}
                      color={colors.primary}
                    />
                  )}
                  <Text style={[styles.serialLabel, {color: colors.warning}]}>
                    {status && 'CSN#:'}
                  </Text>
                  <Text style={[styles.serialValue, {color: colors.text}]}>
                    {status && serialEnd}
                  </Text>
                </View>
              )}

              {/* {IsLastMissings && IsActive && (
                <TouchableOpacity
                  style={[styles.resetButton, {backgroundColor: colors.error}]}
                  onPress={ResetStock}>
                  <Icon name="refresh" size={12} color="#FFFFFF" />
                  <Text style={styles.resetButtonText}>Reset</Text>
                </TouchableOpacity>
              )} */}
            </View>
          </View>
        </View>
        <Text
          style={{
            backgroundColor: 'red',
            paddingHorizontal: 10,
            textAlign: 'right',
            paddingVertical: 5,
            borderRadius: 10,
            fontSize: 18,
            fontWeight: 'bold',
            color: '#fff',
          }}>
          {Item?.Location === 'NOTASSIGNED' ? 'N/A' : Item?.Location}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default TicketsDetails;
