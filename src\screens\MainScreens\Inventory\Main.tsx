import { Alert, SafeAreaView, Text } from 'react-native'
import React, { useState } from 'react'
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Input from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import { fetchAllBarcode, fetchSingleBarcode } from '../../../server/service';

type MainProps = {
  navigation: NativeStackNavigationProp<any>; // Replace 'any' with your specific stack param list if available
};

const Main: React.FC<MainProps> = ({ navigation }) => {
  const [barcode, setBarcode] = useState(String);

  const loadBarcode = async () => {
    if (barcode.length === 0 || barcode.trim() === "") {
      Alert.alert("Enter Barcode please")
    }else{
      try {
        const details = await fetchSingleBarcode(barcode);
        if (!details || (Array.isArray(details) && details.length === 0) || (typeof details === 'object' && Object.keys(details).length === 0) || details === null || details === undefined) {
          Alert.alert("Item Not Found, Create New?")
      } else {
          navigation.navigate("Home")
      }
      } catch (err) {
        console.log(err);
      } 
    }
  }

  // return (
  //   <SafeAreaView style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
  //     <Input PlaceHolder='Search Barcode...' onChangeText={(text: string) => setBarcode(text)}/>
  //     <AppButton Title='Search Barcode' OnPress={loadBarcode}/>
  //   </SafeAreaView>
  // );

  return(
    <SafeAreaView style={{flex: 1, alignItems: 'center', justifyContent: 'center', width: "100%"}}>
        <Text>This is Home Screen</Text>
    </SafeAreaView>
  )
};

export default Main;