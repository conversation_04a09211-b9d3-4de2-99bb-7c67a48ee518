import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  activateOrganization,
  requestActivationKey,
} from '../../../services/apiService';
import {MaterialColors} from '../../../constants/MaterialColors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const TrialPeriodModal = ({visible, trialPeriod, onClose, onActivate}) => {
  const [activationKey, setActivationKey] = useState('');

  const handleRequestActivationKey = async () => {
    try {
      const organizationData = await AsyncStorage.getItem('organizationData');

      if (organizationData) {
        const parsedOrgData = JSON.parse(organizationData);
        console.log(parsedOrgData, 'Parsed  Data 1');

        if (parsedOrgData.id) {
          const responseData = await requestActivationKey(
            parsedOrgData.id.toString(),
          );
          console.log(responseData, 'Response Data');

          if (responseData.success) {
            Alert.alert('Success', responseData.data.message);
          } else {
            Alert.alert(
              'Error',
              responseData.error || 'Failed to request activation key',
            );
          }
        }
      }
    } catch (error) {
      console.error('Error requesting activation key:', error);
      Alert.alert(
        'Error',
        'An error occurred while requesting the activation key',
      );
    }
  };

  const handleActivateOrganization = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const parsedUserData = JSON.parse(userData);
        const responseData = await activateOrganization({
          email: parsedUserData.email,
          activation_key: activationKey.trim(),
          subscription_type: 'monthly',
        });

        console.log(responseData, 'Response Data');

        if (responseData.success) {
          Alert.alert('Success', responseData.data.message);
          onActivate(); // Call the onActivate function to hide the calendar icon
          onClose(); // Close the modal
        } else {
          Alert.alert(
            'Error',
            responseData.error || 'Failed to activate organization',
          );
        }
      }
    } catch (error) {
      console.error('Error activating organization:', error);
      Alert.alert('Error', 'Invalid activation key. Please try again.');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      width: '90%',
      maxWidth: 400,
      backgroundColor: colors.surface,
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: isDark ? 0.3 : 0.25,
      shadowRadius: 8,
      elevation: 8,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingVertical: 16,
      paddingHorizontal: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    closeButton: {
      padding: 6,
    },
    modalBody: {
      padding: 20,
      alignItems: 'center',
    },
    modalMessage: {
      fontSize: 15,
      color: colors.textSecondary,
      marginBottom: 24,
      textAlign: 'center',
      lineHeight: 22,
    },
    highlightText: {
      color: colors.primary,
      fontWeight: '600',
    },
    inputContainer: {
      width: '100%',
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '500',
      marginBottom: 6,
    },
    textInputContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.card,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    input: {
      width: '100%',
      fontSize: 16,
      paddingVertical: 14,
      paddingHorizontal: 16,
      color: colors.text,
    },
    button: {
      width: '100%',
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    secondaryButton: {
      backgroundColor: colors.secondary,
    },
    outlineButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.border,
      shadowOpacity: 0,
      elevation: 0,
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    secondaryButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    outlineButtonText: {
      color: colors.textSecondary,
      fontSize: 16,
      fontWeight: '500',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header with close button */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Trial Period</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon name="close" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalBody}>
            <Text style={styles.modalMessage}>
              You have{' '}
              <Text style={styles.highlightText}>{trialPeriod} days</Text>{' '}
              remaining in your trial period.
            </Text>

            {/* Input field with label */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Activation Key</Text>
              <View style={styles.textInputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your activation key"
                  value={activationKey}
                  onChangeText={setActivationKey}
                  placeholderTextColor={colors.placeholder}
                />
              </View>
            </View>

            {/* Action buttons */}
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={handleActivateOrganization}>
              <Text style={styles.buttonText}>Activate License</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={handleRequestActivationKey}>
              <Text style={styles.secondaryButtonText}>
                Request Activation Key
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.outlineButton]}
              onPress={onClose}>
              <Text style={styles.outlineButtonText}>Continue with Trial</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default TrialPeriodModal;
