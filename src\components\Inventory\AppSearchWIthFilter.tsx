import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  StyleSheet,
  Platform,
} from 'react-native';
import React from 'react';
import Search from './Search';
import FontAwesome5Icon from 'react-native-vector-icons/FontAwesome5';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {TextInput} from 'react-native';
import {MaterialColors} from '../../constants/MaterialColors';
import Animated, {useAnimatedStyle, withTiming} from 'react-native-reanimated';
import AppFocus from './AppFocus';
import SearchManual from './SearchManual';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

export interface Props {
  SearchValue?: string;
  OnSearch?: (text: string) => Promise<void>;
  isEnableFilter?: boolean;
  OnSearchSet?: () => void;
  Keyboardon?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  onToggleLookup?: (value: boolean) => void;
  OnSubmitEditing?: () => void;
  IsFilter?: boolean;
}

const AppSearchWIthFilter = ({
  SearchValue,
  OnSearch,
  isEnableFilter,
  OnSearchSet,
  Keyboardon,
  textInputRef,
  onToggleLookup,
  IsFilter = true,
  OnSubmitEditing,
}: Props) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: hp('1.2%'),
      paddingHorizontal: wp('1%'),
    },
    searchContainer: {
      flex: 1,
    },
    filterButton: {
      alignItems: 'center',
      justifyContent: 'center',
      width: hp('5.5%'),
      height: hp('5.5%'),
      borderRadius: 15,
      marginLeft: wp('2%'),
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 3,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    filterButtonActive: {
      backgroundColor: colors.primary,
    },
    filterButtonInactive: {
      backgroundColor: colors.surface,
      borderWidth: 0,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SearchManual
          PlaceHolder="Search Item Here...."
          onChangeText={OnSearch}
          Value={SearchValue}
          AutoFocus={true}
          isBarcode={true}
          Editable={true}
          textInputRef={textInputRef}
          keyboardON={Keyboardon}
          onToggleLookup={onToggleLookup}
          OnSubmitEditing={OnSubmitEditing}
          ClearInput={() => {
            if (textInputRef?.current) {
              textInputRef.current.clear();
            }
            if (OnSearch) {
              OnSearch('');
            }
          }}
        />
      </View>

      {IsFilter && (
        <TouchableOpacity
          style={[
            styles.filterButton,
            isEnableFilter
              ? styles.filterButtonActive
              : styles.filterButtonInactive,
          ]}
          activeOpacity={0.7}
          onPress={OnSearchSet}>
          <FontAwesome5Icon
            name="filter"
            color={isEnableFilter ? '#FFFFFF' : colors.textSecondary}
            size={hp('2.2%')}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default AppSearchWIthFilter;
