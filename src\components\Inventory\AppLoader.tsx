import React from 'react';
import {View, StyleSheet} from 'react-native';
import LottieView from 'lottie-react-native';
import {Backround} from '../../constants/Color';

type Props = {
  modalVisible: boolean;
  isLookup?: boolean;
};

const AppLoader = ({modalVisible, isLookup = false}: Props) => {
  if (!modalVisible) return null;

  return (
    <View style={styles.overlay} pointerEvents="box-none">
      <View style={styles.centeredContainer} pointerEvents="box-none">
        <View
          style={
            isLookup ? styles.loaderContainerLookup : styles.loaderContainer
          }>
          <LottieView
            style={styles.lottie}
            source={require('../../assets/Lotties/Loading.json')}
            autoPlay
            loop
          />
        </View>
      </View>
    </View>
  );
};

export default AppLoader;

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject, // Fill the entire screen
    zIndex: 111, // Ensure it appears above everything else
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center', // Center vertically
    alignItems: 'center', // Center horizontally
  },
  loaderContainer: {
    marginTop: 550,
    width: 50,
    height: 50,
    backgroundColor: Backround,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  loaderContainerLookup: {
    width: 50,
    height: 50,
    backgroundColor: Backround,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  lottie: {
    width: 80,
    height: 80,
    backgroundColor: 'transparent',
  },
});
