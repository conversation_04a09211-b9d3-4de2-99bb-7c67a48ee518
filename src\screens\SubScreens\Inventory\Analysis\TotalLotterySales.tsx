import {View, Text} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Header from '../../../../components/Inventory/Header';
import DataList from '../../../../components/Inventory/AppList';
import {Department, Invoice_Itemized} from '../../../../server/types';
import {Fonts} from '../../../../styles/fonts';
import {GetAllItemsWithFilter} from '../../../../utils/PublicHelper';
import {getInventoryPort} from '../../../../server/InstanceTypes';
import {useThemeColors} from '../../../../Theme/useThemeColors';

const TotalLotterySales = () => {
  const colors = useThemeColors();

  const [totalLotterySale, setTotalLotterySale] = useState<Invoice_Itemized[]>(
    [],
  );
  const [totalLotterySaleFil, setTotalLotterySaleFil] = useState<
    Invoice_Itemized[]
  >([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    INIT();
  }, []);

  const INIT = async () => {
    await GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getTotalLotterySale',
      setTotalLotterySale,
      setTotalLotterySaleFil,
      setLoading,
    );
  };

  const renderItem = ({item}: {item: Invoice_Itemized}) => (
    <View
      style={{
        backgroundColor: colors.card,
        paddingHorizontal: wp('2.5%'),
        paddingVertical: hp('2%'),
        marginBottom: 10,
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.border,
      }}>
      <View>
        <Text style={{fontFamily: Fonts.OnestBold, color: colors.text}}>
          {item.DiffItemName}
        </Text>
        <Text
          style={{fontFamily: Fonts.OnestBold, color: colors.textSecondary}}>
          Invoice Number: {item.Invoice_Number}
        </Text>

        <Text
          style={{fontFamily: Fonts.OnestBold, color: colors.textSecondary}}>
          Quanity: {item.Quantity}
        </Text>
      </View>

      <Text
        style={{
          fontFamily: Fonts.OnestBold,
          color: colors.primary,
          fontSize: hp('2%'),
        }}>
        Total Sales: ${Number(item.Quantity) * Number(item.PricePer)}
      </Text>
    </View>
  );

  return (
    <View
      style={{
        backgroundColor: colors.background,
        height: hp('100%'),
        width: wp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Total Lottery Sales" />

      <DataList
        data={totalLotterySaleFil}
        renderItem={renderItem}
        loading={loading}
        Hight="82%"
      />
    </View>
  );
};

export default TotalLotterySales;
