

import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import AppTextInput from '../../../components/Inventory/AppTextInput'
import AppButton from '../../../components/Inventory/AppButton'

const LOT_MovetoInventoryConfirmation = () => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
      <Header NavName='Move back to Inventory'/>

      <View style={{ marginTop: 30, paddingHorizontal: 10}}>
        <AppTextInput PlaceHolder='Location #' Value='1'/>
        <AppTextInput PlaceHolder='Game #' Value='12'/>
        <AppTextInput PlaceHolder='Book #' Value='5464546'/>
        <AppTextInput PlaceHolder='Air Ticket#' Value='1'/>
      </View>

      <View style={{ marginTop: 350, paddingHorizontal: 10}}>
        <AppButton Title='Save'/>
      </View>
    </View>
  )
}

export default LOT_MovetoInventoryConfirmation