import React, {useCallback} from 'react';
import {Inventory_Filter} from '../../../../server/types';
import PrintLabelItem from './PrintLabelItem';

interface PrintLabelRendererProps {
  inputValues: {itemNum: string; value: string}[];
  action: boolean;
  openModalForItem: (itemNum: string) => void;
  clearEachItem: (item: Inventory_Filter) => void;
  colors: any;
  isDark: boolean;
}

export const usePrintLabelRenderer = ({
  inputValues,
  action,
  openModalForItem,
  clearEachItem,
  colors,
  isDark,
}: PrintLabelRendererProps) => {
  const renderItem = useCallback(
    ({item}: {item: Inventory_Filter}) => {
      const inputValue =
        inputValues.find(input => input.itemNum === item.ItemNum)?.value || '';

      return (
        <PrintLabelItem
          item={item}
          onPress={() => openModalForItem(item.ItemNum)}
          inputValue={inputValue}
          action={action}
          onClearItem={() => clearEachItem(item)}
          colors={colors}
          isDark={isDark}
        />
      );
    },
    [inputValues, action, openModalForItem, clearEachItem, colors, isDark],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    [],
  );

  return {
    renderItem,
    keyExtractor,
    getItemLayout,
  };
};

export default usePrintLabelRenderer;
