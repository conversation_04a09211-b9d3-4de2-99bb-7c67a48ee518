# CountItem Hooks

This directory contains custom hooks for managing CountItem functionality, providing separation of concerns and reusable logic.

## Hooks

### useCountItemData
- **Purpose**: Manages all data-related state and operations
- **Features**:
  - Master data, filtered data, and display data management
  - Pagination logic
  - Loading states
  - Physical count operations
  - Stock updates
  - Data fetching and refreshing

### useCountItemFilters
- **Purpose**: Handles filtering logic for inventory items
- **Features**:
  - Department, vendor, brand, and subcategory filtering
  - Search integration with filters
  - Filter state management
  - Automatic filter application

### useCountItemSearch
- **Purpose**: Manages search functionality and barcode scanning
- **Features**:
  - Debounced search queries
  - Barcode lookup integration
  - Keyboard management
  - Search mode toggling
  - Focus management

### useCountItemModal
- **Purpose**: Handles modal state and interactions
- **Features**:
  - Modal visibility management
  - Input value handling
  - Item-specific modal operations
  - Validation and restrictions

### useCountItemPerformance
- **Purpose**: Provides performance optimization utilities
- **Features**:
  - Memoized key extractors
  - Optimized item layout calculations
  - Input value lookups
  - FlatList performance props

## Usage

```tsx
import {
  useCountItemData,
  useCountItemFilters,
  useCountItemSearch,
  useCountItemModal,
} from '../../../hooks/inventory';

// In your component
const {
  masterData,
  filteredData,
  displayData,
  // ... other data
} = useCountItemData();

const {
  searchQuery,
  onSearchChange,
  // ... other search functions
} = useCountItemSearch(
  masterData,
  filteredData,
  page,
  action,
  applyPagination,
  setFilteredData,
  setLoading,
  openModalForItem,
  navigation
);
```

## Benefits

1. **Separation of Concerns**: Each hook handles a specific aspect of functionality
2. **Reusability**: Hooks can be used in other components if needed
3. **Testability**: Individual hooks can be tested in isolation
4. **Maintainability**: Logic is organized and easy to understand
5. **Performance**: Optimized state management and memoization

## Dependencies

- React (useState, useEffect, useCallback, useRef, useMemo)
- React Navigation
- AsyncStorage
- Server types and utilities
- Performance optimization utilities
