import {View, Text, FlatList} from 'react-native';
import React, {useCallback, useState} from 'react';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import Header from '../../../components/Inventory/Header';
import ChoieItemCard from '../../../components/Inventory/ChoieItemCard';
import {
  AdditionalInfo,
  Inventory,
  InventoryVendor,
} from '../../../server/types';
import {GetItemsParamsNoFilter} from '../../../utils/PublicHelper';
import AppButton from '../../../components/Inventory/AppButton';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, SecondaryHint} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import DataList from '../../../components/Inventory/AppList';

type BarcodeScreenRouteProp = RouteProp<any, 'ChoiceItem'>;

const ChoiceItem: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [itemData, setItemData] = useState<Inventory[]>(
    route.params?.ItemData || [],
  );
  const [kidData, setKidData] = useState<Inventory[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [inventoryVendor, setInventoryVendor] = useState<InventoryVendor[]>([]);
  const [inventoryAdditional, setInventoryAdditional] = useState<
    AdditionalInfo[]
  >([]);

  useFocusEffect(
    useCallback(() => {
      initialDetails();
    }, []),
  );
  const initialDetails = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/Get_Inventory_KidIndex/:Kit_ID',
      setKidData,
      {Kit_ID: itemData[0]?.ItemNum},
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      setItemData,
      {ItemNum: itemData[0]?.ItemNum},
    );
  };

  const renderItem = ({item}: {item: Inventory}) => (
    <ChoieItemCard
      key={item.ItemNum}
      Barcode={item.ItemNum}
      Title={item.ItemName}
      OnPress={() => choicItem(item)}
      Price={Number(item.Price)}
      inStock={Number(item.In_Stock)}
    />
  );

  const choicItem = async (item: Inventory) => {
    if (route.params?.isPickList) {
      navigation.navigate('PickListItem', {
        ItemData: item,
        Total: route.params?.Total,
      });
    } else {
      const vendorItems = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
        setInventoryVendor,
        {
          Vendor_Number: item.Vendor_Number,
          ItemNum: item.ItemNum,
        },
        false,
      );

      const inventoryAdditional = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getInventoryAdditional/:ItemNum',
        setInventoryAdditional,
        {
          ItemNum: item.ItemNum,
        },
        false,
      );
      navigation.navigate('Barcode', {
        ItemData: [item],
        VENDORITEM: vendorItems,
        ADDITIONAL: inventoryAdditional,
        ISCREATE: route.params?.ISCREATE,
      });
    }
  };

  return (
    <View
      style={{
        backgroundColor: Backround,
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: 10}}>
        <Header NavName="Choice Item" />
        <Text style={{fontSize: hp('3.5%'), fontFamily: Fonts.OnestBold}}>
          {itemData[0].ItemNum}
        </Text>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: hp('1.9%'),
            color: SecondaryHint,
          }}>
          Please Choose an item from below to continue
        </Text>
        <DataList
          data={kidData}
          renderItem={renderItem}
          loading={loading}
          Hight="80%"
        />
      </View>

      <View
        style={{
          backgroundColor: SecondaryHint,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
          position: 'absolute',
          right: 0,
          bottom: 0,
          left: 0,
        }}>
        <AppButton
          Title="Edit Choice Item"
          OnPress={() =>
            navigation.navigate('ChoiceBarcode', {
              ItemData: itemData,
              ListData: kidData,
            })
          }
        />
      </View>
    </View>
  );
};

export default ChoiceItem;
