import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {Fonts, FontSizes} from '../../../styles/fonts';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {LabelMargins} from '../../../Types/PrinterTypes';
import {
  getMargins,
  saveMargins,
  resetMargins,
  validateMargins,
} from '../../../utils/marginHelper';

interface LabelMarginConfigProps {
  navigation: any;
  route?: any;
}

const LabelMarginConfig: React.FC<LabelMarginConfigProps> = ({navigation}) => {
  const colors = useThemeColors();

  const [margins, setMargins] = useState<LabelMargins>({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    loadMargins();
  }, []);

  const loadMargins = async () => {
    try {
      console.log('Loading margins in component...');
      const savedMargins = await getMargins();
      console.log('Loaded margins in component:', savedMargins);
      setMargins(savedMargins);
    } catch (error) {
      console.error('Error loading margins in component:', error);
      Alert.alert('Error', 'Failed to load margin settings');
    }
  };

  const handleMarginChange = (field: keyof LabelMargins, value: string) => {
    const numericValue = parseFloat(value) || 0;
    const updatedMargins = {...margins, [field]: numericValue};
    console.log(`Updating ${field} to ${numericValue}`, updatedMargins);
    setMargins(updatedMargins);

    // Validate the updated margins
    const validationErrors = validateMargins(updatedMargins);
    setErrors(validationErrors);
  };

  const handleSave = async () => {
    const validationErrors = validateMargins(margins);
    if (validationErrors.length > 0) {
      Alert.alert('Validation Error', validationErrors.join('\n'));
      return;
    }

    setLoading(true);
    try {
      console.log('Saving margins from component:', margins);
      await saveMargins(margins);
      Alert.alert('Success', 'Margin settings saved successfully', [
        {text: 'OK', onPress: () => navigation.goBack()},
      ]);
    } catch (error) {
      console.error('Error saving margins from component:', error);
      Alert.alert('Error', 'Failed to save margin settings');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    Alert.alert(
      'Reset Margins',
      'Are you sure you want to reset all margins to default values (0)?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await resetMargins();
              setMargins({top: 0, right: 0, bottom: 0, left: 0});
              setErrors([]);
              Alert.alert('Success', 'Margins reset to default values');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset margins');
            }
          },
        },
      ],
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
      paddingTop: hp('2%'),
    },
    title: {
      fontSize: FontSizes.large,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginBottom: hp('1%'),
    },
    description: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      marginBottom: hp('3%'),
      lineHeight: 20,
    },
    inputContainer: {
      marginBottom: hp('1.5%'),
    },
    previewContainer: {
      backgroundColor: colors.surface,
      borderRadius: 10,
      padding: wp('4%'),
      marginVertical: hp('2%'),
      borderWidth: 1,
      borderColor: colors.border,
    },
    previewTitle: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginBottom: hp('1%'),
    },
    previewText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      lineHeight: 18,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: hp('4%'),
      marginHorizontal: wp('1%'),
      gap: wp('4%'),
    },
    resetButton: {
      flex: 1,
      minHeight: hp('6.5%'),
      paddingHorizontal: wp('4%'),
    },
    saveButton: {
      flex: 2,
      minHeight: hp('6.5%'),
      paddingHorizontal: wp('4%'),
    },
    errorContainer: {
      backgroundColor: colors.error + '20',
      borderRadius: 8,
      padding: wp('3%'),
      marginVertical: hp('1%'),
      borderLeftWidth: 4,
      borderLeftColor: colors.error,
    },
    errorText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.error,
    },
  });

  return (
    <View style={styles.container}>
      <Header NavName="Label Margins" />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: hp('5%')}}>
        <Text style={styles.title}>Configure Label Margins</Text>
        <Text style={styles.description}>
          Adjust the margins for your labels. Positive values move content
          inward, negative values move content outward. Values are in printer
          units.
        </Text>

        {errors.length > 0 && (
          <View style={styles.errorContainer}>
            {errors.map((error, index) => (
              <Text key={index} style={styles.errorText}>
                • {error}
              </Text>
            ))}
          </View>
        )}

        <View style={styles.inputContainer}>
          <AppTextInput
            Title="Top Margin"
            PlaceHolder="Enter top margin (e.g., 5)"
            Value={margins.top.toString()}
            onChangeText={value => handleMarginChange('top', value)}
            isNumeric={true}
            isRequired={false}
          />
        </View>

        <View style={styles.inputContainer}>
          <AppTextInput
            Title="Right Margin"
            PlaceHolder="Enter right margin (e.g., 5)"
            Value={margins.right.toString()}
            onChangeText={value => handleMarginChange('right', value)}
            isNumeric={true}
            isRequired={false}
          />
        </View>

        <View style={styles.inputContainer}>
          <AppTextInput
            Title="Bottom Margin"
            PlaceHolder="Enter bottom margin (e.g., 5)"
            Value={margins.bottom.toString()}
            onChangeText={value => handleMarginChange('bottom', value)}
            isNumeric={true}
            isRequired={false}
          />
        </View>

        <View style={styles.inputContainer}>
          <AppTextInput
            Title="Left Margin"
            PlaceHolder="Enter left margin (e.g., 5)"
            Value={margins.left.toString()}
            onChangeText={value => handleMarginChange('left', value)}
            isNumeric={true}
            isRequired={false}
          />
        </View>

        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Current Settings</Text>
          <Text style={styles.previewText}>
            Top: {margins.top} units{'\n'}
            Right: {margins.right} units{'\n'}
            Bottom: {margins.bottom} units{'\n'}
            Left: {margins.left} units
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <AppButton
            Title="Reset"
            OnPress={handleReset}
            BackRound={colors.error}
            style={styles.resetButton}
            disabled={loading}
            textStyle={{fontSize: hp('2%'), fontFamily: Fonts.OnestBold}}
          />
          <AppButton
            Title={loading ? 'Saving...' : 'Save Margins'}
            OnPress={handleSave}
            style={styles.saveButton}
            disabled={loading || errors.length > 0}
            textStyle={{fontSize: hp('2%'), fontFamily: Fonts.OnestBold}}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default LabelMarginConfig;
