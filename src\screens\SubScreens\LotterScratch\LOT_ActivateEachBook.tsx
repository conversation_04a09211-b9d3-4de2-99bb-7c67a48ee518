import {View, Text, Alert} from 'react-native';
import React, {useState} from 'react';
import {Backround} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import {RouteProp} from '@react-navigation/native';
import {Inventory, Inventory_In} from '../../../server/types';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import {Form, Formik} from 'formik';
import * as Yup from 'yup';
import AppButton from '../../../components/Inventory/AppButton';
import {Activate_Book} from '../../../Types/Lottery/Lottery_Types';
import {
  createData,
  formatNumber,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../utils/PublicHelper';
import {
  applyDefaultsActivateBook,
  applyDefaultsShiftDetails,
} from '../../../Validator/Lottery/Lottery_Validator';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import AppFocus from '../../../components/Inventory/AppFocus';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const validationSchema = Yup.object().shape({
  tickets: Yup.string().required('Tickets is required'),
  book: Yup.string().required('Book is required'),
});

type BarcodeScreenRouteProp = RouteProp<any, 'ActivateByBooks'>;
const LOT_ActivateEachBook: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [book, setBooks] = useState<Inventory>(route.params?.ItemData || []);
  const [gameID, setGameID] = useState<string>('');
  const [openSerial, setOpenSerial] = useState<string>('');
  const [bookDetails, setBookDetails] = useState<Activate_Book>();
  const [bookQty, setBookQty] = useState<number>(
    route.params?.ItemData?.In_Stock,
  );

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const AddTicketByBook = async (Values: any) => {
    const formattedSerial = formatNumber(openSerial);
    const splitSerial = formattedSerial.split('-');

    try {
      if (bookQty > 0) {
        Alert.alert('Tickets Must Empty, Then Reload Tickets');
      } else {
        const bookData: Partial<Activate_Book> = {
          Game_ID: gameID,
          Book_No: Values?.book,
          Book_Created: getFormateDate(Date()),
          Book_Tickets: Number(Values?.tickets),
          CreatedBy: 'Owner',
          Location: book.Location,
          ItemNum: book.ItemNum,
          Open_Serial: splitSerial[2],
        };
        const applyDefault = applyDefaultsActivateBook(bookData);

        const gameDetails = await GetItemsParamsNoFilter(
          (await getLotteryPort()).toString(),
          '/GetShiftBookTickets/:ItemNum/:Location',
          setBookDetails,
          {ItemNum: book.ItemNum, Location: book.Location},
        );

        if (Array.isArray(gameDetails) && gameDetails.length === 0) {
          const result = await createData<Activate_Book>({
            baseURL: (await getLotteryPort()).toString(),
            data: applyDefault,
            endpoint: '/createactivatebook',
          });
          if (result) {
            const result = await createData<Activate_Book>({
              baseURL: (await getLotteryPort()).toString(),
              data: applyDefault,
              endpoint: '/createactivatedbookhistory',
            });
          }
        } else {
          const result = await updateData<Activate_Book>({
            baseURL: (await getLotteryPort()).toString(),
            data: applyDefault,
            endpoint: '/updateactivatebooks',
          });
          if (result) {
            const result = await createData<Activate_Book>({
              baseURL: (await getLotteryPort()).toString(),
              data: applyDefault,
              endpoint: '/createactivatedbookhistory',
            });
          }
        }

        const inventoryData: Partial<Inventory> = {
          ItemNum: book.ItemNum,
          ItemName: book.ItemName,
          Dept_ID: book.Dept_ID,
          Cost: book.Cost,
          Price: book.Price,
          Retail_Price: book.Retail_Price,
          In_Stock: Number(Values?.tickets),
          Date_Created: book.Date_Created,
          Last_Sold: book.Last_Sold,
          Location: book.Location,
          Vendor_Number: book.Vendor_Number,
          Vendor_Part_Num: book.Vendor_Part_Num,
          Reorder_Level: book.Reorder_Level,
          Reorder_Quantity: book.Reorder_Quantity,
          ReOrder_Cost: book.ReOrder_Cost,
          Unit_Size: book.Unit_Size,
          Unit_Type: book.Unit_Type,
          FoodStampable: book.FoodStampable,
          Tax_1: book.Tax_1[0],
          Tax_2: book.Tax_2[0],
          Tax_3: book.Tax_3[0],
          Tax_4: book.Tax_4[0],
          Tax_5: book.Tax_5[0],
          Tax_6: book.Tax_6[0],
          Check_ID: book.Check_ID,
          Check_ID2: book.Check_ID2,
          Store_ID: book.Store_ID,
          ItemName_Extra: book.ItemName_Extra,
        };

        const applyDefaultInv = applyDefaults(inventoryData);
        const resultInv = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefaultInv,
          endpoint: '/updatebarcode',
        });

        if (resultInv) {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const CashierID = await AsyncStorage.getItem('SWIPEID');
          const ValideCashier = CashierID === null ? '100101' : CashierID;

          const inventoryAdjustData: Partial<Inventory_In> = {
            ItemNum: book.ItemNum,
            Store_ID: ValidStore,
            Quantity: Number(Values?.tickets),
            DateTime: getFormateDate(Date()),
            Dirty: true,
            TransType: 'L',
            Description: 'LOTTERY SCRATCH',
            Cashier_ID: ValideCashier,
            CostPer: book.Cost,
          };

          const applyDefault =
            applyDefaultsInventoryAdjust(inventoryAdjustData);
          const result = await createData<Inventory_In>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createinvetoryin',
          });

          if (result) {
            setBookQty(Number(Values.tickets));
            navigation.goBack();
          }
        }
      }
    } catch (error) {
      console.log('Error', error);
    }
  };

  const initialValues = {
    tickets: '',
    book: '',
  };

  return (
    <View
      style={{
        backgroundColor: colors.background,
        width: '100%',
        height: '100%',
      }}>
      <Header NavName="Activate By Book" />
      <View style={{paddingHorizontal: 10}}>
        <Text style={{fontSize: 20, fontWeight: 'bold', color: colors.text}}>
          Game Name #: {book.ItemName}
        </Text>
        <Text style={{fontSize: 20, fontWeight: 'bold', color: colors.text}}>
          Current Tickets #: {bookQty}
        </Text>
      </View>

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={values => {
          console.log(values);
          AddTicketByBook(values);
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View style={{paddingHorizontal: 10, marginTop: 50}}>
            <View style={{opacity: 1}}>
              <AppFocus
                PlaceHolder="Scan Book Number"
                Title="Scan Book Number"
                onChangeText={value => {
                  const sixDigits = value.slice(0).slice(0, 3);
                  const sixDigitsBook = value.slice(0).slice(4, 10);
                  setGameID(sixDigits);
                  setOpenSerial(value);
                  setFieldValue('book', sixDigitsBook);
                }}
              />
            </View>
            <Text
              style={{fontWeight: 'bold', fontSize: 15, color: colors.text}}>
              Book Number
            </Text>
            <View
              style={{
                borderRadius: 10,
                borderWidth: 2,
                borderColor: colors.border,
                paddingHorizontal: 10,
                paddingVertical: 12,
                marginVertical: 10,
                backgroundColor: colors.surface,
              }}>
              <Text style={{fontSize: 15, color: colors.text}}>
                {!values.book ? 'N/A' : values.book}
              </Text>
            </View>
            <AppTextInput
              PlaceHolder="Enter Tickets"
              Title="Num of Tickets"
              Value={values.tickets}
              onChangeText={handleChange('tickets')}
              onBlur={handleBlur('tickets')}
              error={errors.tickets}
              touched={touched.tickets}
              isNumeric={true}
            />

            <View>
              <AppButton Title="Save & Close" OnPress={handleSubmit} />
            </View>
          </View>
        )}
      </Formik>
    </View>
  );
};

export default LOT_ActivateEachBook;
