import {View, Text, StyleSheet, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
  UpdatePurchaseOrder,
  VendorItem,
} from '../../../server/types';
import {Formik} from 'formik';
import {RouteProp} from '@react-navigation/native';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import * as Yup from 'yup';
import Header from '../../../components/Inventory/Header';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsPurchaseOrderItem,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {
  createData,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const validationSchema = Yup.object().shape({
  quanity: Yup.number().required('Quanity is required'),
});

type CountItemRouteProp = RouteProp<any, 'PurchaseOrderQuanity'>;
const PurchaseOrderQuanity: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [inventory, setInventory] = useState<Inventory[]>([]);
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.Main);
  const [success, setSuccess] = useState<boolean>(false);
  const [vendorItems, setVendorItems] = useState<VendorItem>(
    route.params?.ItemData,
  );
  const [poType, setPoType] = useState<number>(route.params?.poType);
  const initialValues = {
    quanity: '',
  };

  useEffect(() => {
    getInvetoryDetails();
  }, []);
  const getInvetoryDetails = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      setInventory,
      {ItemNum: vendorItems.ItemNum},
      false,
    );
  };

  const AddPurchaseOrderItems = async (Values: any) => {
    if (success) {
      Alert.alert('Item Already Added!');
    } else {
      if (Values.quanity > 0) {
        AddItems(Values);
      } else {
        Alert.alert('Please Enter Correct Value!');
      }
    }
  };

  const AddItems = async (Values: any) => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;

    const ValidCost =
      vendorItems.CostPer <= 0
        ? Number(inventory[0]?.Cost?.toFixed(2))
        : Number(vendorItems.CostPer);
    try {
      const checkCaseQty =
        vendorItems.NumPerVenCase === 0 ||
        vendorItems.NumPerVenCase === undefined
          ? Number(Values.quanity)
          : Number(vendorItems.NumPerVenCase) * Number(Values.quanity);
      const checkNumCase =
        vendorItems.NumPerVenCase === 0 ||
        vendorItems.NumPerVenCase === undefined
          ? 0
          : vendorItems.NumPerVenCase;

      const TotalCost =
        Number(checkCaseQty) > 1
          ? Number(ValidCost) * Number(checkCaseQty)
          : ValidCost;

      const ExpectedRecive = Number(
        vendorItems.NumPerVenCase === 0 ||
          vendorItems.NumPerVenCase === undefined
          ? Values.quanity
          : checkCaseQty,
      );

      const TotalCostDirect = Number(ValidCost) * Number(Values.quanity);

      const cashPack =
        vendorItems.NumPerVenCase === 0 ||
        vendorItems.NumPerVenCase === undefined
          ? 0
          : Number(Values.quanity);

      const poItmes: Partial<PurchaseOrderItems> = {
        PO_Number: mainPO.PO_Number,
        ItemNum: vendorItems.ItemNum,
        Quan_Ordered:
          route.params?.IsDirect === true
            ? Number(Values.quanity)
            : checkCaseQty,
        CostPer: ValidCost,
        Quan_Received: route.params?.IsDirect ? Number(Values.quanity) : 0,
        Vendor_Part_Number: vendorItems.Vendor_Part_Num || '',
        CasePack: route.params?.IsDirect === true ? 0 : cashPack,
        Store_ID: ValidStore,
        destStore_ID: ValidStore,
        NumberPerCase: route.params?.IsDirect === true ? 0 : checkNumCase,
      };

      const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);
      setSuccess(true);
      const createResult = await createData<PurchaseOrderItems>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createpodetails',
      });

      if (createResult) {
        const calExistTotal = Number(mainPO.Total_Cost) + Number(TotalCost);
        const calExistExpected =
          Number(mainPO.ExpectedAmountToReceive) + Number(ExpectedRecive);

        const calExistTotalDirect =
          Number(mainPO.Total_Cost) + Number(TotalCostDirect);

        if (route.params?.IsDirect === true) {
          updatePO(calExistTotalDirect, 0);
        } else {
          updatePO(calExistTotal, calExistExpected);
        }

        // if (route.params?.IsDirect === true) {
        //   AdjustInventory(Values);
        // }
        setSuccess(true);
      } else {
        setSuccess(false);
        console.log('Error');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: TotalCost,
      Total_Cost_Received: route.params?.IsDirect
        ? TotalCost
        : Number(mainPO.Total_Cost) > 0
        ? Number(mainPO.Total_Cost_Received)
        : 0,
      Total_Charges: route.params?.IsDirect === true ? TotalCost : 0,
      ExpectedAmountToReceive: Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: route.params?.IsDirect === true ? 'C' : 'O',
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: route.params?.IsDirect === true ? 2 : 0,
      Dirty: true,
      Print_Notes_On_PO: true,
      ShipTo_1: mainPO.ShipTo_1,
      ShipTo_2: mainPO.ShipTo_2,
      ShipTo_4: mainPO.ShipTo_4,
      Terms: route.params?.IsDirect ? 'DSD' : '',
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
        true,
      );
      if (poType === 0) {
        navigation.navigate('PurchaseOrderItem');
      } else {
        navigation.navigate('ReturnToVendor');
      }
    } else {
      console.log('Error!!');
    }
  };

  const AdjustInventory = async (Values: any) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: vendorItems.ItemNum},
    );

    const adjustStock =
      Number(Values.quanity) + Number(getInventory[0].In_Stock);
    const inventoryData: Partial<Inventory> = {
      ItemNum: getInventory[0]?.ItemNum,
      ItemName: getInventory[0]?.ItemName,
      Dept_ID: getInventory[0]?.Dept_ID,
      Cost: getInventory[0]?.Cost,
      Price: getInventory[0]?.Price,
      Retail_Price: getInventory[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: getInventory[0]?.Date_Created,
      Last_Sold: getInventory[0]?.Last_Sold,
      Location: getInventory[0]?.Location,
      Vendor_Number: getInventory[0]?.Vendor_Number,
      Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
      Reorder_Level: getInventory[0]?.Reorder_Level,
      Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
      ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
      Unit_Size: getInventory[0]?.Unit_Size,
      Unit_Type: getInventory[0]?.Unit_Type,
      FoodStampable: getInventory[0]?.FoodStampable,
      Tax_1: getInventory[0]?.Tax_1[0],
      Tax_2: getInventory[0]?.Tax_2[0],
      Tax_3: getInventory[0]?.Tax_3[0],
      Tax_4: getInventory[0]?.Tax_4[0],
      Tax_5: getInventory[0]?.Tax_5[0],
      Tax_6: getInventory[0]?.Tax_6[0],
      Check_ID: getInventory[0]?.Check_ID,
      Check_ID2: getInventory[0]?.Check_ID2,
      Store_ID: getInventory[0]?.Store_ID,
      ItemName_Extra: getInventory[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: getInventory[0].ItemNum,
        Store_ID: ValidStore,
        Quantity: adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: getInventory[0].Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    itemInfoCard: {
      backgroundColor: colors.card,
      borderRadius: 12,
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('2%'),
      marginVertical: hp('1.5%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    itemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
      marginBottom: 4,
    },
    itemDetailsRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    itemCost: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    itemPrice: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
      paddingHorizontal: wp('2%'),
      borderRadius: 8,
    },
    stockText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    formContainer: {
      marginTop: hp('2%'),
    },
    inputContainer: {
      marginBottom: hp('2%'),
    },
    buttonContainer: {
      marginTop: hp('1.5%'),
    },
  });
  return (
    <View style={styles.container}>
      <Header NavName="Add Item" />

      <View style={styles.itemInfoCard}>
        <Text style={styles.itemName}>{vendorItems.ItemName}</Text>
        <View style={styles.itemDetailsRow}>
          <Text style={styles.itemCost}>Cost: {inventory[0]?.Cost}</Text>
          <Text style={styles.itemPrice}>${inventory[0]?.Price}</Text>
        </View>
        <Text style={styles.stockText}>
          Available Stock: {inventory[0]?.In_Stock}
        </Text>
      </View>

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={values => {
          AddPurchaseOrderItems(values);
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <AppTextInput
                PlaceHolder="Enter Quantity"
                Title="Quantity"
                Value={values.quanity}
                onChangeText={handleChange('quanity')}
                onBlur={handleBlur('quanity')}
                error={errors.quanity}
                touched={touched.quanity}
                isNumeric={true}
              />
            </View>

            <View style={styles.buttonContainer}>
              <AppButton Title="Save & Close" OnPress={handleSubmit} />
            </View>
          </View>
        )}
      </Formik>
    </View>
  );
};

export default PurchaseOrderQuanity;
