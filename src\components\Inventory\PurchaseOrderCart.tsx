import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Primary, Secondary, SecondaryHint} from '../../constants/Color';
import {Fonts, FontSizes} from '../../styles/fonts';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

type Props = {
  PONumber: number;
  Vendor: string;
  DateTime: string;
  OnPress: () => void;
  Price: number;
};

const PurchaseOrderCart = ({
  DateTime,
  PONumber,
  Vendor,
  Price,
  OnPress,
}: Props) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderBlockColor: colors.border,
      borderBottomWidth: 1,
      backgroundColor: colors.card,
      padding: 8,
      borderRadius: 6,
      marginBottom: 4,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
  });

  return (
    <TouchableOpacity style={[styles.row]} onPress={OnPress}>
      <View style={{gap: 3, width: wp('60%')}}>
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{
            fontSize: FontSizes.small,
            fontFamily: Fonts.OnestBold,
            color: colors.text,
          }}>
          Purchase Order: {PONumber}
        </Text>
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{
            fontSize: FontSizes.small,
            fontFamily: Fonts.OnestBold,
            color: colors.textSecondary,
          }}>
          Vendor: {Vendor}
        </Text>

        <Text
          style={{
            fontSize: FontSizes.small,
            fontFamily: Fonts.OnestBold,
            color: colors.textSecondary,
          }}>
          Created: {DateTime}
        </Text>
      </View>
      <Text
        style={{
          fontSize: FontSizes.medium,
          fontFamily: Fonts.OnestBold,
          color: colors.primary,
        }}>
        ${Price || 0}
      </Text>
    </TouchableOpacity>
  );
};

export default PurchaseOrderCart;
