import React from 'react';
import {PurchaseOrder} from '../../server/types';
import DataList from './AppList';
import PurchaseOrderCart from './PurchaseOrderCart';

interface PurchaseOrderListProps {
  data: PurchaseOrder[];
  loading: boolean;
  refreshing: boolean;
  onRefresh: () => Promise<void>;
  onItemPress: (item: PurchaseOrder) => void;
  height?: string;
}

const PurchaseOrderList: React.FC<PurchaseOrderListProps> = ({
  data,
  loading,
  refreshing,
  onRefresh,
  onItemPress,
  height = '66%',
}) => {
  const renderItem = ({item}: {item: PurchaseOrder}) => (
    <PurchaseOrderCart
      PONumber={item.PO_Number}
      Vendor={item.Company}
      Price={Number(item.Total_Cost.toFixed(2))}
      DateTime={new Date(item.DateTime).toISOString().split('T')[0]}
      OnPress={() => onItemPress(item)}
    />
  );

  return (
    <DataList
      data={data}
      renderItem={renderItem}
      loading={loading}
      Hight={height}
      refreshing={refreshing}
      onRefresh={onRefresh}
    />
  );
};

export default PurchaseOrderList;
