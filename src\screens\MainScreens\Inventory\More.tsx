import {
  View,
  Text,
  ScrollView,
  Alert,
  StyleSheet,
  StatusBar,
} from 'react-native';
import React from 'react';
import MoreScreen from '../../../components/Inventory/MoreScreen';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import {hasPermission} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Feather from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const More: React.FC<NavProps> = ({navigation}) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Menu options with icons and navigation functions
  const menuItems = [
    {
      PageName: 'Lookup Items',
      Description: 'Quick-select lists for efficient inventory tracking',
      Icon: (
        <Feather
          name="search"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_INVEN_VIEW',
      permissionMessage: 'You do not have permission to view inventory.',
      navigate: () => navigation.navigate('Lookup'),
    },
    {
      PageName: 'Count Items',
      Description: 'Physically count inventory for accuracy',
      Icon: (
        <MaterialIcons
          name="list-alt"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_HH_Inv_Count',
      permissionMessage: 'You do not have permission to view Count Items.',
      navigate: () => navigation.navigate('CountItem'),
    },
    {
      PageName: 'Pick & Hold',
      Description: 'Collect items for an order and hold for customer pickup',
      Icon: (
        <FontAwesome
          name="hand-paper-o"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_HH_Inv_Count',
      permissionMessage: 'You do not have permission for this function',
      navigate: () => navigation.navigate('ItemPickList'),
    },
    {
      PageName: 'Purchase Orders',
      Description: 'Create, track, and manage purchase orders',
      Icon: (
        <MaterialCommunityIcons
          name="file-document-outline"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_HH_Create_PO',
      permissionMessage: 'You do not have permission for this function',
      navigate: () => navigation.navigate('PurchaseOrder', {ItemData: 0}),
    },
    {
      PageName: 'Direct Purchase',
      Description: 'Make instant purchases without a purchase order',
      Icon: (
        <MaterialIcons
          name="shopping-cart"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_HH_DSD',
      permissionMessage: 'You do not have permission to view Direct Purchase.',
      navigate: () => navigation.navigate('PurchaseOrderCreate', {POType: 2}),
    },
    {
      PageName: 'Return to Vendor',
      Description: 'Return defective or unwanted items to suppliers',
      Icon: (
        <MaterialIcons
          name="assignment-return"
          size={hp('2.8%')}
          color={MaterialColors.primary.main}
        />
      ),
      permission: 'CFA_HH_PO_Receive',
      permissionMessage: 'You do not have permission to view Direct Purchase.',
      navigate: () => navigation.navigate('PurchaseOrderCreate', {POType: 1}),
    },
  ];

  // Handle menu item press with permission check
  const handleMenuPress = async item => {
    const isAuthorized = await hasPermission(item.permission);

    if (!isAuthorized) {
      Alert.alert(item.permissionMessage);
      return;
    }

    item.navigate();
  };
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: wp('5%'),
    },
    headerTitle: {
      fontSize: hp('3%'),
      textAlign: 'center',
      fontFamily: Fonts.OnestBold,
      paddingVertical: hp('2%'),
      marginBottom: hp('2%'),
    },
    menuListContainer: {
      borderRadius: 16,
      overflow: 'hidden',
    },
    itemWithBorder: {
      borderBottomWidth: 1,
    },
  });
  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      <Text style={[styles.headerTitle, {color: colors.text}]}>Inventory</Text>

      <View
        style={[styles.menuListContainer, {backgroundColor: colors.surface}]}>
        {menuItems.map((item, index) => (
          <View
            key={index}
            style={[
              index !== menuItems.length - 1 && styles.itemWithBorder,
              {borderBottomColor: colors.border},
            ]}>
            <MoreScreen
              PageName={item.PageName}
              Description={item.Description}
              Icon={React.cloneElement(item.Icon, {color: colors.primary})}
              Onpress={() => handleMenuPress(item)}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default More;
