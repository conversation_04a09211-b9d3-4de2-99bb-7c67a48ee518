import {View, Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import React, {useCallback, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  createData,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  showAlert,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import DataList from '../../../components/Inventory/AppList';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import {deleteItem} from '../../../server/service';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type CountItemRouteProp = RouteProp<any, 'ReturnToVendor'>;

const ReturnVendor: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.ItemData);
  const [itemPO, setItemPO] = useState<PurchaseOrderItems[]>([]);
  const [initialPO, setInitialPO] = useState<PurchaseOrderItems[]>([]);
  const [itemPOFilter, setItemPOFilter] = useState<PurchaseOrderItems[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      getInitialDetails();
    }, []),
  );

  const getInitialDetails = async () => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: route?.params?.ItemData?.PO_Number},
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setInitialPO,
      {PO_Number: route?.params?.ItemData?.PO_Number},
      false,
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpounique/:PO_Number',
      setMainPO,
      {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
      true,
    );
  };

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <View>
        <PurchaseOrderItemCart
          Name={item?.ItemName}
          Ordered={item?.Quan_Ordered}
          RecivedNow={item?.Quan_Received}
          DamagedNow={item.Quan_Damaged}
          Delete={() => DeletePurchaseItem(item?.PO_Number, item.ItemNum, item)}
          ItemCost={Number(item.Quan_Ordered) * Number(item.CostPer)}
        />
      </View>
    );
  };

  const DeletePurchaseItem = (
    ponumber: number,
    itemnum: string,
    item: PurchaseOrderItems,
  ) => {
    const dataToDelete = {
      PO_Number: ponumber,
      ItemNum: itemnum,
    };

    deleteItem(getInventoryPort().toString(), '/deletepurchaseorderitem', {
      Data: dataToDelete,
      loading: setLoading,
      onSuccess: (result: any) => {
        if (result.success) {
          const updatePoitems = itemPO.filter(
            existingItem =>
              !(
                existingItem.PO_Number === ponumber &&
                existingItem.ItemNum === itemnum
              ),
          );
          setItemPO(updatePoitems);
          setItemPOFilter(updatePoitems);

          const TotalCost = updatePoitems.reduce(
            (sum, item) => sum + item.Quan_Ordered * item.CostPer,
            0,
          );
          setMainPO(prev => ({...prev, Total_Cost: TotalCost}));
          updatePO(TotalCost, updatePoitems.length);
        }
      },
      onError: () => {
        Alert.alert('Error', 'Failed to delete item from purchase order');
      },
    });
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItems = {
      PO_Number: mainPO.PO_Number,
      Vendor_Number: mainPO.Vendor_Number,
      Date_Created: mainPO.Date_Created,
      Expected_Items: Expected || itemPO.length,
      Expected_Cost: mainPO.Expected_Cost,
      Total_Cost: TotalCost || mainPO.Total_Cost,
      Company: mainPO.Company,
      Date_Expected: mainPO.Date_Expected,
      Order_Status: mainPO.Order_Status,
      Order_Completed: mainPO.Order_Completed,
      PO_Received: mainPO.PO_Received,
    };

    try {
      const result = await updateData({
        baseURL: (await getInventoryPort()).toString(),
        data: poItems,
        endpoint: '/updatepurchaseorder',
      });
      console.log('PO Update result:', result);
    } catch (error) {
      console.error('Error updating PO:', error);
    }
  };

  const AdjustStocking = () => {
    if (itemPO.length > 0) {
      if (route.params?.POTYPE === 1) {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Return(quan);
        });
      } else {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Direct(quan);
        });
      }
    } else {
      Alert.alert('Please Select Direct Purchase Items');
    }
  };

  const AdjustInventory_Direct = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );

    const adjustStock =
      Number(PurchaseItems.Quan_Ordered) +
      Number((getInventory as any)[0].In_Stock);
    const inventoryData: Partial<Inventory> = {
      ItemNum: (getInventory as any)[0]?.ItemNum,
      ItemName: (getInventory as any)[0]?.ItemName,
      Dept_ID: (getInventory as any)[0]?.Dept_ID,
      Cost: (getInventory as any)[0]?.Cost,
      Price: (getInventory as any)[0]?.Price,
      Retail_Price: (getInventory as any)[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: (getInventory as any)[0]?.Date_Created,
      Last_Sold: (getInventory as any)[0]?.Last_Sold,
      Location: (getInventory as any)[0]?.Location,
      Vendor_Number: (getInventory as any)[0]?.Vendor_Number,
      Vendor_Part_Num: (getInventory as any)[0]?.Vendor_Part_Num,
      Reorder_Level: (getInventory as any)[0]?.Reorder_Level,
      Reorder_Quantity: (getInventory as any)[0]?.Reorder_Quantity,
      ReOrder_Cost: (getInventory as any)[0]?.ReOrder_Cost,
      Unit_Size: (getInventory as any)[0]?.Unit_Size,
      Unit_Type: (getInventory as any)[0]?.Unit_Type,
      FoodStampable: (getInventory as any)[0]?.FoodStampable,
      Tax_1: (getInventory as any)[0]?.Tax_1[0],
      Tax_2: (getInventory as any)[0]?.Tax_2[0],
      Tax_3: (getInventory as any)[0]?.Tax_3[0],
      Tax_4: (getInventory as any)[0]?.Tax_4[0],
      Tax_5: (getInventory as any)[0]?.Tax_5[0],
      Tax_6: (getInventory as any)[0]?.Tax_6[0],
      Check_ID: (getInventory as any)[0]?.Check_ID,
      Check_ID2: (getInventory as any)[0]?.Check_ID2,
      Store_ID: (getInventory as any)[0]?.Store_ID,
      ItemName_Extra: (getInventory as any)[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: (getInventory as any)[0].ItemNum,
        Store_ID: ValidStore,
        Quantity: adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: (getInventory as any)[0].Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Direct Purchase Items Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };

  const AdjustInventory_Return = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );
    const adjustStock =
      Number((getInventory as any)[0]?.In_Stock) +
      Number(PurchaseItems.Quan_Ordered);
    const inventoryData: Partial<Inventory> = {
      ItemNum: (getInventory as any)[0]?.ItemNum,
      ItemName: (getInventory as any)[0]?.ItemName,
      Dept_ID: (getInventory as any)[0]?.Dept_ID,
      Cost: (getInventory as any)[0]?.Cost,
      Price: (getInventory as any)[0]?.Price,
      Retail_Price: (getInventory as any)[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: (getInventory as any)[0]?.Date_Created,
      Last_Sold: (getInventory as any)[0]?.Last_Sold,
      Location: (getInventory as any)[0]?.Location,
      Vendor_Number: (getInventory as any)[0]?.Vendor_Number,
      Vendor_Part_Num: (getInventory as any)[0]?.Vendor_Part_Num,
      Reorder_Level: (getInventory as any)[0]?.Reorder_Level,
      Reorder_Quantity: (getInventory as any)[0]?.Reorder_Quantity,
      ReOrder_Cost: (getInventory as any)[0]?.ReOrder_Cost,
      Unit_Size: (getInventory as any)[0]?.Unit_Size,
      Unit_Type: (getInventory as any)[0]?.Unit_Type,
      FoodStampable: (getInventory as any)[0]?.FoodStampable,
      Tax_1: (getInventory as any)[0]?.Tax_1[0],
      Tax_2: (getInventory as any)[0]?.Tax_2[0],
      Tax_3: (getInventory as any)[0]?.Tax_3[0],
      Tax_4: (getInventory as any)[0]?.Tax_4[0],
      Tax_5: (getInventory as any)[0]?.Tax_5[0],
      Tax_6: (getInventory as any)[0]?.Tax_6[0],
      Check_ID: (getInventory as any)[0]?.Check_ID,
      Check_ID2: (getInventory as any)[0]?.Check_ID2,
      Store_ID: (getInventory as any)[0]?.Store_ID,
      ItemName_Extra: (getInventory as any)[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: (getInventory as any)[0]?.ItemNum,
        Store_ID: ValidStore,
        Quantity: '-' + adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: (getInventory as any)[0]?.Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Vendor Return Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };

  const NavHandle = () => {
    if (itemPO.length > 0) {
      showAlert(
        route.params?.POTYPE === 1
          ? 'Vendor Return Items Not Saved Yet, Would You Like to Save Close?'
          : 'Direct Purchase Items Not Saved Yet, Would You Like to Save Close?',
      )
        .then(async result => {
          if (result) {
            AdjustStocking();
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      showAlert(
        "You didn't add any items to this direct purchase order!",
        'Confirmation',
        true,
        'OK',
        'Discard Order',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const isReturnOrDsd = route.params?.POTYPE === 1 ? false : true;

  const navigateToVendorItems = () => {
    navigation.navigate('ReturnVendorItemsScreen', {
      mainPO,
      itemPO,
      isReturnOrDsd,
      isEnableFilter: false,
      setFilter: () => {},
    });
  };

  const styles = StyleSheet.create({
    rootContainer: {
      backgroundColor: colors.background,
      flex: 1,
      justifyContent: 'space-between',
    },
    mainContainer: {
      paddingHorizontal: wp('2.5%'),
      flex: 1,
    },
    vendorInfoCard: {
      backgroundColor: colors.surface,
      paddingVertical: hp('1.2%'),
      paddingHorizontal: wp('3%'),
      borderRadius: 8,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: hp('1%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    vendorDetails: {
      gap: hp('0.5%'),
    },
    vendorName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    vendorNumber: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    totalCost: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    addItemsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
      marginVertical: hp('0.5%'),
    },
    addItemsText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
      paddingVertical: hp('0.5%'),
      paddingHorizontal: wp('1.5%'),
    },
    bottomButtonContainer: {
      position: 'absolute',
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
  });

  return (
    <View style={styles.rootContainer}>
      <View style={styles.mainContainer}>
        <Header
          NavName={
            route.params?.POTYPE === 1 ? 'Return to Vendor' : 'Direct Purchase'
          }
          isProvid={true}
          Onpress={() => NavHandle()}
        />

        <View style={styles.vendorInfoCard}>
          <View style={styles.vendorDetails}>
            <Text style={styles.vendorName}>{mainPO.Company}</Text>
            <Text style={styles.vendorNumber}>
              Vendor Number: {mainPO.Vendor_Number}
            </Text>
          </View>
          <Text style={styles.totalCost}>
            $
            {mainPO.Total_Cost.toFixed(2).includes('-0.0')
              ? '0.00'
              : mainPO.Total_Cost.toFixed(2)}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.addItemsButton}
          onPress={navigateToVendorItems}>
          <Text style={styles.addItemsText}>Add Items</Text>
          <AntDesign
            name="pluscircle"
            size={hp('3.3%')}
            color={colors.primary}
          />
        </TouchableOpacity>

        <DataList
          data={itemPO}
          renderItem={renderItem}
          loading={loading}
          Hight="70%"
        />
      </View>

      <View style={styles.bottomButtonContainer}>
        <FAB
          label="Save & Close"
          position="bottomRight"
          onPress={() => AdjustStocking()}
        />
      </View>
    </View>
  );
};

export default ReturnVendor;
