import { View, Text } from 'react-native'
import React from 'react'
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Backround } from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';

type NavProps = {
    navigation: NativeStackNavigationProp<any>; 
  };
  
  
  
  const LOT_AddInventory: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
      <Header NavName='Add Book to Inventory'/>

      <View style={{paddingHorizontal: 10, marginTop: 30}}>
        <AppTextInput PlaceHolder='Scan New Book'/>
        <AppTextInput PlaceHolder='Game #'/>
        <AppTextInput PlaceHolder='Book #'/>
      </View>

      <View style={{paddingHorizontal: 10, marginTop: 430}}>
        <AppButton Title='Save'/>
      </View>
      
    </View>
  )
}

export default LOT_AddInventory