import {useState, useEffect, useCallback} from 'react';
import {PurchaseOrder, Vendor} from '../server/types';
import {
  GetAllItems,
  GetItemsWithParams,
  showAlertOK,
} from '../utils/PublicHelper';
import {getInventoryPort} from '../server/InstanceTypes';

export interface UsePurchaseOrdersProps {
  poType: number;
  onDataLoaded?: (data: PurchaseOrder[]) => void;
}

export interface UsePurchaseOrdersReturn {
  // Data state
  purchaseOrd: PurchaseOrder[];
  filterPO: PurchaseOrder[];
  vendors: Vendor[];

  // Loading states
  loading: boolean;
  refreshing: boolean;

  // Search and filter state
  searchQuery: string;
  action: string;
  selectedVendor: string;
  isEnableFilter: boolean;

  // Actions
  setSearchQuery: (query: string) => void;
  setAction: (action: string) => void;
  setSelectedVendor: (vendor: string) => void;
  getPurOrder: () => Promise<void>;
  getVendor: () => Promise<void>;
  onRefresh: () => Promise<void>;
  onSearchChange: (text: string) => Promise<void>;
  onStatusFilter: (value: string) => void;
  resetFilters: () => void;
}

export const usePurchaseOrders = ({
  poType,
  onDataLoaded: _onDataLoaded,
}: UsePurchaseOrdersProps): UsePurchaseOrdersReturn => {
  const [purchaseOrd, setPurchaseOrd] = useState<PurchaseOrder[]>([]);
  const [filterPO, setFilterPO] = useState<PurchaseOrder[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [action, setAction] = useState('O');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);

  // Filter effect
  useEffect(() => {
    let filtered = purchaseOrd;

    if (selectedVendor) {
      filtered = filtered.filter(item => item.Vendor_Number === selectedVendor);
    }

    if (action) {
      filtered = filtered.filter(item => item.Status === action);
    }

    setFilterPO(filtered);
  }, [purchaseOrd, selectedVendor, action]);

  // Enable filter effect
  useEffect(() => {
    if (selectedVendor) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedVendor]);

  const getPurOrder = useCallback(async () => {
    const data = await GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorder/:POType',
      setPurchaseOrd,
      setFilterPO,
      setLoading,
      {POType: poType},
    );

    if (data === undefined || !data) {
      showAlertOK(
        'Database Connection Failed, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
      return;
    }
  }, [poType]);

  const getVendor = useCallback(async () => {
    GetAllItems<Vendor[]>(
      (await getInventoryPort()).toString(),
      '/GetVendors',
      setVendors,
      setLoading,
    );
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await getPurOrder();
      await getVendor();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
    setRefreshing(false);
  }, [getPurOrder, getVendor]);

  // Custom search function for PurchaseOrder
  const searchPurchaseOrders = useCallback(
    (text: string, orders: PurchaseOrder[]): PurchaseOrder[] => {
      if (text === '') {
        return orders;
      }

      const searchKeys = ['PO_Number', 'Company', 'Vendor_Number'];
      return orders.filter(order =>
        searchKeys.some(key => {
          const value = order[key as keyof PurchaseOrder];
          return value?.toString().toLowerCase().includes(text.toLowerCase());
        }),
      );
    },
    [],
  );

  const onSearchChange = useCallback(
    async (text: string) => {
      setSearchQuery(text);
      setLoading(true);

      if (action === 'O') {
        const filteredPO = purchaseOrd.filter(order => order.Status === 'O');
        if (text === '') {
          setFilterPO(filteredPO);
        } else {
          const searchResult = searchPurchaseOrders(text, filteredPO);
          setFilterPO(searchResult);
        }
      } else {
        const filteredPO = purchaseOrd.filter(order => order.Status === 'C');
        if (text === '') {
          setFilterPO(filteredPO);
        } else {
          const searchResult = searchPurchaseOrders(text, filteredPO);
          setFilterPO(searchResult);
        }
      }

      setLoading(false);
    },
    [action, purchaseOrd, searchPurchaseOrders],
  );

  const onStatusFilter = useCallback(
    (value: string) => {
      try {
        const filteredPO = purchaseOrd.filter(order => order.Status === value);
        setFilterPO(filteredPO);
        setAction(value);
      } catch (error) {
        console.log(error);
      }
    },
    [purchaseOrd],
  );

  const resetFilters = useCallback(() => {
    setSelectedVendor('');
    setAction('O');
    setSearchQuery('');
    setIsEnableFilter(false);
  }, []);

  return {
    // Data state
    purchaseOrd,
    filterPO,
    vendors,

    // Loading states
    loading,
    refreshing,

    // Search and filter state
    searchQuery,
    action,
    selectedVendor,
    isEnableFilter,

    // Actions
    setSearchQuery,
    setAction,
    setSelectedVendor,
    getPurOrder,
    getVendor,
    onRefresh,
    onSearchChange,
    onStatusFilter,
    resetFilters,
  };
};
