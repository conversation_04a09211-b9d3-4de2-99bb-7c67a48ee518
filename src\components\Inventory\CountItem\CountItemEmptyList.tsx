import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import LottieView from 'lottie-react-native';
import { Fonts } from '../../../styles/fonts';
import {
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useThemeColors } from '../../../Theme/useThemeColors';

interface CountItemEmptyListProps {
  searchQuery: string;
}

const CountItemEmptyList: React.FC<CountItemEmptyListProps> = ({ searchQuery }) => {
  const colors = useThemeColors();

  const styles = StyleSheet.create({
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
  });

  return (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : 'No inventory items available'}
      </Text>
    </View>
  );
};

export default memo(CountItemEmptyList);
