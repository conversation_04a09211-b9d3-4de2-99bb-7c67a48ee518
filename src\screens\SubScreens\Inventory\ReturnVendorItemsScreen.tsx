import React, {memo, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  Keyboard,
} from 'react-native';
import {RouteProp} from '@react-navigation/native';
import Header from '../../../components/Inventory/Header';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {VendorItem, PurchaseOrder} from '../../../server/types';
import {MaterialColors} from '../../../constants/MaterialColors';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useReturnVendor} from '../../../hooks/useReturnVendor';

// Define components outside of the main component
const VendorItemComponent = memo(
  ({
    item,
    onPress,
    colors,
  }: {
    item: VendorItem;
    onPress: (item: VendorItem) => void;
    colors: any;
  }) => {
    return (
      <TouchableOpacity
        style={[
          {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('1%'),
            marginVertical: hp('0.3%'),
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
            backgroundColor: colors.card,
          },
        ]}
        onPress={() => onPress(item)}>
        <View
          style={{
            gap: hp('0.4%'),
            flex: 1,
          }}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.small,
              color: colors.text,
            }}>
            {item.ItemName}
          </Text>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.small,
              color: colors.textSecondary,
            }}>
            {`Per Case: ${item.NumPerVenCase}`}
          </Text>
        </View>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.small,
            color: colors.primary,
          }}>
          ${item.CostPer.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.item.ItemNum === nextProps.item.ItemNum;
  },
);

const EmptyVendorList = memo(
  ({searchQuery, colors}: {searchQuery: string; colors: any}) => (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: hp('10%'),
      }}>
      <Text
        style={{
          fontSize: FontSizes.medium,
          color: colors.textSecondary,
          textAlign: 'center',
          marginTop: 20,
          fontFamily: Fonts.OnestMedium,
        }}>
        {searchQuery ? 'No matching items found' : 'No vendor items available'}
      </Text>
    </View>
  ),
);

const VendorListFooter = memo(
  ({isLoading, colors}: {isLoading: boolean; colors: any}) => {
    if (!isLoading) {
      return null;
    }

    return (
      <View
        style={{
          padding: 15,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        }}>
        <ActivityIndicator size="small" color={MaterialColors.primary.main} />
        <Text
          style={{
            marginLeft: 10,
            fontFamily: Fonts.OnestMedium,
            color: colors.textSecondary,
          }}>
          Loading more items...
        </Text>
      </View>
    );
  },
);

type ReturnVendorItemsRouteProp = RouteProp<any, 'ReturnVendorItemsScreen'>;

interface ReturnVendorItemsScreenProps {
  route: ReturnVendorItemsRouteProp;
  navigation: any;
}

const ReturnVendorItemsScreen: React.FC<ReturnVendorItemsScreenProps> = ({
  route,
  navigation,
}) => {
  const routeParams = route.params || {};
  const {
    mainPO,
    itemPO = [],
    isReturnOrDsd = false,
    isEnableFilter = false,
    setFilter = () => {},
    poType = 0,
  } = routeParams as {
    mainPO: PurchaseOrder;
    itemPO: any[];
    isReturnOrDsd: boolean;
    isEnableFilter: boolean;
    setFilter: (value: boolean) => void;
    poType: number;
  };

  const colors = useThemeColors();

  const {
    vendorItemsFilter,
    loading,
    searchQuery,
    showLookup,
    textInputRef,
    VendorItemAdd,
    handleLoadMore,
    handleSearchChange,
    toggleLookup,
    handleDoneClick,
    keyExtractor,
    getItemLayout,
    setCamera,
  } = useReturnVendor(mainPO, navigation, route, itemPO, isReturnOrDsd, poType);

  const renderModalItem = useCallback(
    ({item}: {item: VendorItem}) => (
      <VendorItemComponent
        item={item}
        onPress={VendorItemAdd}
        colors={colors}
      />
    ),
    [VendorItemAdd, colors],
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    headerContainer: {
      width: '95%',
    },
    searchContainer: {
      paddingBottom: hp('1%'),
    },
    itemCountText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
      paddingVertical: hp('1%'),
    },
    listContainer: {
      backgroundColor: colors.background,
      borderRadius: 12,
      flex: 1,
    },
    centeredLoading: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
    listContent: {
      paddingBottom: hp('2%'),
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Header
          NavName="Add Items"
          Onpress={() => navigation.goBack()}
          isProvid={true}
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            handleSearchChange('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCamera(true);
          }}
        />
      </View>

      <View style={styles.searchContainer}>
        <AppSearchWIthFilter
          OnSearch={handleSearchChange}
          SearchValue={searchQuery}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={isEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={handleDoneClick}
        />

        <Text style={styles.itemCountText}>
          Total Items: ({vendorItemsFilter.length || 0})
        </Text>
      </View>

      <View style={styles.listContainer}>
        {loading ? (
          <View style={styles.centeredLoading}>
            <ActivityIndicator
              size="large"
              color={MaterialColors.primary.main}
            />
          </View>
        ) : (
          <FlatList
            data={vendorItemsFilter}
            keyExtractor={keyExtractor}
            renderItem={renderModalItem}
            getItemLayout={getItemLayout}
            initialNumToRender={10}
            maxToRenderPerBatch={10}
            windowSize={10}
            removeClippedSubviews={true}
            updateCellsBatchingPeriod={75}
            onEndReachedThreshold={0.5}
            onEndReached={handleLoadMore}
            ListEmptyComponent={
              <EmptyVendorList searchQuery={searchQuery} colors={colors} />
            }
            ListFooterComponent={
              <VendorListFooter isLoading={loading} colors={colors} />
            }
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
            }}
          />
        )}
      </View>
    </View>
  );
};

export default ReturnVendorItemsScreen;
