import {View, Text, TextInput, Alert, Modal, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {Inventory} from '../../../server/types';
import {RouteProp} from '@react-navigation/native';
import AppButton from '../../../components/Inventory/AppButton';
import {
  getFormateDate,
  GetItemsParamsNoFilter,
  updateData,
} from '../../../utils/PublicHelper';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import {applyDefaults} from '../../../Validator/Inventory/Barcode';
import {Activate_Book} from '../../../Types/Lottery/Lottery_Types';
import {applyDefaultsActivateBook} from '../../../Validator/Lottery/Lottery_Validator';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {Backround} from '../../../constants/Color';

type BarcodeScreenRouteProp = RouteProp<any, 'ChangeLocation'>;
const LOT_ChangeLocation: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [book, setBooks] = useState<Inventory>(route.params?.ItemData || []);
  const [exist, setExists] = useState<Inventory>();
  const [location, setLocation] = useState(route.params?.ItemData?.Location);
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [isConfirm, setIsConfirm] = useState<boolean>(true);

  const confirmChangeLocation = async () => {
    if (!currentBarcode || currentBarcode === '' || currentBarcode === null) {
      Alert.alert('Must Fill the Details');
    } else {
      const gameDetails = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getLocationExist/:Dept_ID/:Location',
        setExists,
        {Dept_ID: book.Dept_ID, Location: currentBarcode},
      );

      if (!gameDetails || gameDetails === undefined) {
        const inventoryData: Partial<Inventory> = {
          ItemNum: book.ItemNum,
          ItemName: book.ItemName,
          Dept_ID: book.Dept_ID,
          Cost: book.Cost,
          Price: book.Price,
          Retail_Price: book.Retail_Price,
          In_Stock: book.In_Stock,
          Date_Created: book.Date_Created,
          Last_Sold: book.Last_Sold,
          Location: currentBarcode,
          Vendor_Number: book.Vendor_Number,
          Vendor_Part_Num: book.Vendor_Part_Num,
          Reorder_Level: book.Reorder_Level,
          Reorder_Quantity: book.Reorder_Quantity,
          ReOrder_Cost: book.ReOrder_Cost,
          Unit_Size: book.Unit_Size,
          Unit_Type: book.Unit_Type,
          FoodStampable: book.FoodStampable,
          Tax_1: book.Tax_1[0],
          Tax_2: book.Tax_2[0],
          Tax_3: book.Tax_3[0],
          Tax_4: book.Tax_4[0],
          Tax_5: book.Tax_5[0],
          Tax_6: book.Tax_6[0],
          Check_ID: book.Check_ID,
          Check_ID2: book.Check_ID2,
          Store_ID: book.Store_ID,
          ItemName_Extra: book.ItemName_Extra,
        };

        const applyDefault = applyDefaults(inventoryData);
        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });
        if (result) {
          const getBookDetails = await GetItemsParamsNoFilter(
            (await getLotteryPort()).toString(),
            '/GetShiftBookTickets/:ItemNum/:Location',
            setExists,
            {ItemNum: book.ItemNum, Location: book.Location},
          );
          const bookData: Partial<Activate_Book> = {
            Book_No: getBookDetails[0].Book_No,
            Book_Created: getBookDetails[0].Book_Created,
            Book_Tickets: getBookDetails[0].Book_Tickets,
            CreatedBy: getBookDetails[0].CreatedBy,
            Location: currentBarcode,
            ItemNum: getBookDetails[0].ItemNum,
          };

          const applyDefault = applyDefaultsActivateBook(bookData);
          const result = await updateData<Activate_Book>({
            baseURL: (await getLotteryPort()).toString(),
            data: applyDefault,
            endpoint: '/updateactivatebooks',
          });
          if (result) {
            Alert.alert('Location Updated Success!');
            setLocation(currentBarcode);
            navigation.goBack();
          } else {
            Alert.alert('Not Updated Book');
          }
        } else {
          Alert.alert('Not Updated Inventory');
        }
      } else {
        Alert.alert('Location Already in Use');
      }
    }
  };

  return (
    <View>
      <Header NavName="Change Location" />
      <View style={{paddingHorizontal: 10}}>
        <View style={{paddingHorizontal: 10}}>
          <Text style={{fontSize: 20, fontWeight: 'bold'}}>
            Game Name #: {book.ItemName}
          </Text>
          <Text style={{fontSize: 20, fontWeight: 'bold'}}>
            Current Location #: {location}
          </Text>
        </View>

        <TextInput
          placeholder="Change Location"
          editable={isConfirm}
          onChangeText={value => setCurrentBarcode(value)}
          value={currentBarcode}
          style={{
            borderColor: 'red',
            borderRadius: 10,
            borderWidth: 2,
            padding: 10,
            width: '100%',
            fontSize: 18,
            marginTop: 30,
          }}
        />

        <View style={{marginTop: 20}}>
          <AppButton
            Title={'Confirm'}
            OnPress={() => confirmChangeLocation()}
          />
        </View>
      </View>
    </View>
  );
};

export default LOT_ChangeLocation;
