import React, {memo} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Inventory_Filter} from '../../../server/types';
import {Primary} from '../../../constants/Color';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';

interface CountInventoryItemProps {
  item: Inventory_Filter;
  onPress: () => void;
  inputValue: string;
  hasInputValue: boolean;
}

const CountInventoryItem: React.FC<CountInventoryItemProps> = ({
  item,
  onPress,
  inputValue,
  hasInputValue,
}) => {
  const colors = useThemeColors();

  const handlePress = () => {
    console.log('CountInventoryItem onPress called for item:', item.ItemNum);
    onPress();
  };

  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: hp('1%'),
      paddingHorizontal: wp('2%'),
      borderBottomWidth: 0.5,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
      borderRadius: 6,
      marginBottom: 4,
      justifyContent: 'space-between',
    },
    leftContent: {
      flexDirection: 'row',
      alignItems: 'center',
      width: wp('70%'),
    },
    iconContainer: {
      backgroundColor: colors.primary + '30',
      paddingHorizontal: 5,
      paddingVertical: 5,
      borderRadius: 30,
    },
    itemDetails: {
      gap: 2,
      marginLeft: 8,
    },
    itemName: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    metadataRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoGroup: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 10,
    },
    stockText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestMedium,
      color: colors.primary,
      marginLeft: 3,
    },
    barcodeText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.textSecondary,
      marginLeft: 3,
    },
    priceContainer: {
      backgroundColor: colors.primary + '30',
      paddingHorizontal: 10,
      paddingVertical: 5,
      borderRadius: 10,
    },
    priceText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
    },
  });

  return (
    <TouchableOpacity
      style={[
        styles.row,
        hasInputValue && {borderLeftWidth: 2, borderLeftColor: Primary},
      ]}
      onPress={handlePress}>
      <View style={styles.leftContent}>
        <View style={styles.iconContainer}>
          <MaterialCommunityIcons
            name="package-variant"
            color={MaterialColors.primary.main}
            size={hp('1.9%')}
          />
        </View>
        <View style={styles.itemDetails}>
          <Text style={styles.itemName}>{item.ItemName}</Text>
          <View style={styles.metadataRow}>
            <View style={styles.infoGroup}>
              <MaterialCommunityIcons
                name="warehouse"
                color={Primary}
                size={hp('1.6%')}
              />
              {!hasInputValue ? (
                <Text style={styles.stockText}>{item.In_Stock} in Stock</Text>
              ) : (
                <>
                  {inputValue !== '' ? (
                    <Text style={styles.stockText}>
                      {`${item.In_Stock} → ${inputValue}`}
                    </Text>
                  ) : (
                    <Text style={styles.stockText}>
                      {item.In_Stock} in Stock
                    </Text>
                  )}
                </>
              )}
            </View>
            <View style={styles.infoGroup}>
              <MaterialCommunityIcons
                name="barcode"
                color="#A1A1A1"
                size={hp('1.6%')}
              />
              <Text style={styles.barcodeText}>{item.ItemNum}</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.priceContainer}>
        <Text style={styles.priceText}>${item.Price}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default memo(CountInventoryItem, (prevProps, nextProps) => {
  // Only re-render if essential props change
  return (
    prevProps.item.ItemNum === nextProps.item.ItemNum &&
    prevProps.inputValue === nextProps.inputValue &&
    prevProps.hasInputValue === nextProps.hasInputValue
  );
});
