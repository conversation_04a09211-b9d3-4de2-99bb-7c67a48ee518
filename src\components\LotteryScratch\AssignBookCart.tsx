import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

// Enhanced colors
const EnhancedColors = {
  primary: {
    main: '#2563EB',
    light: '#DBEAFE',
  },
  error: {
    main: '#EF4444',
    background: '#FEE2E2',
  },
  text: {
    primary: '#1F2937',
    secondary: '#4B5563',
    onDark: '#FFFFFF',
  },
  surface: '#FFFFFF',
  grey: {
    200: '#E5E7EB',
  },
};

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

type Props = {
  Onpress: () => void;
  ShiftID: string;
  CashierID: string;
  CashierName?: string;
  Date: string;
  IsShiftReport?: boolean;
};

const AssignBookCart = ({
  Onpress,
  ShiftID,
  CashierID,
  Date,
  CashierName,
  IsShiftReport = false,
}: Props) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    cardContainer: {
      backgroundColor: colors.card,
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1.2%'),
      borderRadius: 8,
      marginVertical: hp('0.5%'),
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
    },
    flaggedCard: {
      borderLeftColor: colors.error,
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: hp('0.8%'),
    },
    headerIcon: {
      marginRight: 4,
    },
    shiftIdContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    shiftIdText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    flagContainer: {
      backgroundColor: colors.error + '20', // Adding transparency
      padding: 5,
      borderRadius: 14,
      marginLeft: wp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
    },
    dateChip: {
      backgroundColor: colors.primary,
      paddingHorizontal: wp('1.8%'),
      paddingVertical: hp('0.4%'),
      borderRadius: 12,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateIcon: {
      marginRight: 3,
    },
    dateChipText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: '#FFFFFF',
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginBottom: hp('0.8%'),
    },
    detailsContainer: {
      gap: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoIcon: {
      marginRight: 4,
    },
    infoLabel: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
      marginRight: 4,
    },
    infoValue: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    warningBadge: {
      backgroundColor: colors.error + '20', // Adding transparency
      paddingVertical: hp('0.4%'),
      paddingHorizontal: wp('1.5%'),
      borderRadius: 4,
      marginTop: hp('0.6%'),
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
    },
    warningText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.error,
    },
  });

  return (
    <TouchableOpacity
      style={[styles.cardContainer, IsShiftReport && styles.flaggedCard]}
      activeOpacity={0.7}
      onPress={Onpress}>
      <View style={styles.headerRow}>
        <View style={styles.shiftIdContainer}>
          <MaterialIcons
            name="assignment"
            size={18}
            color={colors.primary}
            style={styles.headerIcon}
          />
          <Text style={styles.shiftIdText}>Shift ID: {ShiftID}</Text>
          {IsShiftReport && (
            <View style={styles.flagContainer}>
              <FontAwesome name="flag" color={colors.error} size={16} />
            </View>
          )}
        </View>
        <View style={styles.dateChip}>
          <MaterialIcons
            name="event"
            size={12}
            color="#FFFFFF"
            style={styles.dateIcon}
          />
          <Text style={styles.dateChipText}>End Shift: {Date}</Text>
        </View>
      </View>

      <View style={styles.divider} />

      <View style={styles.detailsContainer}>
        <View style={styles.infoRow}>
          <MaterialIcons
            name="person"
            size={16}
            color={colors.primary}
            style={styles.infoIcon}
          />
          <Text style={styles.infoLabel}>Cashier ID:</Text>
          <Text style={styles.infoValue}>{CashierID}</Text>
        </View>

        {CashierName && (
          <View style={styles.infoRow}>
            <MaterialIcons
              name="badge"
              size={16}
              color={colors.primary}
              style={styles.infoIcon}
            />
            <Text style={styles.infoLabel}>Cashier:</Text>
            <Text style={styles.infoValue}>{CashierName}</Text>
          </View>
        )}
      </View>

      {IsShiftReport && (
        <View style={styles.warningBadge}>
          <MaterialIcons
            name="warning"
            size={16}
            color={colors.error}
            style={{marginRight: 6}}
          />
          <Text style={styles.warningText}>This shift requires attention</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default AssignBookCart;
