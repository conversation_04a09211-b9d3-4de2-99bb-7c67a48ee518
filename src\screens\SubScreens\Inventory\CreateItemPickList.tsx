import {View, Text, Alert} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Backround} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  Get_Max_ID,
  Invoice_OnHold,
  Invoice_Totals,
} from '../../../server/types';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  createData,
  GetAllItemsWithFilter,
  GetCommonLatestID,
  getCurrentDateTime,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsVerification,
  GetItemsWithParams,
  GetLatestID,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {
  applyDefaultsInvoiceOnHold,
  applyDefaultsInvoiceTotals,
} from '../../../Validator/Inventory/Barcode';
import {useFocusEffect} from '@react-navigation/native';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const validationSchema = Yup.object().shape({
  onHoldID: Yup.string().required('Refrence Number is required'),
});

const CreateItemPickList: React.FC<NavProps> = ({navigation}) => {
  const [invoicenum, setInvoicenum] = useState<Get_Max_ID[]>();

  useFocusEffect(
    useCallback(() => {
      getInvocieID();
    }, []),
  );
  const getInvocieID = async () => {
    GetLatestID(
      (await getInventoryPort()).toString(),
      setInvoicenum,
      '/getinvoiceID',
    );
  };

  const CreateNewPickList = async (Values: any): Promise<void> => {
    const invNum = await GetCommonLatestID(
      (await getInventoryPort()).toString(),
      '/getinvoiceID',
    );

    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const stationId = await AsyncStorage.getItem('STATIONID');
    const ValidStation = stationId === null ? '1001' : stationId;

    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: Number(invNum),
        ReferenceInvoiceNumber: invNum?.toString(),
        Orig_OnHoldID: Values.onHoldID,
        Store_ID: ValidStore,
        Cashier_ID: ValideCashier,
        CustNum: '101',
        DateTime: getFormateDate(Date()),
        Total_Price: 0,
        Total_Cost: 0,
        Total_Tax1: 0,
        Grand_Total: 0,
        Station_ID: ValidStation,
        Payment_Method: 'CA',
        Status: 'O',
        Taxed_1: -1,
        Taxed_Sales: 0,
        Dirty: true,
        CourseOrderingProgress: '0.1',
        Total_UndiscountedSale: 0,
      };

      const getExists = await GetItemsVerification(
        (await getInventoryPort()).toString(),
        '/GetInvoiceTotalExists/:Orig_OnHoldID',
        {Orig_OnHoldID: Values.onHoldID},
      );

      if (getExists) {
        showAlertOK(
          `The On Hold ID You Have Chosen is Already in Use. All On Hold ID's Must Be Unique. Please Try Again.`,
          'Already In Use',
        );
      } else {
        const applyDefult = applyDefaultsInvoiceTotals(pickListData);

        const result = await createData<Invoice_Totals>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefult,
          endpoint: '/createinvoicetotal',
        });

        if (result) {
          await CreateOnHoldItems(Values, applyDefult, Number(invNum));
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const CreateOnHoldItems = async (
    values: any,
    InvoiceTotal: Invoice_Totals,
    InvNum: number,
  ): Promise<void> => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const stationId = await AsyncStorage.getItem('STATIONID');
    const ValidStation = stationId === null ? '1001' : stationId;

    try {
      const pickListData: Partial<Invoice_OnHold> = {
        Invoice_Number: InvNum,
        OnHoldID: values.onHoldID,
        Cashier_ID: ValideCashier,
        Store_ID: ValidStore,
        Station_ID: ValidStation,
      };

      const applyDefult = applyDefaultsInvoiceOnHold(pickListData);
      const result = await createData<Invoice_OnHold>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/createonhold',
      });

      if (result) {
        navigation.navigate('AddItemPickList', {
          ItemData: InvoiceTotal,
          VALIDREF: values.onHoldID,
        });
      } else {
      }
    } catch (error) {
      console.log(error);
    }
  };

  const initialValues = {
    onHoldID: '',
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  return (
    <View
      style={{
        backgroundColor: colors.background, // Changed from Backround
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Create Pick List" />
      <Formik
        initialValues={initialValues}
        enableReinitialize={true} // Enable reinitialization
        validationSchema={validationSchema}
        onSubmit={async values => {
          const invoiceTotal = await CreateNewPickList(values);
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View>
            <AppTextInput
              PlaceHolder="Refrence Holder (required)"
              Title="Refrence Number"
              Value={values.onHoldID}
              onChangeText={handleChange('onHoldID')}
              onBlur={handleBlur('onHoldID')}
              error={errors.onHoldID}
              touched={touched.onHoldID}
              maxLength={12}
            />
            <AppButton Title="Add PickList" OnPress={handleSubmit} />
          </View>
        )}
      </Formik>
    </View>
  );
};

export default CreateItemPickList;
