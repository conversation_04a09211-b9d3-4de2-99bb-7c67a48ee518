import {View, Text} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../../constants/Color';
import Header from '../../../../components/Inventory/Header';
import DataList from '../../../../components/Inventory/AppList';
import {Inventory_Filter, Invoice_Itemized} from '../../../../server/types';
import DashBoardItemCart from '../../../../components/Inventory/DashBoardItemCart';
import {GetAllItems, handleSearch} from '../../../../utils/PublicHelper';
import {getInventoryPort} from '../../../../server/InstanceTypes';
import {useFocusEffect} from '@react-navigation/native';
import Search from '../../../../components/Inventory/Search';
import {TextInput} from 'react-native-gesture-handler';
import {Fonts} from '../../../../styles/fonts';
import {useThemeColors} from '../../../../Theme/useThemeColors';
import {useTheme} from '../../../../Theme/ThemeContext';

const ViewAllTransaction = () => {
  const [inventoryData, setInventoryData] = useState<Invoice_Itemized[]>([]);
  const [filteredData, setFilteredData] = useState<Invoice_Itemized[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      getAllTrans();
    }, []),
  );
  const getAllTrans = async () => {
    GetAllItems<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getLatestSoldItems',
      setInventoryData,
      setLoading,
      false,
    );
    GetAllItems<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getLatestSoldItems',
      setFilteredData,
      setLoading,
      false,
    );
  };
  const renderItem = ({item}: {item: Invoice_Itemized}) => (
    <View style={{marginVertical: hp('0.5%')}}>
      <DashBoardItemCart
        Name={item.DiffItemName}
        Department={item.Quantity}
        Price={item.PricePer.toFixed(2)}
        Date={item.ItemNum}
      />
    </View>
  );
  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      inventoryData,
      ['ItemNum', 'DiffItemName'],
      setFilteredData,
      setLoading,
    );
  };

  return (
    <View
      style={{
        height: hp('100%'),
        width: wp('100%'),
        backgroundColor: colors.background, // Changed from Backround
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="View All Transaction History" />
      <Search
        textInputRef={textInputRef}
        PlaceHolder="Search..."
        Value={searchQuery}
        onChange={onSearchChange}
        keyboardON={true}
      />
      <Text
        style={{
          fontFamily: Fonts.OnestBold,
          fontSize: hp('1.9%'),
          color: colors.textSecondary, // Changed from SecondaryHint
          paddingVertical: hp('1%'),
        }}>
        Total Transaction: {filteredData.length}
      </Text>

      <View style={{height: hp('71%')}}>
        <DataList
          data={filteredData}
          renderItem={renderItem}
          loading={loading}
        />
      </View>
    </View>
  );
};

export default ViewAllTransaction;
