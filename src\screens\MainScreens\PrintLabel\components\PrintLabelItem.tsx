import React, {memo} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../../styles/fonts';
import {Inventory_Filter} from '../../../../server/types';
import Icon from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface PrintLabelItemProps {
  item: Inventory_Filter;
  onPress: () => void;
  inputValue: string;
  action: boolean;
  onClearItem: () => void;
  colors: any;
  isDark: boolean;
}

const PrintLabelItem = memo(
  ({
    item,
    onPress,
    inputValue,
    action,
    onClearItem,
    colors,
    isDark,
  }: PrintLabelItemProps) => {
    const hasInputValue = inputValue !== '';

    return (
      <TouchableOpacity onPress={onPress}>
        <View
          style={[
            {
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: hp('0.8%'),
              paddingHorizontal: wp('2%'),
              borderBottomWidth: 0.5,
              borderBottomColor: colors.border,
              backgroundColor: isDark ? colors.surface : colors.background,
              borderRadius: 6,
              marginBottom: 4,
            },
            hasInputValue && {
              borderLeftWidth: 2,
              borderLeftColor: colors.primary,
            },
          ]}>
          {hasInputValue && (
            <TouchableOpacity
              style={{
                backgroundColor: colors.error,
                borderRadius: 20,
                width: 16,
                height: 16,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 4,
              }}
              onPress={onClearItem}>
              <Icon name="minus" size={8} color="#FFFFFF" />
            </TouchableOpacity>
          )}

          {/* Added icon container with printer-related icon */}
          <View
            style={{
              marginRight: wp('1.5%'),
              justifyContent: 'center',
              alignItems: 'center',
              width: wp('7%'),
              height: wp('7%'),
              borderRadius: wp('3.5%'),
              backgroundColor: colors.primary + '15',
            }}>
            <MaterialIcons
              name="print"
              size={wp('3.8%')}
              color={colors.primary}
            />
          </View>

          <View
            style={{
              flex: 1,
              gap: 1,
            }}>
            <Text
              style={{
                fontSize: wp('3%'),
                fontFamily: Fonts.OnestBold,
                color: colors.text,
                marginBottom: 1,
              }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {item.ItemName}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              {/* Added icon to price */}
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: wp('2%'),
                }}>
                <MaterialIcons
                  name="attach-money"
                  size={wp('2.2%')}
                  color={colors.textSecondary}
                />
                <Text
                  style={{
                    fontSize: FontSizes.small,
                    fontFamily: Fonts.OnestMedium,
                    color: colors.textSecondary,
                    marginRight: wp('2%'),
                  }}>
                  {item.Price}
                </Text>
              </View>

              {!action ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: wp('0.5%'),
                  }}>
                  <MaterialIcons
                    name="storage"
                    size={wp('2.2%')}
                    color={colors.primary}
                  />
                  <Text
                    style={{
                      fontSize: FontSizes.small,
                      fontFamily: Fonts.OnestMedium,
                      color: colors.primary,
                    }}>
                    In: {item.In_Stock}
                  </Text>
                </View>
              ) : (
                <>
                  {inputValue !== '' ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: wp('0.5%'),
                        }}>
                        <MaterialIcons
                          name="storage"
                          size={wp('2.2%')}
                          color={colors.primary}
                        />
                        <Text
                          style={{
                            fontSize: FontSizes.small,
                            fontFamily: Fonts.OnestMedium,
                            color: colors.primary,
                          }}>
                          In Stock: {item.In_Stock}
                        </Text>
                      </View>
                      <Text
                        style={{
                          fontSize: FontSizes.small,
                          color: colors.textSecondary,
                          marginHorizontal: wp('1%'),
                        }}>
                        •
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: wp('0.5%'),
                        }}>
                        <MaterialIcons
                          name="qr-code"
                          size={wp('2.2%')}
                          color={colors.primary}
                        />
                        <Text
                          style={{
                            fontSize: FontSizes.small,
                            fontFamily: Fonts.OnestMedium,
                            color: colors.primary,
                          }}>
                          No Of Labels: {inputValue}
                        </Text>
                      </View>
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: wp('0.5%'),
                      }}>
                      <MaterialIcons
                        name="storage"
                        size={wp('2.2%')}
                        color={colors.primary}
                      />
                      <Text
                        style={{
                          fontSize: FontSizes.small,
                          fontFamily: Fonts.OnestMedium,
                          color: colors.primary,
                        }}>
                        In Stock: {item.In_Stock}
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if item, input value, or theme changes
    return (
      prevProps.item.ItemNum === nextProps.item.ItemNum &&
      prevProps.inputValue === nextProps.inputValue &&
      prevProps.action === nextProps.action &&
      prevProps.isDark === nextProps.isDark &&
      JSON.stringify(prevProps.colors) === JSON.stringify(nextProps.colors)
    );
  },
);

export default PrintLabelItem;
