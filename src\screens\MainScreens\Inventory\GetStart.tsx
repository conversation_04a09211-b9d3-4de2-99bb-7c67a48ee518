import { View, Text } from 'react-native'
import React from 'react'
import AppButton from '../../../components/Inventory/AppButton'
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type NavProps = {
    navigation: NativeStackNavigationProp<any>; 
  };

const GetStart: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{justifyContent:'space-between', flex: 1, marginVertical: 10, marginHorizontal: 10}}>
      <Text>GetStart</Text>
      <View >
        <AppButton Title='Get Start' OnPress={() =>navigation.navigate("Login")}/>
      </View>
    </View>
  )
}

export default GetStart