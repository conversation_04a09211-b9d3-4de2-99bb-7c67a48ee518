import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TextInput,
  Keyboard,
  Platform,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Header from '../../../components/Inventory/Header';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  Department,
  Inventory,
  Inventory_Filter,
  Invoice_Itemized,
  Invoice_Totals,
  Vendor,
  BrandOrSubCategory,
  Brands,
  SubCategories,
} from '../../../server/types';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import LottieView from 'lottie-react-native';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type AddItemsToListScreenRouteProp = RouteProp<any, 'AddItemsToListScreen'>;

interface AddItemsToListScreenProps {
  route: AddItemsToListScreenRouteProp;
  navigation: NativeStackNavigationProp<any>;
}

const AddItemsToListScreen: React.FC<AddItemsToListScreenProps> = ({
  route,
  navigation,
}) => {
  // Get params from route
  const pickListItem = route.params?.pickListItem;
  const invoiceItem = route.params?.invoiceItem || [];

  // State variables
  const [loading, setLoading] = useState<boolean>(false);
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);

  // Pagination states
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);

  // Filter states
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, SetBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);

  // Refs
  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  // Theme
  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Check if item exists in invoice
  const isItemNumExist = (itemNum: string | undefined) => {
    if (!itemNum) return false;
    return invoiceItem.some(
      (item: Invoice_Itemized) => item.ItemNum === itemNum,
    );
  };

  // Add item to pick list callback
  const addItemsToPickList = useCallback(
    (inventory: Inventory) => {
      const isExists = isItemNumExist(inventory?.ItemNum);

      if (isExists) {
        // Show alert that item already exists
        navigation.navigate('AddItemPickList', {
          ItemData: pickListItem,
          VALIDREF: pickListItem?.ReferenceInvoiceNumber,
        });
      } else {
        navigation.navigate('AddPickListEachItems', {
          ItemData: inventory,
          PickItem: pickListItem,
        });
      }
    },
    [invoiceItem, navigation, pickListItem],
  );

  // Inventory Modal Item Component
  const InventoryModalItem = memo(
    ({
      item,
      onPress,
    }: {
      item: Inventory_Filter;
      onPress: (item: Inventory) => void;
    }) => {
      const isExists = isItemNumExist(item?.ItemNum);

      return (
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: hp('1%'),
            paddingHorizontal: wp('2%'),
            borderBottomWidth: 0.5,
            borderBottomColor: colors.border,
            backgroundColor: colors.card,
            borderRadius: 6,
            marginBottom: 4,
            justifyContent: 'space-between',
            opacity: isExists ? 0.5 : 1,
          }}
          onPress={() => onPress(item as Inventory)}
          disabled={isExists}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            }}>
            <View
              style={{
                backgroundColor: colors.primary + '30',
                paddingHorizontal: 5,
                paddingVertical: 5,
                borderRadius: 30,
              }}>
              <MaterialCommunityIcons
                name="package-variant"
                color={MaterialColors.primary.main}
                size={hp('1.6%')}
              />
            </View>
            <View
              style={{
                gap: 2,
                marginLeft: 8,
                flex: 1,
              }}>
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: Fonts.OnestBold,
                  color: colors.text,
                }}>
                {item.ItemName}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginRight: 10,
                  }}>
                  <Text
                    style={{
                      fontSize: 11,
                      fontFamily: Fonts.OnestBold,
                      color: colors.textSecondary,
                      marginLeft: 3,
                    }}>
                    {item.ItemNum}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <View
            style={{
              backgroundColor: colors.primary + '30',
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 8,
              marginLeft: 8,
            }}>
            <Text
              style={{
                fontSize: 11,
                fontFamily: Fonts.OnestBold,
                color: colors.primary,
              }}>
              ${item.Price?.toFixed(2)}
            </Text>
          </View>
        </TouchableOpacity>
      );
    },
    (prevProps, nextProps) => {
      return prevProps.item.ItemNum === nextProps.item.ItemNum;
    },
  );

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={[styles.emptyText, {color: colors.text}]}>
        {searchQuery ? 'No items found for your search' : 'No items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  });

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * pageSize;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more function
  const handleLoadMore = useCallback(() => {
    if (page * pageSize < filteredData.length && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, pageSize, filteredData, loading]);

  // Apply pagination when filtered data changes
  useEffect(() => {
    if (filteredData.length > 0) {
      applyPagination(filteredData, page);
    }
  }, [filteredData, page]);

  // Apply filtering based on selected filters (same as LookupItems.tsx)
  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply search debouncing (same as LookupItems.tsx)
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Apply filters and search (same as LookupItems.tsx)
  useEffect(() => {
    // Skip if inventoryData isn't loaded yet
    if (inventoryData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = inventoryData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    setFilteredData(filtered);
    applyPagination(filtered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    inventoryData,
  ]);

  // Get inventory data
  const getnoPage = async () => {
    setPage(1);
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/inventorynopg',
      (data: Inventory_Filter[]) => {
        setInventoryData(data);
        setFilteredData(data);
        applyPagination(data, 1);
      },
      setFilteredData,
      setLoading,
      false,
    );
  };

  // Toggle lookup function
  const toggleLookup = useCallback(
    (value: boolean) => {
      setshowLookup(value);
      applyPagination(filteredData, page);
      setSearchQuery('');
      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling - Enhanced for PDA scanner focus
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        setSearchQuery('');
        onChangeAddItems('');
        Keyboard.dismiss();
        // Ensure focus is restored for scanner readiness
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 200);
      }
    },
    [filteredData, page],
  );

  // Handle done click
  const handleDoneClickSub = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();

    // Enhanced focus restoration for PDA scanner readiness
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);

    applyPagination(filteredData, page);
  };

  // Search function
  const onChangeAddItems = async (text: string) => {
    setCameraModal(false);
    setSearchQuery(text);

    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          filteredData as any[],
          ['ItemName', 'ItemNum'],
          setDisplayData as any,
          setLoading,
        );
      } else {
        // Check if the item exists in displayData (inventory)
        const foundInInventory = displayData.find(
          item =>
            item.ItemName?.toLowerCase() === text.toLowerCase() ||
            item.ItemNum?.toLowerCase() === text.toLowerCase(),
        );

        if (foundInInventory) {
          // Item found in inventory, now check if it already exists in invoice items
          const isExists = isItemNumExist(foundInInventory?.ItemNum);

          if (isExists) {
            // Item already exists in invoice items, show alert
            showAlertOK('Item already exists in the pick list!')
              .then(() => {
                // Reset UI after showing alert
                if (textInputRef.current) {
                  textInputRef.current.clear();
                  textInputRef.current.blur();
                }

                setSearchQuery('');
                setshowLookup(false);
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef.current) {
                    textInputRef.current.focus();
                  }
                }, 200);
              })
              .catch(error => {
                console.error('Error showing alert', error);
              });
          } else {
            // Item exists in inventory but not in invoice items, navigate to add it
            navigation.navigate('AddPickListEachItems', {
              ItemData: foundInInventory,
              PickItem: pickListItem,
            });
          }
        } else {
          // Item not found in displayData, try to get it from API
          const getBarcode =
            await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
              (await getInventoryPort()).toString(),
              '/inventory/:ItemNum',
              {ItemNum: text},
            );

          if (
            getBarcode &&
            Array.isArray(getBarcode) &&
            getBarcode.length > 0
          ) {
            // Item found in API, check if it exists in invoice items
            const isExists = isItemNumExist(getBarcode[0]?.ItemNum);

            if (isExists) {
              // Item already exists in invoice items, show alert
              showAlertOK('Item already exists in the pick list!')
                .then(() => {
                  // Reset UI after showing alert
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                })
                .catch(error => {
                  console.error('Error showing alert', error);
                });
            } else {
              // Item exists in API but not in invoice items, navigate to add it
              navigation.navigate('AddPickListEachItems', {
                ItemData: getBarcode[0],
                PickItem: pickListItem,
              });
            }
          } else {
            // Item not found anywhere, show alert for creating new item
            showAlert('Item not found. Do you want to create a new item?')
              .then(async result => {
                if (result) {
                  // Clear the input before navigation to prevent any issues
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                  }
                  setSearchQuery('');

                  navigation.navigate('ItemType', {ItemData: text});
                } else {
                  // Reset UI if user cancels
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                }
              })
              .catch(error => {
                console.error('Error showing alert', error);
              });
          }
        }
      }
    } else {
      setDisplayData(filteredData);
    }
  };

  // Load data on focus
  useFocusEffect(
    useCallback(() => {
      getnoPage();
    }, []),
  );

  // Auto focus text input when screen comes into focus (for PDA scanner readiness)
  useFocusEffect(
    useCallback(() => {
      const focusTimer = setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.focus();
        }
      }, 300); // Small delay to ensure screen transition is complete

      return () => clearTimeout(focusTimer);
    }, []),
  );

  // Additional focus management to ensure input stays focused for scanner readiness
  useEffect(() => {
    const focusTimer = setTimeout(() => {
      if (textInputRef.current && !searchQuery) {
        textInputRef.current.focus();
      }
    }, 100);

    return () => clearTimeout(focusTimer);
  }, [displayData, filteredData]); // Re-focus when data changes

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={{width: '95%'}}>
        <Header
          NavName="Add Items"
          Onpress={() =>
            navigation.navigate('AddItemPickList', {
              ItemData: pickListItem,
              VALIDREF: pickListItem?.ReferenceInvoiceNumber,
            })
          }
          isProvid={true}
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCameraModal(!cameraModal);
          }}
        />
      </View>

      <View style={{paddingBottom: hp('1%')}}>
        <AppSearchWIthFilter
          OnSearch={onChangeAddItems}
          SearchValue={searchQuery}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={isEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={() => handleDoneClickSub()}
        />

        <Text
          style={{
            fontSize: FontSizes.medium,
            fontFamily: Fonts.OnestBold,
            color: colors.text,
            paddingVertical: hp('1%'),
          }}>{`Total Items: (${filteredData.length || 0})`}</Text>
      </View>

      <View style={{flex: 1}}>
        <FlatList
          ref={flatListRef}
          data={displayData}
          renderItem={({item}) => (
            <InventoryModalItem item={item} onPress={addItemsToPickList} />
          )}
          keyExtractor={(item: Inventory_Filter) =>
            (item.ItemNum || '').toString()
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          getItemLayout={(_, index) => ({
            length: 88,
            offset: 88 * index,
            index,
          })}
          ListEmptyComponent={<EmptyListComponent searchQuery={searchQuery} />}
          ListFooterComponent={<ListFooter loading={loading} />}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={10}
          removeClippedSubviews={true}
          updateCellsBatchingPeriod={75}
          showsVerticalScrollIndicator={false}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
          }}
          contentContainerStyle={{
            paddingBottom: hp('2%'),
          }}
        />
      </View>

      {/* Filter Modal */}
      <AppFilter
        isVisible={filter}
        setIsVisble={setFilter}
        Department={text => setSelectedDepartment(text)}
        Vedor={text => setSelectedVendor(text)}
        Brand={text => setSelectedBrand(text)}
        Category={text => setSelectedSubCategory(text)}
        EnableFilter={setIsEnableFilter}
        selectedDepartment={selectedDepartment}
        selectedVendor={selectedVendor}
        selectedBrand={selectedBrand}
        selectedSubCategory={selectedSubCategory}
        isEnableFilter={isEnableFilter}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: wp('2.5%'),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp('10%'),
  },
  animationContainer: {
    width: wp('40%'),
    height: hp('20%'),
  },
  lottie: {
    width: '100%',
    height: '100%',
  },
  emptyText: {
    fontSize: FontSizes.medium,
    fontFamily: Fonts.OnestMedium,
    textAlign: 'center',
    marginTop: hp('2%'),
  },
  footer: {
    paddingVertical: hp('2%'),
    alignItems: 'center',
  },
});

export default AddItemsToListScreen;
