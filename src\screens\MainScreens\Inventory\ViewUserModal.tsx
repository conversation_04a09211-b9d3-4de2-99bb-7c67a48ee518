import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {Fonts} from '../../../styles/fonts';
import {getPermissionsFromStorage} from '../../../utils/permissionHelper';
import {
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {Employee, Lottery_Permissions} from '../../../server/types';
import {App_User} from '../../../Types/Lottery/Lottery_Types';
import {createItem, updateItem} from '../../../server/service';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../../constants/MaterialColors';

const PERMISSION_LABELS = {
  CFA_Inven_Add: 'Add Inventory',
  CFA_Inven_Edit: 'Edit Inventory',
  CFA_Vendors_Add: 'Add Vendors',
  CFA_Depts_Add: 'Add Department',
  CFA_Depts_Edit: 'Edit Department',
  CFA_INVEN_VIEW: 'View Inventory',
  CFA_HH_Create_PO: 'Create PO ',
  CFA_HH_DSD: 'Direct Store Delivery',
  CFA_HH_Inv_Count: 'Inventory Count',
  CFA_HH_PO_Receive: 'Receive PO ',
  CFA_HH_Inv_Adjust: 'Adjust Inventory',
  CFA_HH_PRINT_LABELS: 'Print Labels ',
};

const LOTTERY_PERMISSION_LABELS = {
  StartOrEndShift: 'Start/End Shift',
  ActivateBook: 'Activate Books',
  CreateNewGame: 'Create New Game',
  OrganizeSlot: 'Organize Slots',
  ViewShift: 'View Shift Report',
  ResetShift: 'Reset Shift',
  CFA_Inven_Add: 'Add Inventory',
  CFA_Inven_Edit: 'Edit Inventory',
  CFA_Vendors_Add: 'Add Vendors',
  CFA_Depts_Add: 'Add Department',
  CFA_Depts_Edit: 'Edit Department',
  CFA_INVEN_VIEW: 'View Inventory',
  CFA_HH_Create_PO: 'Create PO ',
  CFA_HH_DSD: 'Direct Store Delivery',
  CFA_HH_Inv_Count: 'Inventory Count',
  CFA_HH_PO_Receive: 'Return Vendor ',
  CFA_HH_Inv_Adjust: 'Adjust Inventory',
  CFA_HH_PRINT_LABELS: 'Print Labels ',
};

const PermissionGrid = ({title, permissions, state, setState, labels}) => {
  return (
    <View style={styles.permissionSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.permissionGrid}>
        {permissions.map((item, index) => (
          <View key={index} style={styles.permissionItem}>
            <CustomCheckbox
              isChecked={state[item] || false}
              onChange={value => setState(prev => ({...prev, [item]: value}))}
            />
            <Text style={styles.permissionLabel}>
              {labels[item] || item.replace('CFA_', '').replace('_', ' ')}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

interface ViewUserModalProps {
  visible: boolean;
  onClose: () => void;
  user: App_User;
}

const ViewUserModal: React.FC<ViewUserModalProps> = ({
  visible,
  onClose,
  user,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const [selectedEmployeeDetail, setSelectedEmployeeDetail] =
    useState<string>('');
  const [permissions, setPermissions] = useState({});
  const [storedPermissions, setStoredPermissions] = useState({});
  const [storedLotteryPermissions, setStoredLotteryPermissions] = useState({});
  const [lotteryPermissions, setLotteryPermissions] = useState({});
  const lotteryPermissionList = Object.keys(LOTTERY_PERMISSION_LABELS);
  const [allEmployee, setAllEmployee] = useState<Employee[]>([]);
  const [permissionList, setPermissionList] = useState<string[]>([]);

  // Keep the existing functionality
  useEffect(() => {
    const loadPermissions = async () => {
      const savedPermissions = await getPermissionsFromStorage();
      if (savedPermissions) {
        setStoredPermissions(savedPermissions);
      }
    };
    getAllEmployee();
    loadPermissions();
  }, []);

  const getAllEmployee = async () => {
    GetAllItems<Employee[]>(
      (await getInventoryPort()).toString(),
      '/getAllEmployee',
      setAllEmployee,
      setLoading,
    );
  };

  const loadLotteryPermissions = async (empID: string) => {
    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: user.Cashier_ID},
      );

      if (Array.isArray(getBarcode) && getBarcode.length > 0) {
        const employeePermissions = getBarcode[0]; // Extract the first object
        setLotteryPermissions({
          StartOrEndShift: employeePermissions.StartOrEndShift || false,
          ActivateBook: employeePermissions.ActivateBook || false,
          CreateNewGame: employeePermissions.CreateNewGame || false,
          OrganizeSlot: employeePermissions.OrganizeSlot || false,
          ViewShift: employeePermissions.ViewShift || false,
          ResetShift: employeePermissions.ResetShift || false,
          CFA_Inven_Add: employeePermissions.CFA_Inven_Add || false,
          CFA_Inven_Edit: employeePermissions.CFA_Inven_Edit || false,
          CFA_Vendors_Add: employeePermissions.CFA_Vendors_Add || false,
          CFA_Depts_Add: employeePermissions.CFA_Depts_Add || false,
          CFA_Depts_Edit: employeePermissions.CFA_Depts_Edit || false,
          CFA_INVEN_VIEW: employeePermissions.CFA_INVEN_VIEW || false,
          CFA_HH_Create_PO: employeePermissions.CFA_HH_Create_PO || false,
          CFA_HH_DSD: employeePermissions.CFA_HH_DSD || false,
          CFA_HH_Inv_Count: employeePermissions.CFA_HH_Inv_Count || false,
          CFA_HH_PO_Receive: employeePermissions.CFA_HH_PO_Receive || false,
          CFA_HH_Inv_Adjust: employeePermissions.CFA_HH_Inv_Adjust || false,
          CFA_HH_PRINT_LABELS: employeePermissions.CFA_HH_PRINT_LABELS || false,
        });
      } else {
        setLotteryPermissions({
          StartOrEndShift: false,
          ActivateBook: false,
          CreateNewGame: false,
          OrganizeSlot: false,
          ViewShift: false,
          ResetShift: false,
          CFA_Inven_Add: false,
          CFA_Inven_Edit: false,
          CFA_Vendors_Add: false,
          CFA_Depts_Add: false,
          CFA_Depts_Edit: false,
          CFA_INVEN_VIEW: false,
          CFA_HH_Create_PO: false,
          CFA_HH_DSD: false,
          CFA_HH_Inv_Count: false,
          CFA_HH_PO_Receive: false,
          CFA_HH_Inv_Adjust: false,
          CFA_HH_PRINT_LABELS: false,
        });
      }
    } catch (error) {
      console.error('Error loading lottery permissions:', error);
    }
  };

  useEffect(() => {
    if (user.Cashier_ID) {
      const selectedEmployee = allEmployee.find(
        emp => emp.Cashier_ID === user.Cashier_ID,
      );

      if (selectedEmployee) {
        setSelectedEmployeeDetail(selectedEmployee);
        const formattedPermissions = {};
        const extractedPermissions = [];

        Object.keys(PERMISSION_LABELS).forEach(key => {
          if (selectedEmployee.hasOwnProperty(key)) {
            formattedPermissions[key] = selectedEmployee[key] === 'Y';
            extractedPermissions.push(key);
          }
        });

        setPermissions(formattedPermissions);
        setPermissionList(extractedPermissions);
        loadLotteryPermissions(user.Cashier_ID);
      } else {
        setPermissions({});
        setPermissionList([]);
      }
    }
  }, [user.Cashier_ID, allEmployee]);

  const createLotteryPermission = async () => {
    const InventoryRef: Lottery_Permissions = {
      Emp_ID: user.Cashier_ID,
      Emp_Name:
        selectedEmployeeDetail.EmpName || selectedEmployeeDetail.name || 'N/A',
      StartOrEndShift: lotteryPermissions['StartOrEndShift'] || false,
      ActivateBook: lotteryPermissions['ActivateBook'] || false,
      CreateNewGame: lotteryPermissions['CreateNewGame'] || false,
      OrganizeSlot: lotteryPermissions['OrganizeSlot'] || false,
      ViewShift: lotteryPermissions['ViewShift'] || false,
      ResetShift: lotteryPermissions['ResetShift'] || false,
      CFA_Inven_Add: lotteryPermissions['CFA_Inven_Add'] || false,
      CFA_Inven_Edit: lotteryPermissions['CFA_Inven_Edit'] || false,
      CFA_Vendors_Add: lotteryPermissions['CFA_Vendors_Add'] || false,
      CFA_Depts_Add: lotteryPermissions['CFA_Depts_Add'] || false,
      CFA_Depts_Edit: lotteryPermissions['CFA_Depts_Edit'] || false,
      CFA_INVEN_VIEW: lotteryPermissions['CFA_INVEN_VIEW'] || false,
      CFA_HH_Create_PO: lotteryPermissions['CFA_HH_Create_PO'] || false,
      CFA_HH_DSD: lotteryPermissions['CFA_HH_DSD'] || false,
      CFA_HH_Inv_Count: lotteryPermissions['CFA_HH_Inv_Count'] || false,
      CFA_HH_PO_Receive: lotteryPermissions['CFA_HH_PO_Receive'] || false,
      CFA_HH_Inv_Adjust: lotteryPermissions['CFA_HH_Inv_Adjust'] || false,
      CFA_HH_PRINT_LABELS: lotteryPermissions['CFA_HH_PRINT_LABELS'] || false,
    };

    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: user.Cashier_ID},
      );

      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        const result = await createItem(
          (await getLotteryPort()).toString(),
          '/createlotpermission',
          InventoryRef,
        );
        if (result) {
          Alert.alert('Success', 'Permission Updated!', [
            {text: 'OK', onPress: () => onClose()},
          ]);
        }
      } else {
        const result = await updateItem(
          (await getLotteryPort()).toString(),
          '/updatelotpermission',
          InventoryRef,
        );
        if (result) {
          Alert.alert('Success', 'Permission Updated!', [
            {text: 'OK', onPress: () => onClose()},
          ]);
        }
      }
    } catch (error) {
      console.error('Error saving lottery permissions:', error);
      Alert.alert('Error', 'Failed to update permissions. Please try again.');
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Header with title and close button */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>User Permissions</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <MaterialCommunityIcons
                name="close"
                size={20}
                color={MaterialColors.text.secondary}
              />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            <View style={styles.modalBody}>
              <View style={styles.userInfoContainer}>
                <Text style={styles.userName}>
                  {user.Emp_Name}{' '}
                  <Text style={styles.userRole}>({user.Role})</Text>
                </Text>
              </View>

              <PermissionGrid
                title="App Permissions"
                permissions={lotteryPermissionList}
                state={lotteryPermissions}
                setState={setLotteryPermissions}
                labels={LOTTERY_PERMISSION_LABELS}
              />
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={[styles.button, styles.outlineButton]}
              onPress={onClose}>
              <Text style={styles.outlineButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={createLotteryPermission}>
              <Text style={styles.buttonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxWidth: 500,
    backgroundColor: MaterialColors.surface,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[200],
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: MaterialColors.text.primary,
  },
  closeButton: {
    padding: 6,
  },
  scrollContent: {
    maxHeight: '70%',
  },
  modalBody: {
    padding: 20,
  },
  userInfoContainer: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[200],
  },
  userName: {
    fontSize: 16,
    fontFamily: Fonts.OnestBold,
    color: MaterialColors.text.primary,
  },
  userRole: {
    fontSize: 14,
    fontFamily: Fonts.OnestMedium,
    color: MaterialColors.text.secondary,
  },
  permissionSection: {
    marginVertical: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: MaterialColors.text.primary,
    marginBottom: 12,
  },
  permissionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: MaterialColors.grey[50],
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: MaterialColors.grey[200],
  },
  permissionItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  permissionLabel: {
    marginLeft: 8,
    color: MaterialColors.text.secondary,
    fontSize: 12,
    flex: 1,
    flexWrap: 'wrap',
    fontFamily: Fonts.OnestMedium,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: MaterialColors.grey[200],
  },
  button: {
    flex: 1,
    padding: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  primaryButton: {
    backgroundColor: MaterialColors.primary.main,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonText: {
    color: MaterialColors.surface,
    fontSize: 16,
    fontWeight: '600',
  },
  outlineButtonText: {
    color: MaterialColors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ViewUserModal;
