import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts} from '../../../styles/fonts';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<any>();

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    topCurve: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '50%',
      height: '30%',
      backgroundColor: colors.primary,
      borderBottomRightRadius: 300,
    },
    bottomCurve: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: '50%',
      height: '30%',
      backgroundColor: colors.primary,
      borderTopLeftRadius: 300,
    },
    logoContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: '40%',
    },
    logoCircle: {
      width: 100,
      height: 100,
      borderRadius: 50,
      borderWidth: 3,
      borderColor: colors.primary,
      backgroundColor: colors.card,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    logoText: {
      fontSize: 40,
      color: colors.primary,
      fontFamily: Fonts.OnestBold,
      fontWeight: 'bold',
    },
    brandName: {
      fontSize: 20,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
      letterSpacing: 2,
      marginBottom: 6,
    },
    tagline: {
      fontSize: 12,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      letterSpacing: 1,
    },
    buttonContainer: {
      width: '100%',
      alignItems: 'center',
      marginTop: '20%',
      paddingHorizontal: 20,
    },
    registrationButton: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      width: '70%',
      paddingVertical: 14,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
      flexDirection: 'row',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    buttonIcon: {
      marginRight: 8,
    },
    registrationButtonText: {
      color: '#FFFFFF',
      fontSize: 12,
      fontFamily: Fonts.OnestBold,
      letterSpacing: 1,
    },
    signInButton: {
      backgroundColor: colors.secondary,
      borderRadius: 12,
      width: '70%',
      paddingVertical: 14,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    signInButtonText: {
      color: '#FFFFFF',
      fontSize: 12,
      fontFamily: Fonts.OnestBold,
      letterSpacing: 1,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        backgroundColor={colors.background}
        barStyle={isDark ? 'light-content' : 'dark-content'}
      />

      {/* Top curved shape */}
      {/* <View style={styles.topCurve} /> */}

      {/* Logo and brand name */}
      <View style={styles.logoContainer}>
        <View style={styles.logoCircle}>
          <MaterialCommunityIcons
            name="package-variant-closed"
            size={50}
            color={colors.primary}
          />
        </View>
        <Text style={styles.brandName}>NurPDA</Text>
        <Text style={styles.tagline}>INVENTORY MADE SIMPLE</Text>
      </View>

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.registrationButton}
          onPress={() => navigation.navigate('RegistrationScreen')}
          activeOpacity={0.8}>
          <MaterialCommunityIcons
            name="account"
            size={14}
            color="#FFFFFF"
            style={styles.buttonIcon}
          />
          <Text style={styles.registrationButtonText}>REGISTRATION</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.signInButton}
          onPress={() => navigation.navigate('LoginScreen')}
          activeOpacity={0.8}>
          <MaterialCommunityIcons
            name="login"
            size={14}
            color="#FFFFFF"
            style={styles.buttonIcon}
          />
          <Text style={styles.signInButtonText}>SIGN IN</Text>
        </TouchableOpacity>
      </View>

      {/* Bottom curved shape */}
      {/* <View style={styles.bottomCurve} /> */}
    </SafeAreaView>
  );
};

export default WelcomeScreen;
