import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useState, useEffect} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, Secondary} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import {Fonts} from '../../../styles/fonts';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {GetAllItems, showAlert} from '../../../utils/PublicHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import SettingDrop from '../../../components/Inventory/SettingDrop';
import {Department, Employee} from '../../../server/types';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import AddUserModal from './AddUserModal';
import {App_User} from '../../../Types/Lottery/Lottery_Types';
import ViewUserModal from './ViewUserModal';
import {MaterialColors} from '../../../constants/MaterialColors';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const UserSettings: React.FC<NavProps> = ({}) => {
  const [lottery, setLottery] = useState<Department[]>([]);
  const [selectedLottery, setSelectedLottery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [employee, setEmployee] = useState<Employee>();
  const [userRole, setUserRole] = useState<string>('');
  const [users, setUsers] = useState<App_User[]>([]);
  const [isModalVisible, setModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<App_User | null>(null);
  const navigation = useNavigation();

  useEffect(() => {
    getAllUsers();
  }, []);

  useEffect(() => {
    getAllUsers();
  }, [isModalVisible]);

  const getAllUsers = async () => {
    GetAllItems<App_User[]>(
      (await getLotteryPort()).toString(),
      '/GetUsers',
      setUsers,
      setLoading,
    );
  };

  const handleUserAdded = (newUser: App_User): void => {
    setUsers((prevUsers: App_User[]) => [...prevUsers, newUser]);
  };

  const renderItem = ({item}: {item: App_User}) => (
    <View style={styles.userItem}>
      <View>
        <Text style={styles.userId}>Emp ID: {item.Cashier_ID}</Text>
        <Text style={styles.userName}>Emp Name: {item.Emp_Name}</Text>
        <Text style={styles.userEmail}>Password: {item.Password}</Text>
        <Text style={styles.userRole}>Role: {item.Role}</Text>
      </View>

      <TouchableOpacity
        style={styles.permissionButton}
        onPress={() => {
          setSelectedUser(item);
          setIsViewModalVisible(true);
        }}>
        <MaterialCommunityIcons
          name="shield-account"
          size={24}
          color="#30324D"
        />
        <Text style={styles.permissionButtonText}>Permissions</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View
      style={{
        backgroundColor: MaterialColors.surface,

        // height: hp('100%'),
        // width: wp('100%'s,
      }}>
      <View style={{paddingHorizontal: 12}}>
        <Header
          NavName={'User Settings'}
          isProvid={true}
          isOption={true}
          isChoice={true}
          Options={() => setModalVisible(true)}
          Onpress={() => navigation.goBack()}
        />
      </View>

      <View
        style={{
          paddingHorizontal: wp('2.5%'),
          height: '100%',
          width: wp('100%'),
          backgroundColor: Backround,
        }}>
        <View style={styles.listContainer}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#007bff" />
              <Text style={styles.loadingText}>Loading users...</Text>
            </View>
          ) : (
            <FlatList
              data={users}
              renderItem={renderItem}
              keyExtractor={item => item.Cashier_ID}
              contentContainerStyle={styles.flatListContent}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <MaterialCommunityIcons
                    name="account-question"
                    size={50}
                    color="#ccc"
                  />
                  <Text style={styles.emptyText}>No users found</Text>
                </View>
              }
              showsVerticalScrollIndicator={false}
              refreshing={loading}
              onRefresh={getAllUsers}
            />
          )}
        </View>

        <AddUserModal
          visible={isModalVisible}
          onClose={() => setModalVisible(false)}
          onUserAdded={handleUserAdded}
        />

        {selectedUser && (
          <ViewUserModal
            visible={isViewModalVisible}
            onClose={() => {
              setIsViewModalVisible(false);
              setSelectedUser(null);
            }}
            user={selectedUser}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#003366',
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  headerText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  listContainer: {
    flex: 1,
    marginTop: 10,
    marginBottom: hp('10%'),
  },
  flatListContent: {
    paddingBottom: 80,
    paddingHorizontal: wp('2%'),
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: hp('1.8%'),
    fontFamily: Fonts.OnestMedium,
    color: '#555',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 10,
    fontSize: hp('2%'),
    fontFamily: Fonts.OnestMedium,
    color: '#999',
  },
  userItem: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userId: {
    fontSize: hp('1.6%'),
    color: '#888',
    fontFamily: Fonts.OnestRegular,
    marginBottom: 4,
  },
  userName: {
    fontSize: hp('2%'),
    fontFamily: Fonts.OnestBold,
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: hp('1.6%'),
    color: '#666',
    fontFamily: Fonts.OnestRegular,
    marginBottom: 4,
  },
  userRole: {
    fontSize: hp('1.6%'),
    color: '#888',
    fontFamily: Fonts.OnestMedium,
    backgroundColor: Secondary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Secondary,
    padding: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  permissionButtonText: {
    marginHorizontal: 8,
    fontFamily: Fonts.OnestMedium,
    fontSize: hp('1.6%'),
    color: '#30324D',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#007bff',
    borderRadius: 8,
    position: 'absolute',
    bottom: 16,
    right: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  addButtonText: {
    color: 'white',
    fontSize: hp('1.8%'),
    fontFamily: Fonts.OnestMedium,
    marginLeft: 8,
  },
});

export default UserSettings;
