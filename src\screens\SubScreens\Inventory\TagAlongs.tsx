import {View, Text, TouchableOpacity, StyleSheet, Keyboard} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';
import Header from '../../../components/Inventory/Header';
import DataList from '../../../components/Inventory/AppList';
import {Inventory, TagAlong} from '../../../server/types';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  GetItemsParamsNoFilter,
  handleSearch,
  showAlert,
} from '../../../utils/PublicHelper';
import {deleteItem} from '../../../server/service';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppButton from '../../../components/Inventory/AppButton';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Search from '../../../components/Inventory/Search';
import {TextInput} from 'react-native-gesture-handler';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

type BarcodeScreenRouteProp = RouteProp<any, 'TagAlongs'>;

const TagAlongs: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [tagAlongs, setTagAlongs] = useState<TagAlong[]>([]);
  const [tagAlongsFilter, setTagAlongsFilter] = useState<Inventory[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tagAlong, setTagAlong] = useState<boolean>(false);
  const [isCreate, setIsCreate] = useState<boolean>(
    route.params?.IsCreate || false,
  );
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [itemNumber, setItemNumber] = useState<string>(
    route.params?.ItemData || '',
  );
  const [searchQuery, setSearchQuery] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      setTagAlong(false);
      updateTagAlongs();
    }, []),
  );

  const updateTagAlongs = async () => {
    if (isCreate) {
      const getLatestTagAlongs = await AsyncStorage.getItem('SetTagAlongs');

      if (getLatestTagAlongs) {
        const parsedTagAlongs: TagAlong[] = JSON.parse(getLatestTagAlongs);
        setTagAlongs(parsedTagAlongs);
      }
    } else {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/GetTagalong/:ItemNum',
        setTagAlongs,
        {
          ItemNum: itemNumber,
        },
      );

      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/GetTagalong/:ItemNum',
        setTagAlongsFilter,
        {
          ItemNum: itemNumber,
        },
      );
    }
  };

  const DeleteTagAlongItems = (Barcode: string, TagAlong: string) => {
    showAlert('Are you sure you want to Delete?').then(async result => {
      if (result) {
        const updateQuery = tagAlongs.filter(
          item => item.TagAlong_ItemNum !== TagAlong,
        );

        if (isCreate) {
          setTagAlongs(updateQuery);
          await AsyncStorage.setItem(
            'SetTagAlongs',
            JSON.stringify(updateQuery),
          );
        } else {
          const deleteResponse = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeleteTagAlone/:ItemNum/:TagAlong_ItemNum',
            {ItemNum: Barcode, TagAlong_ItemNum: TagAlong},
          );
          setTagAlongs(updateQuery);
        }
      }
    });
  };

  const renderItem = ({item}: {item: TagAlong}) => {
    return (
      <View style={styles.tagAlongCard}>
        <View style={styles.tagAlongInfo}>
          <Text style={styles.tagAlongId}>{item.TagAlong_ItemNum}</Text>
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() =>
            DeleteTagAlongItems(route.params?.ItemData, item.TagAlong_ItemNum)
          }>
          <MaterialCommunityIcons
            name="delete-outline"
            color={colors.error}
            size={16}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      tagAlongs,
      ['TagAlong_ItemNum'],
      setTagAlongsFilter,
      setLoading,
    );
  };

  const toggleLookup = (checked: boolean) => {
    if (checked) {
      setshowLookup(checked);
      textInputRef?.current?.blur();
      setTimeout(() => {
        textInputRef?.current?.focus();
      }, 1200);
    } else {
      Keyboard.dismiss();
      setTimeout(() => {
        textInputRef?.current?.focus();
      }, 1200);
      setshowLookup(checked);
    }
    setSearchQuery('');
    onSearchChange('');
  };
  const textInputRef = useRef<TextInput>(null);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1%'),
    },
    searchContainer: {
      marginVertical: hp('1.5%'),
    },
    listContainer: {
      flex: 1,
      marginTop: hp('1%'),
    },
    tagAlongCard: {
      backgroundColor: colors.card,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.8%'),
      marginBottom: hp('1.2%'),
      borderRadius: 12,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    tagAlongInfo: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    tagAlongId: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    tagAlongChip: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2%'),
      paddingVertical: hp('0.5%'),
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.primary,
    },
    tagAlongQuantity: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.primary,
    },
    deleteButton: {
      backgroundColor: isDark ? colors.surface : '#FFEBEE',
      padding: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.error,
    },
    buttonContainer: {
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
      backgroundColor: colors.surface,
      elevation: 3,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
  });

  return (
    <View style={styles.container}>
      <Header
        NavName="TagAlongs"
        isProvid={true}
        Onpress={() => {
          if (route.params?.ISRESTOREITEM) {
            navigation.navigate('Barcode', {
              NEWBARCODE: route.params?.ItemData,
            });
          } else {
            navigation.navigate('Barcode', {
              ISJUSTBACK: true,
            });
          }
        }}
      />

      <View style={styles.searchContainer}>
        <Search
          value={searchQuery}
          PlaceHolder="Search"
          onChange={onSearchChange}
          AutoFocus={true}
          textInputRef={textInputRef}
          keyboardON={showLookup}
          onToggleLookup={value => toggleLookup(value)}
        />
      </View>

      <View style={styles.listContainer}>
        <DataList data={tagAlongs} renderItem={renderItem} loading={loading} />
      </View>

      <View style={styles.buttonContainer}>
        <FAB
          label={'Add Tag Along Items'}
          position="bottomRight"
          onPress={() =>
            navigation.navigate('AddTagAlong', {
              ItemData: itemNumber,
              IsCreate: isCreate,
            })
          }
        />
      </View>
    </View>
  );
};

export default TagAlongs;
