import React, {useState} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  Platform,
} from 'react-native';
// Change from FontAwesome to Feather to match SearchManual
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface SearchProps {
  PlaceHolder?: string;
  Value?: string;
  onChange?: (text: string) => void | Promise<void>;
  onBlur?: () => void;
  isFilter?: boolean;
  keyboardON?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  containerStyle?: ViewStyle;
}

const Search: React.FC<SearchProps> = ({
  PlaceHolder = 'Search',
  Value,
  onChange,
  onBlur,
  isFilter,
  keyboardON,
  textInputRef,
  containerStyle,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 25,
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1.2%'),
      borderWidth: 1,
      ...Platform.select({
        ios: {
          shadowOffset: {width: 0, height: 1},
          shadowOpacity: 0.1,
          shadowRadius: 3,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    filterContainer: {
      flex: 1,
    },
    searchIcon: {
      marginRight: wp('2%'),
    },
    searchInput: {
      flex: 1,
      fontSize: hp('1.8%'),
      fontFamily: Fonts.OnestMedium,
      padding: 0,
      paddingVertical: hp('0.2%'),
    },
  });
  return (
    <View
      style={[
        {
          ...styles.searchContainer,
          backgroundColor: colors.surface,
          borderColor: isFocused ? colors.primary : colors.border,
          shadowColor: isFocused ? colors.primary : colors.shadow,
        },
        containerStyle,
        isFilter && styles.filterContainer,
      ]}>
      <Feather
        name="search"
        size={hp('2.3%')}
        color={isFocused ? colors.primary : colors.textSecondary}
        style={styles.searchIcon}
      />
      <TextInput
        ref={textInputRef}
        style={[styles.searchInput, {color: colors.text}]}
        placeholder={PlaceHolder}
        placeholderTextColor={colors.placeholder}
        value={Value}
        onChangeText={onChange}
        onBlur={() => {
          setIsFocused(false);
          onBlur && onBlur();
        }}
        onFocus={() => setIsFocused(true)}
        showSoftInputOnFocus={true}
      />
      {Value?.length > 0 && (
        <TouchableOpacity
          onPress={() => {
            onChange?.(''); // Clear the input value
            textInputRef?.current?.blur(); // Optional: remove focus from input
          }}>
          <MaterialCommunityIcons
            name="close-circle"
            size={hp('2.3%')}
            color={isFocused ? colors.primary : colors.textSecondary}
            style={styles.searchIcon}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default Search;
