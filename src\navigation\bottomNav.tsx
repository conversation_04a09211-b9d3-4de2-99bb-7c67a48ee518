import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {View, TouchableOpacity, StyleSheet, Platform} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Foundation from 'react-native-vector-icons/Foundation';
import Dashboard from '../screens/MainScreens/Inventory/Dashboard';
import PrintLabel from '../screens/MainScreens/PrintLabel/PrintLabel';
import LotteryScratch from '../screens/MainScreens/LotteryScratch/LotteryScratch';
import Scanner from '../screens/MainScreens/Inventory/Scanner';
import More from '../screens/MainScreens/Inventory/More';
import {MaterialColors} from '../constants/MaterialColors';
import {useThemeColors} from '../Theme/useThemeColors';
import {useTheme} from '../Theme/ThemeContext';

const Tab = createBottomTabNavigator();

const ThemedTabs = () => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  return (
    <Tab.Navigator
      initialRouteName="Dashboard"
      screenOptions={{
        tabBarStyle: [
          styles.tabBar,
          {
            backgroundColor: colors.surface,
            shadowColor: colors.shadow,
            borderTopColor: colors.border,
          },
        ],
        tabBarLabelStyle: [styles.tabBarLabel, {color: colors.textSecondary}],
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
      }}>
      <Tab.Screen
        name="Dashboard"
        component={Dashboard}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({color, size}) => (
            <MaterialCommunityIcons name="home" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />

      <Tab.Screen
        name="PrintLabel"
        component={PrintLabel}
        options={{
          tabBarLabel: 'Print Label',
          tabBarIcon: ({color, size}) => (
            <MaterialCommunityIcons name="label" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />

      <Tab.Screen
        name="Scanner"
        component={Scanner}
        options={{
          tabBarLabel: '',
          tabBarIcon: ({color, size}) => <View style={{height: 0}} />,
          headerShown: false,
          tabBarButton: props => (
            <TouchableOpacity
              {...props}
              style={[
                styles.scanButton,
                {
                  backgroundColor: colors.primary,
                  borderColor: colors.surface,
                  shadowColor: colors.shadow,
                },
              ]}
              activeOpacity={0.8}
              onPress={() => props.onPress && props.onPress()}>
              <MaterialCommunityIcons
                name="barcode-scan"
                color={'white'}
                size={28}
              />
            </TouchableOpacity>
          ),
        }}
      />

      <Tab.Screen
        name="Lottery"
        component={LotteryScratch}
        options={{
          tabBarLabel: 'Lottery',
          tabBarIcon: ({color, size}) => (
            <FontAwesome name="ticket" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />

      <Tab.Screen
        name="More"
        component={More}
        options={{
          tabBarLabel: 'Inventory',
          tabBarIcon: ({color, size}) => (
            <Foundation name="indent-more" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
};

export default function Tabs() {
  return <ThemedTabs />;
}

const styles = StyleSheet.create({
  tabBar: {
    height: 75,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    borderTopWidth: 1,
    paddingVertical: 5,
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
    position: 'relative',
  },
  tabBarLabel: {
    fontSize: 12,
    marginBottom: 10,
    marginTop: -15,
    fontWeight: '500',
  },
  tabBarActiveLabel: {
    // This will be handled by tabBarActiveTintColor in screenOptions
  },
  scanButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    top: -15,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.3,
        shadowRadius: 5,
      },
      android: {
        elevation: 8,
      },
    }),
  },
});
