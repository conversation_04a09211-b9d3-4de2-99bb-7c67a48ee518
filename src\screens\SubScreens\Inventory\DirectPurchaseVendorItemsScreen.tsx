import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useCallback, useRef, useState, useMemo} from 'react';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {VendorItem} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import {useThemeColors} from '../../../Theme/useThemeColors';

type DirectPurchaseVendorItemsRouteProp = RouteProp<
  any,
  'DirectPurchaseVendorItemsScreen'
>;

const DirectPurchaseVendorItemsScreen: React.FC<{
  route: DirectPurchaseVendorItemsRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const vendorItemsFilter = useMemo(
    () => route.params?.vendorItemsFilter || [],
    [route.params?.vendorItemsFilter],
  );
  const VendorItemAdd = route.params?.VendorItemAdd || (() => {});

  const [loading] = useState<boolean>(false);
  const textInputRef2 = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [initial, setInitial] = useState<VendorItem[]>(vendorItemsFilter);
  const [showLookupMain, setShowLookupMain] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const colors = useThemeColors();

  useFocusEffect(
    useCallback(() => {
      setSearchQuery('');
      setShowLookupMain(false);
      setInitial(vendorItemsFilter);
      setTimeout(() => {
        if (textInputRef2.current) {
          textInputRef2.current.blur();
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, [vendorItemsFilter]),
  );

  const toggleLookupMain = useCallback(
    (value: boolean) => {
      setShowLookupMain(value);
      setSearchQuery('');
      setInitial(vendorItemsFilter);

      if (Platform.OS === 'android') {
        if (value) {
          if (textInputRef2.current) {
            textInputRef2.current.clear();
            textInputRef2.current.blur();
          }
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.blur();
              setTimeout(() => {
                if (textInputRef2.current) {
                  textInputRef2.current.focus();
                }
              }, 200);
            }
          }, 200);
        } else {
          setSearchQuery('');
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.blur();
              setTimeout(() => {
                if (textInputRef2.current) {
                  textInputRef2.current.focus();
                }
              }, 200);
            }
          }, 200);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef2.current?.focus();
        }, 100);
        setInitial(vendorItemsFilter);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
        setInitial(vendorItemsFilter);
      }
    },
    [vendorItemsFilter],
  );

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      setInitial(vendorItemsFilter);
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
    setRefreshing(false);
  };

  const onSearchVendorItems = async (text: string) => {
    setSearchQuery(text);

    if (text) {
      if (showLookupMain) {
        // Simple text search in vendor items
        const filtered = vendorItemsFilter.filter(
          item =>
            item.ItemName?.toLowerCase().includes(text.toLowerCase()) ||
            item.ItemNum?.toLowerCase().includes(text.toLowerCase()),
        );
        setInitial(filtered);
      } else {
        // Navigate to add item directly
        const exactMatch = vendorItemsFilter.find(
          item =>
            item.ItemName?.toLowerCase() === text.toLowerCase() ||
            item.ItemNum?.toLowerCase() === text.toLowerCase(),
        );

        if (exactMatch) {
          VendorItemAdd(exactMatch, navigation);
        } else {
          // Filter items for selection
          const filtered = vendorItemsFilter.filter(
            item =>
              item.ItemName?.toLowerCase().includes(text.toLowerCase()) ||
              item.ItemNum?.toLowerCase().includes(text.toLowerCase()),
          );
          setInitial(filtered);
        }
      }
    } else {
      setInitial(vendorItemsFilter);
    }
  };

  const renderItem = ({item}: {item: VendorItem}) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: colors.card,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1.5%'),
          borderRadius: 15,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
          marginVertical: hp('0.5%'),
        }}
        onPress={() => VendorItemAdd(item, navigation)}>
        <View style={{gap: 7, flex: 1, marginRight: wp('2%')}}>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.small,
              color: colors.text,
            }}>
            {item.ItemName}
          </Text>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: FontSizes.small,
              color: colors.textSecondary,
            }}>
            {`Per Case: ${item.NumPerVenCase || 0}`}
          </Text>
        </View>
        <Text
          style={{
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.medium,
            color: colors.primary,
          }}>
          ${item.CostPer?.toFixed(2) || '0.00'}
        </Text>
      </TouchableOpacity>
    );
  };

  const handleDoneClick = () => {
    if (textInputRef2.current) {
      textInputRef2.current.clear();
      textInputRef2.current.blur();
    }

    setSearchQuery('');
    setShowLookupMain(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef2.current) {
        textInputRef2.current.focus();
      }
    }, 200);
    setInitial(vendorItemsFilter);
  };

  return (
    <View
      style={{
        backgroundColor: colors.background,
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header
          NavName="Add Items to Direct Purchase"
          isProvid={true}
          Onpress={() => navigation.goBack()}
        />

        <View style={{paddingVertical: hp('2%')}}>
          <AppSearchWIthFilter
            OnSearch={onSearchVendorItems}
            SearchValue={searchQuery}
            Keyboardon={showLookupMain}
            textInputRef={textInputRef2}
            onToggleLookup={toggleLookupMain}
            IsFilter={false}
            OnSubmitEditing={() => handleDoneClick()}
          />
        </View>

        <View style={{}}>
          <View>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestBold,
                color: colors.text,
                paddingVertical: hp('1%'),
              }}>
              {`Total Items: (${initial.length || 0})`}
            </Text>
          </View>
        </View>

        <DataList
          data={initial}
          loading={loading}
          renderItem={renderItem}
          Hight="70%"
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>
    </View>
  );
};

export default DirectPurchaseVendorItemsScreen;
