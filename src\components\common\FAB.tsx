import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {Fonts, FontSizes} from '../../styles/fonts';
import {Primary} from '../../constants/Color';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface FABProps {
  label: string;
  onPress: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  position?:
    | 'bottomRight'
    | 'bottomLeft'
    | 'bottom'
    | 'topRight'
    | 'topLeft'
    | 'bottomRightEven'
    | 'center';
}

const FAB: React.FC<FABProps> = ({
  label,
  onPress,
  style,
  textStyle,
  position = 'bottomRight', // Changed default to 'bottom'
}) => {
  // Determine position styles based on the position prop
  const getPositionStyle = (): ViewStyle => {
    switch (position) {
      case 'bottomLeft':
        return {bottom: 20, left: 20};
      case 'bottom':
        return {
          bottom: 20,
          alignSelf: 'center',
          left: '50%',
          transform: [{translateX: -60}],
        };
      case 'topRight':
        return {top: 20, right: 20};
      case 'topLeft':
        return {top: 20, left: 20};
      case 'center':
        return {
          bottom: '50%',
          right: '50%',
          transform: [{translateX: 50}, {translateY: 50}],
        };

      case 'bottomRightEven':
        return {bottom: 20, right: 90};
      case 'bottomRight':

      default:
        return {bottom: 20, right: 10};
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    fabButton: {
      position: 'absolute',
      backgroundColor: colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 5,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.4 : 0.3,
      shadowRadius: 3,
      zIndex: 999,
    },
    fabButtonText: {
      color: '#FFFFFF',
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
    },
  });

  return (
    <TouchableOpacity
      style={[styles.fabButton, getPositionStyle(), style]}
      onPress={onPress}>
      <Text style={[styles.fabButtonText, textStyle]}>{label}</Text>
    </TouchableOpacity>
  );
};

export default FAB;
