import { useState, useCallback, useRef, useEffect } from 'react';
import { Inventory_Filter, Inventory } from '../../server/types';
import {
  GetAllItemsNoProps,
  GetItemsParamsNoFilterNoReturn,
  showAlertOK,
  showAlert,
  updateData
} from '../../utils/PublicHelper';
import { getInventoryPort } from '../../server/InstanceTypes';
import { applyDefaults } from '../../Validator/Inventory/Barcode';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface CountItemInput {
  itemNum: string;
  value: string;
}

export const useCountItemData = () => {
  // Data state
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Count item specific state
  const [action, setAction] = useState<boolean>(false);
  const [inputValues, setInputValues] = useState<CountItemInput[]>([]);
  const [newStockValues, setNewStockValues] = useState<{[key: string]: string}>({});
  
  // Pagination state
  const [page, setPage] = useState<number>(1);
  const ITEMS_PER_PAGE = 20;

  // Action ref for immediate state access
  const actionRef = useRef(false);

  // Load persisted count data on initialization
  useEffect(() => {
    const loadPersistedData = async () => {
      try {
        const persistedData = await AsyncStorage.getItem('PHYSICAL_COUNT_DATA');
        const persistedAction = await AsyncStorage.getItem('PHYSICAL_COUNT_ACTION');

        if (persistedData) {
          const parsedData = JSON.parse(persistedData);
          setInputValues(parsedData);

          // Rebuild newStockValues from inputValues
          const stockValues: {[key: string]: string} = {};
          parsedData.forEach((item: CountItemInput) => {
            stockValues[item.itemNum] = item.value;
          });
          setNewStockValues(stockValues);
        }

        if (persistedAction === 'true') {
          setAction(true);
          actionRef.current = true;
        }
      } catch (error) {
        console.error('Error loading persisted count data:', error);
      }
    };

    loadPersistedData();
  }, []);

  // Pagination function
  const applyPagination = useCallback((data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * ITEMS_PER_PAGE;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  }, [ITEMS_PER_PAGE]);

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE, applyPagination]);

  // Fetch inventory data
  const fetchInventoryData = useCallback(async () => {
    try {
      setLoading(true);
      const port = await getInventoryPort();
      const result = await GetAllItemsNoProps(port, '/getInventoryFilter');

      if (result === undefined || !result) {
        setTimeout(() => {
          showAlertOK(
            'Database Connection Failed, Please Check Your Database Configuration',
            'Connection Failed',
            'OK',
          );
        }, 100);
        return;
      }

      // Apply any local stock updates to the fresh data
      const updatedResult = result.map(item => {
        const localUpdate = newStockValues[item.ItemNum];
        if (localUpdate !== undefined) {
          return { ...item, In_Stock: localUpdate };
        }
        return item;
      });

      setMasterData(updatedResult);
      setFilteredData(updatedResult);
      applyPagination(updatedResult, 1);
      setInitialLoading(false);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  }, [applyPagination, newStockValues]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await fetchInventoryData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  }, [fetchInventoryData]);

  // Handle input change for count values
  const handleInputChange = useCallback(async (value: string, itemNum: string) => {
    setInputValues(prevValues => {
      const existingItemIndex = prevValues.findIndex(
        item => item.itemNum === itemNum,
      );

      let updatedValues;
      if (existingItemIndex !== -1) {
        updatedValues = [...prevValues];
        updatedValues[existingItemIndex].value = value;
      } else {
        updatedValues = [...prevValues, { itemNum, value }];
      }

      // Persist to AsyncStorage for recovery
      AsyncStorage.setItem('PHYSICAL_COUNT_DATA', JSON.stringify(updatedValues));

      return updatedValues;
    });
  }, []);

  // Update stock values in all data arrays
  const updateStockInData = useCallback((itemNum: string, newStock: string) => {
    const updateItem = (item: Inventory_Filter) =>
      item.ItemNum === itemNum ? { ...item, In_Stock: newStock } : item;

    setMasterData(prev => prev.map(updateItem));
    setFilteredData(prev => prev.map(updateItem));
    setDisplayData(prev => prev.map(updateItem));
    
    setNewStockValues(prev => ({
      ...prev,
      [itemNum]: newStock,
    }));
  }, []);

  // Save physical count
  const savePhysicalCount = useCallback(async () => {
    const newActionState = !action;
    setAction(newActionState);
    actionRef.current = newActionState;

    // Persist action state
    await AsyncStorage.setItem('PHYSICAL_COUNT_ACTION', newActionState.toString());

    if (newActionState) {
      setTimeout(() => {
        showAlertOK('Physical Count Started', 'Physical Count');
      }, 100);
      return;
    }

    // Finish physical count - show options for remaining items
    if (inputValues.length === 0) {
      setTimeout(() => {
        Alert.alert('No Changes', 'No items have been counted. Please count some items before finishing.');
      }, 100);
      setAction(true);
      actionRef.current = true;
      return;
    }

    const remaining = Number(filteredData.length) - Number(inputValues.length);

    // Add delay to ensure activity is attached
    setTimeout(() => {
      showAlert(
        `You've counted ${inputValues.length} out of ${filteredData.length} items. What to do with the remaining ${remaining} items?`,
        'Physical Count Options',
        true,
        'Keep Current Quantity',
        'Change Stock to 0',
      )
      .then(async result => {
        try {
          setLoading(true);

          if (result) {
            // Option 1: Change Stock to 0 - Update each item individually using /updatebarcode
            console.log('Updating items individually with /updatebarcode');

            for (const countUpdate of inputValues) {
              const getBarcode = await GetItemsParamsNoFilterNoReturn(
                (await getInventoryPort()).toString(),
                '/inventory/:ItemNum',
                { ItemNum: countUpdate.itemNum },
              );

              if (getBarcode && getBarcode[0]) {
                const inventoryData: Partial<Inventory> = {
                  ItemNum: countUpdate.itemNum,
                  ItemName: getBarcode[0]?.ItemName,
                  Dept_ID: getBarcode[0]?.Dept_ID,
                  Cost: getBarcode[0]?.Cost,
                  Price: getBarcode[0]?.Price,
                  Retail_Price: getBarcode[0]?.Retail_Price,
                  In_Stock: Number(countUpdate.value),
                  Date_Created: getBarcode[0]?.Date_Created,
                  Last_Sold: getBarcode[0]?.Last_Sold,
                  Location: getBarcode[0]?.Location,
                  Vendor_Number: getBarcode[0]?.Vendor_Number,
                  Vendor_Part_Num: getBarcode[0]?.Vendor_Part_Num,
                  Reorder_Level: getBarcode[0]?.Reorder_Level,
                  Reorder_Quantity: getBarcode[0]?.Reorder_Quantity,
                  ReOrder_Cost: getBarcode[0]?.ReOrder_Cost,
                  Unit_Size: getBarcode[0]?.Unit_Size,
                  Unit_Type: getBarcode[0]?.Unit_Type,
                  FoodStampable: getBarcode[0]?.FoodStampable,
                  Tax_1: getBarcode[0]?.Tax_1?.[0],
                  Tax_2: getBarcode[0]?.Tax_2?.[0],
                  Tax_3: getBarcode[0]?.Tax_3?.[0],
                  Tax_4: getBarcode[0]?.Tax_4?.[0],
                  Tax_5: getBarcode[0]?.Tax_5?.[0],
                  Tax_6: getBarcode[0]?.Tax_6?.[0],
                  Check_ID: getBarcode[0]?.Check_ID,
                  Check_ID2: getBarcode[0]?.Check_ID2,
                  Store_ID: getBarcode[0]?.Store_ID,
                  ItemName_Extra: getBarcode[0]?.ItemName_Extra,
                };

                const applyDefault = applyDefaults(inventoryData);
                await updateData<Inventory>({
                  baseURL: (await getInventoryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/updatebarcode',
                });
              }
            }
          } else {
            // Option 2: Keep Current Quantity - Use batch update with /updatecountitem
            console.log('Updating items with batch /updatecountitem');

            const updateDataPayload = inputValues.map(input => ({
              itemNum: input.itemNum,
              value: input.value
            }));

            await updateData({
              baseURL: (await getInventoryPort()).toString(),
              data: { ItemData: updateDataPayload },
              endpoint: '/updatecountitem',
            });
          }

          // Clear input values and persisted data after successful update
          setInputValues([]);
          setNewStockValues({});
          await AsyncStorage.removeItem('PHYSICAL_COUNT_DATA');
          await AsyncStorage.removeItem('PHYSICAL_COUNT_ACTION');

          // Refresh data from server
          await fetchInventoryData();

          setTimeout(() => {
            Alert.alert('Item Counted Success!');
          }, 100);
        } catch (error) {
          console.error('Error updating physical count:', error);
          setTimeout(() => {
            Alert.alert(
              'Update Failed',
              'There was an error updating the inventory. Please try again.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    setAction(true);
                    actionRef.current = true;
                  }
                }
              ]
            );
          }, 100);
        } finally {
          setLoading(false);
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
        setAction(true);
        actionRef.current = true;
      });
    }, 200); // Delay for the main showAlert
  }, [action, inputValues, filteredData, updateStockInData, fetchInventoryData]);

  // Clear all count data
  const clearCountData = useCallback(async () => {
    setInputValues([]);
    setNewStockValues({});
    setAction(false);
    actionRef.current = false;

    // Clear persisted data
    await AsyncStorage.removeItem('PHYSICAL_COUNT_DATA');
    await AsyncStorage.removeItem('PHYSICAL_COUNT_ACTION');
  }, []);

  return {
    // Data
    masterData,
    filteredData,
    displayData,
    loading,
    initialLoading,
    refreshing,
    
    // Count state
    action,
    inputValues,
    newStockValues,
    actionRef,
    
    // Pagination
    page,
    ITEMS_PER_PAGE,
    
    // Functions
    setMasterData,
    setFilteredData,
    setDisplayData,
    setPage,
    setLoading,
    applyPagination,
    handleLoadMore,
    fetchInventoryData,
    handleRefresh,
    handleInputChange,
    updateStockInData,
    savePhysicalCount,
    clearCountData,
  };
};
