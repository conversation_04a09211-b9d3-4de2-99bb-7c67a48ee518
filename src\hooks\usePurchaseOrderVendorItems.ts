import {useState, useCallback, useRef, useEffect} from 'react';
import {TextInput, Platform, Keyboard} from 'react-native';
import {
  VendorItem,
  Inventory_Filter,
  PurchaseOrderItems,
} from '../server/types';
import {
  GetItemsParamsNoFilterNoReturn,
  showAlert,
  showAlertOK,
} from '../utils/PublicHelper';
import {getInventoryPort} from '../server/InstanceTypes';

export const usePurchaseOrderVendorItems = (
  vendorItems: VendorItem[],
  itemPO: PurchaseOrderItems[],
  navigation: any,
  mainPO: any,
) => {
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [searchQueryForModal, setSearchQueryForModal] = useState<string>('');
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);

  useEffect(() => {
    setVendorItemsFilter(vendorItems);
  }, [vendorItems]);

  const isItemNumExist = useCallback(
    (itemNum: string) => {
      return itemPO.some(item => item.ItemNum === itemNum);
    },
    [itemPO],
  );

  const toggleLookup = useCallback(
    (value: boolean) => {
      setVendorItemsFilter(vendorItems);
      setshowLookup(value);
      setSearchQueryForModal('');

      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        setSearchQueryForModal('');
        Keyboard.dismiss();
      }
    },
    [vendorItems],
  );

  const onChangeAddItems = useCallback(
    async (text: string) => {
      setCameraModal(false);
      const getBarcode = await GetItemsParamsNoFilterNoReturn<
        Inventory_Filter[]
      >((await getInventoryPort()).toString(), '/inventory/:ItemNum', {
        ItemNum: text,
      });
      const barcodeArray = Array.isArray(getBarcode) ? getBarcode : [];
      setSearchQueryForModal(barcodeArray[0]?.ItemNum || text);

      if (showLookup) {
        // Filter vendor items for lookup
        const filtered = vendorItems.filter(
          item =>
            item.ItemName?.toLowerCase().includes(text.toLowerCase()) ||
            item.ItemNum?.toLowerCase().includes(text.toLowerCase()),
        );
        setVendorItemsFilter(filtered);
        setLoading(false);
      } else {
        if (barcodeArray.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: text,
            });
          } else {
            setVendorItemsFilter(vendorItems);
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }
            setSearchQueryForModal('');
            setshowLookup(false);
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          const targetItemNum =
            barcodeArray.length === 0 ? text : barcodeArray[0]?.ItemNum;
          const FilterExist = isItemNumExist(targetItemNum || text);

          if (FilterExist) {
            showAlertOK(
              'This Item is Already Exists. In Current Purchase Order',
              'Already Exists',
              'OK',
              () => {
                if (textInputRef.current) {
                  textInputRef.current.clear();
                  textInputRef.current.blur();
                }
                setSearchQueryForModal('');
                setshowLookup(false);
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef.current) {
                    textInputRef.current.focus();
                  }
                }, 200);
              },
            );
          } else {
            const CheckedVendorItems = vendorItems.find(
              vendor => vendor.ItemNum === targetItemNum,
            );

            if (CheckedVendorItems) {
              navigation.navigate('PurchaseOrderQuanity', {
                ItemData: CheckedVendorItems,
                 Main: mainPO,
              });
            } else {
              showAlertOK(
                'This Item is Not available. In Current Vendor',
                'Not Available',
                'OK',
                () => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }
                  setSearchQueryForModal('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                },
              );
            }
          }
        }
      }
    },
    [showLookup, vendorItems, isItemNumExist, navigation],
  );

  const handleDoneClickSub = useCallback(() => {
    setVendorItemsFilter(vendorItems);

    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    setSearchQueryForModal('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  }, [vendorItems]);

  return {
    vendorItemsFilter,
    searchQueryForModal,
    showLookup,
    loading,
    cameraModal,
    setCameraModal,
    textInputRef,
    toggleLookup,
    onChangeAddItems,
    handleDoneClickSub,
    isItemNumExist,
  };
};
