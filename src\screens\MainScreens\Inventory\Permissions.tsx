import {View, Text, ScrollView, Alert} from 'react-native';
import React, {useState, useEffect} from 'react';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import {Backround, Secondary, SecondaryHint} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AppButton from '../../../components/Inventory/AppButton';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../../../components/Inventory/Header';
import {
  hasPermission,
  savePermissionsToStorage,
  getPermissionsFromStorage,
} from '../../../utils/permissionHelper';
import {
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  Employee,
  Inventory_Reference,
  Lottery_Permissions,
} from '../../../server/types';
import {createItem, updateItem} from '../../../server/service';

const PERMISSION_LABELS = {
  CFA_Inven_Add: 'Add Inventory',
  CFA_Inven_Edit: 'Edit Inventory',
  CFA_Vendors_Add: 'Add Vendors',
  CFA_Depts_Add: 'Add Department',
  CFA_Depts_Edit: 'Edit Department',
  CFA_INVEN_VIEW: 'View Inventory',
  CFA_HH_Create_PO: 'Create PO ',
  CFA_HH_DSD: 'Direct Store Delivery',
  CFA_HH_Inv_Count: 'Inventory Count',
  CFA_HH_PO_Receive: 'Receive PO ',
  CFA_HH_Inv_Adjust: 'Adjust Inventory',
  CFA_HH_PRINT_LABELS: 'Print Labels ',
};

const LOTTERY_PERMISSION_LABELS = {
  StartOrEndShift: 'Start/End Shift',
  ActivateBook: 'Activate Books',
  CreateNewGame: 'Create New Game',
  OrganizeSlot: 'Organize Slots',
  ViewShift: 'View Shift Report',
  ResetShift: 'Reset Shift',
  CFA_Inven_Add: 'Add Inventory',
  CFA_Inven_Edit: 'Edit Inventory',
  CFA_Vendors_Add: 'Add Vendors',
  CFA_Depts_Add: 'Add Department',
  CFA_Depts_Edit: 'Edit Department',
  CFA_INVEN_VIEW: 'View Inventory',
  CFA_HH_Create_PO: 'Create PO ',
  CFA_HH_DSD: 'Direct Store Delivery',
  CFA_HH_Inv_Count: 'Inventory Count',
  CFA_HH_PO_Receive: 'Return Vendor ',
  CFA_HH_Inv_Adjust: 'Adjust Inventory',
  CFA_HH_PRINT_LABELS: 'Print Labels ',
};

const PermissionGrid = ({title, permissions, state, setState, labels}) => {
  return (
    <View>
      <Text
        style={{
          fontSize: hp('2%'),
          fontWeight: 'bold',
          marginBottom: hp('1%'),
        }}>
        {title}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}>
        {permissions.map((item, index) => (
          <View
            key={index}
            style={{
              width: '40%',
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: hp('2%'),
            }}>
            <CustomCheckbox
              isChecked={state[item] || false}
              onChange={value => setState(prev => ({...prev, [item]: value}))}
            />
            <Text
              style={{
                marginLeft: 5,
                color: SecondaryHint,
                fontFamily: Fonts.OnestBold,
                fontSize: hp('1.5%'),
                flexShrink: 1,
              }}>
              {labels[item] || item.replace('CFA_', '').replace('_', ' ')}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const Permissions = () => {
  const [selectedValue, setSelectedValue] = useState<string>('');
  const [selectedEmployeeDetail, setSelectedEmployeeDetail] =
    useState<string>('');
  const [permissions, setPermissions] = useState({});
  const [storedPermissions, setStoredPermissions] = useState({});
  const [storedLotteryPermissions, setStoredLotteryPermissions] = useState({});
  const [allEmployee, setAllEmployee] = useState<Employee[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [permissionList, setPermissionList] = useState<string[]>([]);
  const [lotteryPermissions, setLotteryPermissions] = useState({});
  const lotteryPermissionList = Object.keys(LOTTERY_PERMISSION_LABELS);

  const allEmployeeOptions = allEmployee.map(employee => ({
    label: employee.Cashier_ID,
    value: employee.Cashier_ID,
  }));

  const getAllEmployee = async () => {
    GetAllItems<Employee[]>(
      (await getInventoryPort()).toString(),
      '/getAllEmployee',
      setAllEmployee,
      setLoading,
    );
  };

  useEffect(() => {
    const loadPermissions = async () => {
      const savedPermissions = await getPermissionsFromStorage();
      if (savedPermissions) {
        setStoredPermissions(savedPermissions);
      }
    };
    getAllEmployee();
    loadPermissions();
  }, []);

  const loadLotteryPermissions = async (empID: string) => {
    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: empID},
      );

      if (Array.isArray(getBarcode) && getBarcode.length > 0) {
        const employeePermissions = getBarcode[0]; // Extract the first object
        setLotteryPermissions({
          StartOrEndShift: employeePermissions.StartOrEndShift || false,
          ActivateBook: employeePermissions.ActivateBook || false,
          CreateNewGame: employeePermissions.CreateNewGame || false,
          OrganizeSlot: employeePermissions.OrganizeSlot || false,
          ViewShift: employeePermissions.ViewShift || false,
          ResetShift: employeePermissions.ResetShift || false,
          CFA_Inven_Add: employeePermissions.CFA_Inven_Add || false,
          CFA_Inven_Edit: employeePermissions.CFA_Inven_Edit || false,
          CFA_Vendors_Add: employeePermissions.CFA_Vendors_Add || false,
          CFA_Depts_Add: employeePermissions.CFA_Depts_Add || false,
          CFA_Depts_Edit: employeePermissions.CFA_Depts_Edit || false,
          CFA_INVEN_VIEW: employeePermissions.CFA_INVEN_VIEW || false,
          CFA_HH_Create_PO: employeePermissions.CFA_HH_Create_PO || false,
          CFA_HH_DSD: employeePermissions.CFA_HH_DSD || false,
          CFA_HH_Inv_Count: employeePermissions.CFA_HH_Inv_Count || false,
          CFA_HH_PO_Receive: employeePermissions.CFA_HH_PO_Receive || false,
          CFA_HH_Inv_Adjust: employeePermissions.CFA_HH_Inv_Adjust || false,
          CFA_HH_PRINT_LABELS: employeePermissions.CFA_HH_PRINT_LABELS || false,
        });
      } else {
        setLotteryPermissions({
          StartOrEndShift: false,
          ActivateBook: false,
          CreateNewGame: false,
          OrganizeSlot: false,
          ViewShift: false,
          ResetShift: false,
          CFA_Inven_Add: false,
          CFA_Inven_Edit: false,
          CFA_Vendors_Add: false,
          CFA_Depts_Add: false,
          CFA_Depts_Edit: false,
          CFA_INVEN_VIEW: false,
          CFA_HH_Create_PO: false,
          CFA_HH_DSD: false,
          CFA_HH_Inv_Count: false,
          CFA_HH_PO_Receive: false,
          CFA_HH_Inv_Adjust: false,
          CFA_HH_PRINT_LABELS: false,
        });
      }
    } catch (error) {
      console.error('Error loading lottery permissions:', error);
    }
  };

  useEffect(() => {
    if (selectedValue) {
      const selectedEmployee = allEmployee.find(
        emp => emp.Cashier_ID === selectedValue,
      );

      if (selectedEmployee) {
        setSelectedEmployeeDetail(selectedEmployee);
        const formattedPermissions = {};
        const extractedPermissions = [];

        Object.keys(PERMISSION_LABELS).forEach(key => {
          if (selectedEmployee.hasOwnProperty(key)) {
            formattedPermissions[key] = selectedEmployee[key] === 'Y';
            extractedPermissions.push(key);
          }
        });

        setPermissions(formattedPermissions);
        setPermissionList(extractedPermissions);
        loadLotteryPermissions(selectedValue);
      } else {
        setPermissions({});
        setPermissionList([]);
      }
    }
  }, [selectedValue, allEmployee]);

  const createLotteryPermission = async () => {
    const InventoryRef: Lottery_Permissions = {
      Emp_ID: selectedValue,
      Emp_Name:
        selectedEmployeeDetail.EmpName || selectedEmployeeDetail.name || '',
      StartOrEndShift: lotteryPermissions['StartOrEndShift'] || false,
      ActivateBook: lotteryPermissions['ActivateBook'] || false,
      CreateNewGame: lotteryPermissions['CreateNewGame'] || false,
      OrganizeSlot: lotteryPermissions['OrganizeSlot'] || false,
      ViewShift: lotteryPermissions['ViewShift'] || false,
      ResetShift: lotteryPermissions['ResetShift'] || false,
      CFA_Inven_Add: lotteryPermissions['CFA_Inven_Add'] || false,
      CFA_Inven_Edit: lotteryPermissions['CFA_Inven_Edit'] || false,
      CFA_Vendors_Add: lotteryPermissions['CFA_Vendors_Add'] || false,
      CFA_Depts_Add: lotteryPermissions['CFA_Depts_Add'] || false,
      CFA_Depts_Edit: lotteryPermissions['CFA_Depts_Edit'] || false,
      CFA_INVEN_VIEW: lotteryPermissions['CFA_INVEN_VIEW'] || false,
      CFA_HH_Create_PO: lotteryPermissions['CFA_HH_Create_PO'] || false,
      CFA_HH_DSD: lotteryPermissions['CFA_HH_DSD'] || false,
      CFA_HH_Inv_Count: lotteryPermissions['CFA_HH_Inv_Count'] || false,
      CFA_HH_PO_Receive: lotteryPermissions['CFA_HH_PO_Receive'] || false,
      CFA_HH_Inv_Adjust: lotteryPermissions['CFA_HH_Inv_Adjust'] || false,
      CFA_HH_PRINT_LABELS: lotteryPermissions['CFA_HH_PRINT_LABELS'] || false,
    };
    try {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: selectedValue},
      );

      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        const result = await createItem(
          (await getLotteryPort()).toString(),
          '/createlotpermission',
          InventoryRef,
        );
        if (result) {
          Alert.alert('Permission Updated!');
        }
      } else {
        const result = await updateItem(
          (await getLotteryPort()).toString(),
          '/updatelotpermission',
          InventoryRef,
        );
        if (result) {
          Alert.alert('Permission Updated!');
        }
      }
    } catch (error) {
      console.error('Error saving lottery permissions:', error);
    }
  };

  const savePermissions = async () => {
    await createLotteryPermission();
  };

  return (
    <View
      style={{
        paddingHorizontal: wp('2.5%'),
        height: '100%',
        width: wp('100%'),
        backgroundColor: Backround,
      }}>
      <Header NavName="Permissions" />
      <ScrollView contentContainerStyle={{}}>
        <AppDropDown
          label="Cashiers"
          options={allEmployeeOptions}
          selectedValue={selectedValue}
          onSelect={value => setSelectedValue(value)}
          isRequired={true}
        />
        {selectedValue !== '' && (
          <>
            <PermissionGrid
              title="App Permissions"
              permissions={lotteryPermissionList}
              state={lotteryPermissions}
              setState={setLotteryPermissions}
              labels={LOTTERY_PERMISSION_LABELS}
            />
          </>
        )}
      </ScrollView>
      {selectedValue !== '' && (
        <View
          style={{
            position: 'absolute',
            right: 0,
            left: 0,
            bottom: 0,
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('1%'),
            backgroundColor: Secondary,
          }}>
          <AppButton Title="Save" OnPress={savePermissions} />
        </View>
      )}
    </View>
  );
};

export default Permissions;
