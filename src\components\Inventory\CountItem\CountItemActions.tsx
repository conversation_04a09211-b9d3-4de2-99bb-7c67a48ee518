import React, { memo } from 'react';
import { View, StyleSheet } from 'react-native';
import FAB from '../../common/FAB';
import { MaterialColors } from '../../../constants/MaterialColors';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useThemeColors } from '../../../Theme/useThemeColors';
import { useTheme } from '../../../Theme/ThemeContext';

interface CountItemActionsProps {
  action: boolean;
  onStartFinish: () => void;
  onCancel: () => void;
}

const CountItemActions: React.FC<CountItemActionsProps> = ({
  action,
  onStartFinish,
  onCancel,
}) => {
  const colors = useThemeColors();
  const { isDark } = useTheme();

  const styles = StyleSheet.create({
    fabContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('0.5%'),
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      elevation: 12,
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: -4 },
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 6,
      borderTopWidth: 0,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: hp('8%'),
    },
    actionContainer: {
      height: '100%',
      width: '100%',
    },
    actionContainerWithCancel: {
      height: '100%',
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

  return (
    <View style={styles.fabContainer}>
      <View
        style={
          !action
            ? styles.actionContainer
            : styles.actionContainerWithCancel
        }>
        {action && (
          <FAB
            label={'Cancel'}
            position="bottomRightEven"
            style={{
              backgroundColor: MaterialColors.error.main,
            }}
            onPress={onCancel}
          />
        )}

        <FAB
          label={!action ? 'Start Physical Count' : 'Finish'}
          position="bottomRight"
          onPress={onStartFinish}
        />
      </View>
    </View>
  );
};

export default memo(CountItemActions);
