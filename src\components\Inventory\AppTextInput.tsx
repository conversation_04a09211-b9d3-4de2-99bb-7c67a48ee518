import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  Keyboard,
  StyleSheet,
} from 'react-native'; // <-- import Keyboard
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {Fonts, FontSizes} from '../../styles/fonts';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Primary, Secondary, SecondaryHint} from '../../constants/Color';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface AppTextInputProps {
  PlaceHolder?: string;
  onChangeText?: (text: string) => void;
  IsAddPress?: () => void;
  Value?: string | number | null;
  Title?: string;
  Editable?: boolean;
  isNumeric?: boolean;
  error?: string;
  touched?: boolean;
  AutoFocus?: boolean;
  onBlur?: (e: any) => void;
  maxLength?: number;
  isRequired?: boolean;
  IsMutipleLine?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  keyboardON?: boolean;
  IsAdd?: boolean;
  OnFocus?: (text: boolean) => void;
}

const AppTextInput: React.FC<AppTextInputProps> = ({
  PlaceHolder,
  onChangeText,
  Value,
  Title,
  Editable = true,
  isNumeric = false,
  error,
  touched,
  AutoFocus = true,
  isRequired = false,
  onBlur,
  maxLength,
  textInputRef,
  keyboardON = false,
  IsMutipleLine = false,
  IsAdd = false,
  IsAddPress,
  OnFocus,
}: AppTextInputProps) => {
  const [inputValue, setInputValue] = useState(Value?.toString() ?? '');
  const isFocused = useIsFocused();
  const [clear, setClear] = useState<boolean>(false);

  useEffect(() => {
    setInputValue(Value?.toString() ?? '');
  }, [Value]);

  // Handle the clear input action
  const handleClearInput = () => {
    setInputValue('');
    setClear(false);
    if (onChangeText) {
      onChangeText('');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    addButton: {
      width: wp('11.3%'),
      height: wp('11.3%'),
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      borderWidth: 1,
      marginRight: wp('2.5%'),
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
      marginLeft: 10,
    },
  });

  return (
    <View style={{marginBottom: 15}}>
      {Title && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: FontSizes.medium,
              marginBottom: hp('1%'),
              fontFamily: Fonts.OnestMedium,
              color: colors.text,
            }}>
            {Title}
          </Text>
          {isRequired && (
            <Text
              style={{
                color: colors.error,
                marginBottom: hp('1%'),
                fontSize: FontSizes.medium,
              }}>
              {' '}
              *
            </Text>
          )}
        </View>
      )}

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View
          style={{
            borderColor: error && touched ? colors.error : colors.border,
            borderRadius: 10,
            borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: wp('2%'),
            paddingVertical: hp('0.2%'),
            justifyContent: 'space-between',
            backgroundColor: colors.surface,
            shadowColor: colors.shadow,
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: isDark ? 0.3 : 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}>
          <TextInput
            ref={textInputRef}
            placeholder={PlaceHolder}
            style={
              !Editable
                ? {
                    fontSize: FontSizes.medium,
                    fontFamily: Fonts.OnestMedium,
                    width: IsAdd ? '85%' : '100%',
                    color: colors.textSecondary,
                  }
                : {
                    fontSize: FontSizes.medium,
                    fontFamily: Fonts.OnestMedium,
                    width: IsAdd ? '85%' : '100%',
                    color: colors.text,
                  }
            }
            placeholderTextColor={colors.placeholder}
            value={inputValue}
            editable={Editable}
            onChangeText={text => {
              setInputValue(text);
              if (onChangeText) onChangeText(text);
            }}
            keyboardType={isNumeric ? 'numeric' : 'default'}
            onBlur={onBlur}
            maxLength={maxLength}
            showSoftInputOnFocus={!keyboardON}
            multiline={IsMutipleLine}
            onFocus={OnFocus}
          />
          {clear && (
            <TouchableOpacity onPress={handleClearInput}>
              <AntDesign
                name="closecircle"
                color={colors.disabled}
                size={hp('3%')}
              />
            </TouchableOpacity>
          )}
        </View>
        {IsAdd && (
          <TouchableOpacity
            style={[
              styles.addButton,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
                shadowColor: colors.shadow,
              },
            ]}
            onPress={IsAddPress}>
            <MaterialCommunityIcons
              name="equalizer"
              color={colors.primary}
              size={hp('3%')}
            />
          </TouchableOpacity>
        )}
      </View>

      {error && touched && (
        <Text style={{color: colors.error, fontSize: 12, marginTop: 5}}>
          {error}
        </Text>
      )}

      {maxLength && inputValue && (
        <Text style={{fontSize: 12, color: colors.textSecondary, marginTop: 5}}>
          {inputValue.length}/{maxLength} characters
        </Text>
      )}
    </View>
  );
};

export default AppTextInput;
