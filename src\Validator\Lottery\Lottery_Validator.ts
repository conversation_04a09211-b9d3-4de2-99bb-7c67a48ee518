import { Activate_Book, Game_Details, Shift_Details } from "../../Types/Lottery/Lottery_Types";
import { getFormateDate } from "../../utils/PublicHelper";


export const applyDefaultsShiftDetails = (data: Partial<Shift_Details>): Shift_Details => {
    return {
      Shift_ID: data.Shift_ID || '',
      Shift_Cashier_ID: data.Shift_Cashier_ID || '',
      Shift_Start_Time: data.Shift_Start_Time || new Date(0),
      Shift_End_Time: data.Shift_End_Time || null,
      Shift_Status: data.Shift_Status !== undefined ? data.Shift_Status : false,
    };
  };
  

  export const applyDefaultsActivateBook = (data: Partial<Activate_Book>): Activate_Book => {
    return {
      Game_ID: data.Game_ID || '',
      Book_No: data.Book_No || '',
      Book_Created: data.Book_Created || new Date(0),
      Book_Tickets: data.Book_Tickets || 0,
      CreatedBy: data.CreatedBy || '',
      Location: data.Location || '',
      ItemNum: data.ItemNum || '',
      Open_Serial: data.Open_Serial || null
    };
  };

  export const applyDefaultsGameDetails = (data: Partial<Game_Details>): Game_Details => {
    return {
      Game_ID: data.Game_ID || '',
      Open_Book: data.Open_Book || '',
      Close_Book: data.Close_Book || '',
      Stock_Level_Open: data.Stock_Level_Open || 0,
      Stock_Level_Close: data.Stock_Level_Close || null,
      Shift_Open_Serial: data.Shift_Open_Serial || null,
      Shift_Close_Serial: data.Shift_Close_Serial || null,
      Missing_Ticket: data.Missing_Ticket || 0,
      Location: data.Location || '',
      Shift_ID: data.Shift_ID || '',
      Date: data.Date || getFormateDate(Date()),
      Reset: data.Reset || null,
      Shift_Start_Skip: data.Shift_Start_Skip || null,
      Shift_End_Skip: data.Shift_End_Skip || null
    };
  };

  
  