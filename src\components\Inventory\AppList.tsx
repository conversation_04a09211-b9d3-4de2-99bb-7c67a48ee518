import React, {useState} from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  FlatListProps,
  Text,
  StyleSheet,
  Button,
  TouchableOpacity,
} from 'react-native';
import LottieView from 'lottie-react-native';
import AppLoader from './AppLoader';
import {Fonts, FontSizes} from '../../styles/fonts';
import {Backround, green, Orange, Primary, Red} from '../../constants/Color';
import {useThemeColors} from '../../Theme/useThemeColors';

// Define a generic type for your data items (you can adjust this based on the structure of your data)
interface DataListProps<T> {
  data: T[];
  renderItem: (item: {item: T; index: number}) => React.ReactNode;
  loading: boolean;
  keyExtractor?: (item: T) => string;
  ListFooterComponent?: React.ReactNode;
  Hight?: string;
  IsCreateMessage?: string;
  IsCreateBtnLabel?: string;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  onRefresh?: () => void;
  onIsCreate?: () => void;
  refreshing?: boolean;
  IsCreate?: boolean;
  extraData?: any;
  GetItemLayout?: any;
  onViewableItemsChanged?: any;
  viewabilityConfig?: any;
  flatListRef?: any;
}

function DataList<T>({
  data,
  renderItem,
  loading,
  keyExtractor,
  ListFooterComponent,
  Hight,
  onEndReached,
  onEndReachedThreshold,
  refreshing = false,
  onRefresh,
  extraData,
  IsCreate = false,
  IsCreateMessage,
  IsCreateBtnLabel,
  onIsCreate,
  GetItemLayout,
  onViewableItemsChanged,
  viewabilityConfig,
  flatListRef,
}: DataListProps<T>) {
  let animation;
  const [appLoader, setAppLoader] = useState<boolean>(true);

  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
  });

  return (
    <View>
      {loading ? (
        <AppLoader modalVisible={appLoader} setModalVisible={setAppLoader} />
      ) : data.length === 0 ? (
        <View style={styles.container}>
          <View style={styles.animationContainer}>
            <LottieView
              ref={animation}
              style={styles.lottie}
              source={require('../../assets/Lotties/Nodata.json')}
              autoPlay
              loop
            />
          </View>
          {IsCreate && (
            <View>
              <Text
                style={{
                  fontSize: FontSizes.small,
                  color: colors.error,
                  fontFamily: Fonts.OnestMedium,
                  paddingVertical: 10,
                  paddingHorizontal: 50,
                  textAlign: 'center',
                }}>
                {IsCreateMessage}
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary,
                  paddingVertical: 5,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginHorizontal: 120,
                  borderRadius: 30,
                }}
                onPress={onIsCreate}>
                <Text
                  style={{
                    fontSize: FontSizes.small,
                    color: '#FFFFFF',
                    fontFamily: Fonts.OnestMedium,
                    textAlign: 'center',
                  }}>
                  {IsCreateBtnLabel}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          style={{height: Hight}}
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.name}-${index}`}
          ListFooterComponent={
            loading ? (
              <ActivityIndicator size="large" color={colors.primary} />
            ) : null
          }
          onEndReached={onEndReached}
          onEndReachedThreshold={onEndReachedThreshold}
          refreshing={refreshing}
          onRefresh={onRefresh}
          extraData={extraData}
          showsVerticalScrollIndicator={false}
          getItemLayout={GetItemLayout}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          removeClippedSubviews={false}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          updateCellsBatchingPeriod={50}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
          }}
        />
      )}
    </View>
  );
}

export default DataList;
