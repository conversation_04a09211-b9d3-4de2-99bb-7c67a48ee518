import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { Primary } from '../../constants/Color'
type Props ={
  Name: string;
  Onpress: () => void;
}

const LotteryHomeCart = (props: Props) => {
  return (
    <TouchableOpacity style={{borderWidth: 2, borderColor: Primary, paddingHorizontal: 10, paddingVertical: 25, alignItems: 'center', justifyContent: 'center'}} onPress={props.Onpress}>
        <Text>{props.Name}</Text>
    </TouchableOpacity>
  )
}

export default LotteryHomeCart