import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

type Props = {
  Reference: string | null;
  CreatedDate: string;
  OnPress: () => void;
  OnPressDelete?: () => void;
  GrandTotal: number;
};

const ItemPickListCart = ({
  Reference,
  CreatedDate,
  OnPress,
  OnPressDelete,
  GrandTotal,
}: Props) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      borderRadius: wp('3%'),
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1.2%'),
      marginVertical: hp('0.4%'),
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      flexDirection: 'row',
      alignItems: 'center',
      elevation: 1,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.2 : 0.05,
      shadowRadius: 2,
    },
    contentContainer: {
      flex: 1,
      marginRight: wp('2%'),
    },
    topRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: hp('0.4%'),
    },
    referenceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      marginRight: wp('2%'),
    },
    referenceText: {
      fontSize: FontSizes.small - 1,
      fontFamily: Fonts.OnestSemiBold,
      color: colors.text,
      marginLeft: wp('1%'),
      flex: 1,
    },
    bottomRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    dateText: {
      fontSize: FontSizes.small - 2,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      marginLeft: wp('1%'),
    },
    totalContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: isDark ? colors.surface : '#E3F2FD',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('0.4%'),
      borderRadius: wp('2%'),
      borderWidth: 0.5,
      borderColor: colors.primary + '30',
      minWidth: wp('20%'),
    },
    totalText: {
      fontSize: FontSizes.small,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
      marginLeft: wp('1%'),
      textAlign: 'center',
    },
    deleteButton: {
      backgroundColor: isDark ? colors.surface : '#FFEBEE',
      padding: wp('2.5%'),
      borderRadius: wp('2%'),
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 0.5,
      borderColor: colors.error + '30',
      alignSelf: 'center',
    },
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={OnPress}
      activeOpacity={0.7}>
      <View style={styles.contentContainer}>
        {/* Top Row: Reference and Total */}
        <View style={styles.topRow}>
          <View style={styles.referenceContainer}>
            <MaterialCommunityIcons
              name="file-document-outline"
              color={colors.primary}
              size={14}
            />
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.referenceText}>
              {Reference || 'N/A'}
            </Text>
          </View>

          <View style={styles.totalContainer}>
            <MaterialCommunityIcons
              name="cash-multiple"
              color={colors.primary}
              size={12}
            />
            <Text style={styles.totalText}>${GrandTotal.toFixed(2)}</Text>
          </View>
        </View>

        {/* Bottom Row: Date */}
        <View style={styles.bottomRow}>
          <View style={styles.dateContainer}>
            <MaterialCommunityIcons
              name="calendar-outline"
              color={colors.textSecondary}
              size={12}
            />
            <Text style={styles.dateText}>{formatDate(CreatedDate)}</Text>
          </View>
        </View>
      </View>

      {/* Delete Button */}
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={OnPressDelete}
        activeOpacity={0.7}>
        <MaterialCommunityIcons
          name="delete-outline"
          color={colors.error}
          size={16}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default ItemPickListCart;
