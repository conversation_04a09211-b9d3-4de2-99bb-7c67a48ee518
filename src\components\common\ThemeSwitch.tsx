// components/ThemeSwitch.js
import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Switch} from 'react-native';
import {useTheme} from '../../Theme/ThemeContext';
import {useThemeColors} from '../../Theme/useThemeColors';

export const ThemeSwitch = () => {
  const {
    theme,
    isDark,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
  } = useTheme();
  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      padding: 20,
      backgroundColor: colors.surface,
      borderRadius: 12,
      margin: 16,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 16,
    },
    option: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    optionText: {
      fontSize: 16,
      color: colors.text,
    },
    activeOption: {
      color: colors.primary,
      fontWeight: '600',
    },
    toggleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
    },
    toggleText: {
      fontSize: 16,
      color: colors.text,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Theme Settings</Text>

      {/* Quick Toggle */}
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleText}>Dark Mode</Text>
        <Switch
          value={isDark}
          onValueChange={toggleTheme}
          trackColor={{false: colors.border, true: colors.primary}}
          thumbColor={isDark ? colors.background : colors.text}
        />
      </View>

      {/* Theme Options */}
      <TouchableOpacity style={styles.option} onPress={setLightTheme}>
        <Text
          style={[styles.optionText, theme === 'light' && styles.activeOption]}>
          Light
        </Text>
        {theme === 'light' && <Text style={styles.activeOption}>✓</Text>}
      </TouchableOpacity>

      <TouchableOpacity style={styles.option} onPress={setDarkTheme}>
        <Text
          style={[styles.optionText, theme === 'dark' && styles.activeOption]}>
          Dark
        </Text>
        {theme === 'dark' && <Text style={styles.activeOption}>✓</Text>}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.option, {borderBottomWidth: 0}]}
        onPress={setSystemTheme}>
        <Text
          style={[
            styles.optionText,
            theme === 'system' && styles.activeOption,
          ]}>
          System
        </Text>
        {theme === 'system' && <Text style={styles.activeOption}>✓</Text>}
      </TouchableOpacity>
    </View>
  );
};
