import React, {useState} from 'react';
import {
  View,
  Button,
  Text,
  Alert,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import DocumentPicker from 'react-native-document-picker';
import RNFS from 'react-native-fs';
import Share from 'react-native-share'; // Import the Share module
import {PurchaseOrder, PurchaseOrderItems} from '../../server/types';

type Props = {
  PurchaseOrder: PurchaseOrder;
  PurchaseItems: PurchaseOrderItems[];
};

const GeneratePDF = ({PurchaseOrder, PurchaseItems}: Props) => {
  const [selectedPath, setSelectedPath] = useState<string | null>(null);

  // Handle selecting the directory
  const handleSelectDirectory = async () => {
    try {
      const res = await DocumentPicker.pickDirectory();
      if (res && res.uri) {
        const mainPath = '/storage/emulated/0/';
        const extractPath = res.uri.replace(
          /^content:\/\/[^/]+\/tree\/primary%3A/,
          '',
        );
        const decodedPath = decodeURIComponent(extractPath);
        const finalPath = mainPath + encodeURIComponent(decodedPath);
        setSelectedPath(finalPath);
      } else {
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
      } else {
        console.error('DocumentPicker Error: ', err);
        Alert.alert('Error', 'Failed to select a directory');
      }
    }
  };

  // Request storage permission for Android
  const requestWritePermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      );
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        Alert.alert('Permission Denied', 'Storage permission is required');
        return false;
      }
    }
    return true;
  };

  // Generate PDF
  const generatePDF = async () => {
    if (!selectedPath) {
      Alert.alert('No Directory Selected', 'Please select a directory first.');
      return;
    }

    // Request storage permissions (for Android)
    const hasPermission = await requestWritePermissions();
    if (!hasPermission) return;

    // Dynamically create HTML content based on PurchaseOrder and PurchaseOrderItems data
    const generateOrderItemsHTML = () => {
      return PurchaseItems.map(item => {
        return `
          <tr>
            <td>${item.ItemName}</td>
            <td>${item.Quan_Ordered}</td>
            <td>${item.CostPer}</td>
            <td>${(item.CasePack * item.NumberPerCase).toFixed(2)}</td>
          </tr>
        `;
      }).join('');
    };

    // Create the HTML content for the PDF
    const htmlContent = `
      <html>
        <body style="font-family: Arial, sans-serif;">
          <h1 style="text-align: center;">Purchase Order</h1>
          <h3 style="text-align: center;">PO #${PurchaseOrder?.PO_Number}</h3>
          <p style="font-size: 16px; margin-top: 20px;">Supplier: ${
            PurchaseOrder?.Vendor_Number
          }</p>
          <p style="font-size: 16px;">Order Date: ${PurchaseOrder?.DateTime}</p>

          <h3 style="margin-top: 20px;">Order Details:</h3>
          <table border="1" style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr>
                <th style="padding: 8px; text-align: left;">Item</th>
                <th style="padding: 8px; text-align: left;">Quantity</th>
                <th style="padding: 8px; text-align: left;">Unit Price</th>
                <th style="padding: 8px; text-align: left;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${generateOrderItemsHTML()}
            </tbody>
          </table>

          <h3 style="margin-top: 20px;">Total Amount: $${PurchaseOrder?.Total_Cost.toFixed(
            2,
          )}</h3>
        </body>
      </html>
    `;

    // Convert HTML to PDF
    const options = {
      html: htmlContent,
    };

    try {
      const file = await RNHTMLtoPDF.convert(options);
      const filePath = file.filePath;
      const newFilePath = `${selectedPath}/${
        'Purchase_Order_' + PurchaseOrder?.PO_Number
      }.pdf`;

      // Save the generated PDF file to the selected directory
      await RNFS.moveFile(filePath, newFilePath);
      Alert.alert('Success', `PDF saved at ${newFilePath}`);

      // Share the PDF
      sharePDF(newFilePath);
    } catch (error) {
      console.error('Error generating or saving PDF:', error);
      Alert.alert('Error', 'Failed to generate PDF');
    }
  };

  // Share the PDF
  const sharePDF = async (filePath: string) => {
    try {
      const options = {
        title: 'Share Purchase Order PDF',
        url: `file://${filePath}`, // Use the correct file URL format for your platform
        type: 'application/pdf',
      };
      await Share.open(options);
    } catch (error) {
      console.error('Error sharing the PDF:', error);
      Alert.alert('Error', 'Failed to share PDF');
    }
  };

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <View style={{flexDirection: 'row', gap: 30}}>
        <Button title="Select Directory" onPress={handleSelectDirectory} />
        <Button title="Generate PDF" onPress={generatePDF} />
      </View>
    </View>
  );
};

export default GeneratePDF;
