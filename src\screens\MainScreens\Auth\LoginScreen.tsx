import React, {useState, useEffect} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  CommonActions,
  useNavigation,
  useNavigationState,
} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {Employee} from '../../../server/types';
import {getOrganizationStatus, login} from '../../../services/apiService';
import {MaterialColors} from '../../../constants/MaterialColors';
import Header from '../../../components/Inventory/Header';
import {showAlert} from '../../../utils/PublicHelper';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {Primary} from '../../../constants/Color';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import TrialExpiredModal from '../../SubScreens/Auth/TrialExpiredModal';

const LoginScreen = () => {
  const navigation = useNavigation();
  const navigationState = useNavigationState(state => state);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [exists, setExists] = useState<Employee[]>([]);
  const [showTrialExpiredModal, setShowTrialExpiredModal] = useState(false);
  const [trialExpiredUserEmail, setTrialExpiredUserEmail] = useState('');

  // Check if user came from logout (no navigation history)
  // When users logout or get disconnected, they navigate here using navigation.reset()
  // which clears the navigation stack, so index will be 0
  const canGoBack = navigationState?.index > 0;

  const ownerPermission = {
    StartOrEndShift: true,
    ActivateBook: true,
    CreateNewGame: true,
    OrganizeSlot: true,
    ViewShift: true,
    ResetShift: true,
    CFA_Inven_Add: true,
    CFA_Inven_Edit: true,
    CFA_Vendors_Add: true,
    CFA_Depts_Add: true,
    CFA_Depts_Edit: true,
    CFA_INVEN_VIEW: true,
    CFA_HH_Create_PO: true,
    CFA_HH_DSD: true,
    CFA_HH_Inv_Count: true,
    CFA_HH_PO_Receive: true,
    CFA_HH_Inv_Adjust: true,
    CFA_HH_PRINT_LABELS: true,
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setPasswordVisible(false);
  };

  const handleRegisterPrompt = async (err: string, Navname: string) => {
    const confirmed = await showAlert(err, 'Login Failed', false, 'No', 'Yes');

    if (confirmed) {
      navigation.navigate(Navname);
    }
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      resetForm();
    });

    return unsubscribe;
  }, [navigation]);

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Check if trial period has expired
  const checkTrialPeriod = (userData: any): boolean => {
    if (userData.trialEndDate) {
      const trialEndDate = new Date(userData.trialEndDate);
      const currentDate = new Date();
      return currentDate > trialEndDate;
    }
    return false;
  };

  // Handle trial expired modal close
  const handleTrialExpiredModalClose = () => {
    setShowTrialExpiredModal(false);
    setTrialExpiredUserEmail('');
  };

  // Handle successful activation from trial expired modal
  const handleTrialExpiredActivation = async () => {
    try {
      // Proceed with normal login flow after activation
      const userData = await AsyncStorage.getItem('userData');
      const organizationData = await AsyncStorage.getItem('organizationData');

      if (userData && organizationData) {
        const parsedUserData = JSON.parse(userData);
        const parsedOrgData = JSON.parse(organizationData);

        if (parsedUserData.role === 'owner') {
          await AsyncStorage.setItem(
            'LOTTERY_PERMISSIONS',
            JSON.stringify(ownerPermission),
          );
          await AsyncStorage.setItem('SWIPEID', '01');
        } else {
          loginEmployee(parsedUserData?.employeeId);
        }

        Alert.alert(
          'Success',
          `Welcome back, ${parsedUserData.name}! Your license has been activated.`,
          [
            {
              text: 'OK',
              onPress: async () => {
                const isLocalIP = await AsyncStorage.getItem('LOCALIP');
                if (
                  !isLocalIP ||
                  isLocalIP === null ||
                  isLocalIP === undefined
                ) {
                  navigation.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [
                        {
                          name: 'ConfigureIp',
                          params: {userData: parsedUserData},
                        },
                      ],
                    }),
                  );
                } else {
                  navigation.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [{name: 'Home'}],
                    }),
                  );
                }
              },
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error handling trial expired activation:', error);
      Alert.alert('Error', 'An error occurred during activation');
    }
  };

  const handleLogin = async () => {
    if (rememberMe) {
      if (!email.trim()) {
        Alert.alert('Error', 'Please enter your email');
        return;
      }

      if (!isValidEmail(email)) {
        Alert.alert('Error', 'Please enter a valid email address');
        return;
      }

      if (!password) {
        Alert.alert('Error', 'Please enter your password');
        return;
      }

      setIsLoading(true);

      const responseData = await login(email, password);

      console.log(responseData, 'responseData');

      setIsLoading(false);

      if (responseData.success) {
        if (responseData.data.organization.status !== 'revoked') {
          // Check if trial period has expired
          const isTrialExpired = checkTrialPeriod(responseData.data.user);

          if (isTrialExpired) {
            // Store user data temporarily for activation
            await AsyncStorage.setItem(
              'userData',
              JSON.stringify(responseData.data.user),
            );
            await AsyncStorage.setItem(
              'organizationData',
              JSON.stringify(responseData.data.organization),
            );

            setTrialExpiredUserEmail(responseData.data.user.email);
            setShowTrialExpiredModal(true);
            setIsLoading(false);
            return;
          }

          if (responseData.data.user.role === 'owner') {
            await AsyncStorage.setItem(
              'LOTTERY_PERMISSIONS',
              JSON.stringify(ownerPermission),
            );

            await AsyncStorage.setItem('SWIPEID', '01');
          } else {
            loginEmployee(responseData.data.user?.employeeId);
          }

          await AsyncStorage.setItem('userToken', responseData.data.token);
          await AsyncStorage.setItem(
            'userData',
            JSON.stringify(responseData.data.user),
          );
          await AsyncStorage.setItem(
            'organizationData',
            JSON.stringify(responseData.data.organization),
          );

          Alert.alert(
            'Success',
            `Welcome back, ${responseData.data.user.name}!`,
            [
              {
                text: 'OK',
                onPress: async () => {
                  const isLocalIP = await AsyncStorage.getItem('LOCALIP');
                  if (
                    !isLocalIP ||
                    isLocalIP === null ||
                    isLocalIP === undefined
                  ) {
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'ConfigureIp',
                            params: {userData: responseData.data.user},
                          },
                        ],
                      }),
                    );
                  } else {
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [{name: 'Home'}],
                      }),
                    );
                  }
                },
              },
            ],
          );
        } else {
          Alert.alert('Organization Revoked');
          return;
        }
      } else {
        const errorText = responseData.error?.toLowerCase() || '';

        if (errorText.includes("account doesn't exist")) {
          handleRegisterPrompt(responseData.error, 'RegistrationScreen');
        } else {
          handleRegisterPrompt(responseData.error, 'ForgotPassword');
        }
      }
    } else {
      setIsLoading(true);
      const LoginUser = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/LoginUser/:Password',
        {Password: password},
      );

      if (Array.isArray(LoginUser) && LoginUser.length === 0) {
        Alert.alert('User Not Found!');
        setIsLoading(false);
        return;
      } else {
        const responseData = await getOrganizationStatus(
          LoginUser[0]?.Organization_ID,
        );

        if (responseData.success) {
          if (responseData.data.status === 'revoked') {
            Alert.alert('Organization Revoked');
            setIsLoading(false);
            return;
          } else {
            loginEmployee(LoginUser[0]?.Cashier_ID);
            await AsyncStorage.setItem(
              'userData',
              JSON.stringify(LoginUser[0]),
            );
            await AsyncStorage.setItem('ISEMPLOYEELOGGED', String(true));

            setIsLoading(false);
            Alert.alert('Success', `Welcome back, ${LoginUser[0]?.Emp_Name}!`, [
              {
                text: 'OK',
                onPress: async () => {
                  const isLocalIP = await AsyncStorage.getItem('LOCALIP');
                  if (
                    !isLocalIP ||
                    isLocalIP === null ||
                    isLocalIP === undefined
                  ) {
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'ConfigureIp',
                            params: {userData: LoginUser[0]},
                          },
                        ],
                      }),
                    );
                  } else {
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [{name: 'Home'}],
                      }),
                    );
                  }
                },
              },
            ]);
          }
        } else {
          Alert.alert('Network Error!');
        }
      }
    }
  };

  const loginEmployee = async (cashier: string) => {
    const swipeLogin = await GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/loginEmployee/:Cashier_ID',
      setExists,
      {Cashier_ID: cashier},
    );
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetEmployeePermission/:Emp_ID',
      {Emp_ID: cashier},
    );

    const employeeData = swipeLogin[0];
    const lotteryPermissions = getBarcode
      ? {
          StartOrEndShift: getBarcode[0].StartOrEndShift || false,
          ActivateBook: getBarcode[0].ActivateBook || false,
          CreateNewGame: getBarcode[0].CreateNewGame || false,
          OrganizeSlot: getBarcode[0].OrganizeSlot || false,
          ViewShift: getBarcode[0].ViewShift || false,
          ResetShift: getBarcode[0].ResetShift || false,
          CFA_Inven_Add: getBarcode[0]?.CFA_Inven_Add || false,
          CFA_Inven_Edit: getBarcode[0]?.CFA_Inven_Edit || false,
          CFA_Vendors_Add: getBarcode[0]?.CFA_Vendors_Add || false,
          CFA_Depts_Add: getBarcode[0]?.CFA_Depts_Add || false,
          CFA_Depts_Edit: getBarcode[0]?.CFA_Depts_Edit || false,
          CFA_INVEN_VIEW: getBarcode[0]?.CFA_INVEN_VIEW || false,
          CFA_HH_Create_PO: getBarcode[0]?.CFA_HH_Create_PO || false,
          CFA_HH_DSD: getBarcode[0]?.CFA_HH_DSD || false,
          CFA_HH_Inv_Count: getBarcode[0]?.CFA_HH_Inv_Count || false,
          CFA_HH_PO_Receive: getBarcode[0]?.CFA_HH_PO_Receive || false,
          CFA_HH_Inv_Adjust: getBarcode[0]?.CFA_HH_Inv_Adjust || false,
          CFA_HH_PRINT_LABELS: getBarcode[0]?.CFA_HH_PRINT_LABELS || false,
        }
      : {
          StartOrEndShift: false,
          ActivateBook: false,
          CreateNewGame: false,
          OrganizeSlot: false,
          ViewShift: false,
          ResetShift: false,
          CFA_Inven_Add: false,
          CFA_Inven_Edit: false,
          CFA_Vendors_Add: false,
          CFA_Depts_Add: false,
          CFA_Depts_Edit: false,
          CFA_INVEN_VIEW: false,
          CFA_HH_Create_PO: false,
          CFA_HH_DSD: false,
          CFA_HH_Inv_Count: false,
          CFA_HH_PO_Receive: false,
          CFA_HH_Inv_Adjust: false,
          CFA_HH_PRINT_LABELS: false,
        };

    await AsyncStorage.setItem('SWIPEID', cashier);
    await AsyncStorage.setItem('EMPLOYEE', JSON.stringify(employeeData));
    await AsyncStorage.setItem(
      'LOTTERY_PERMISSIONS',
      JSON.stringify(lotteryPermissions),
    );
  };

  const handleForgotPassword = () => {
    resetForm();
    navigation.navigate('ForgotPassword');
  };

  // Custom back button handler
  // This handles the back button behavior based on how the user arrived at the login screen:
  // - If they navigated normally (canGoBack = true) and still have a token, go back
  // - If they came from logout/disconnect (canGoBack = false) or no token, go to Welcome
  const handleBackPress = async () => {
    try {
      // Check if user has a token (not logged out)
      const userToken = await AsyncStorage.getItem('userToken');

      if (canGoBack && userToken) {
        // If there's navigation history and user is still logged in, go back normally
        navigation.goBack();
      } else {
        // If no navigation history or user is logged out, go to Welcome screen
        navigation.navigate('Welcome');
      }
    } catch (error) {
      console.error('Error checking user token:', error);
      // Fallback to Welcome screen on error
      navigation.navigate('Welcome');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    backButton: {
      padding: 6,
      borderRadius: 20,
    },
    headerTitle: {
      flex: 1,
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
    },
    headerRight: {
      width: 30,
    },
    contentContainer: {
      flex: 1,
      padding: 24,
    },
    scrollContentContainer: {
      flexGrow: 1,
      justifyContent: 'center',
    },
    formContainer: {
      width: '100%',
      maxWidth: 400,
      alignSelf: 'center',
    },
    labelContainer: {
      marginBottom: 6,
    },
    inputLabel: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '500',
    },
    inputContainer: {
      marginBottom: 18,
      position: 'relative',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.card,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    input: {
      fontSize: 16,
      paddingVertical: 14,
      paddingHorizontal: 16,
      color: colors.text,
    },
    eyeIcon: {
      position: 'absolute',
      right: 16,
      top: 14,
      padding: 4,
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: 4,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: rememberMe ? colors.primary : 'transparent',
    },
    checkboxLabel: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 10,
    },
    button: {
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 8,
      marginBottom: 8,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    forgotPasswordButton: {
      alignItems: 'center',
      marginTop: 16,
      marginBottom: 8,
      padding: 8,
    },
    forgotPasswordText: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: '500',
    },
    spacer: {
      height: 16,
    },
    registerButton: {
      alignItems: 'center',
      marginTop: 16,
      padding: 8,
    },
    registerText: {
      color: colors.primary,
      fontSize: 14,
      fontWeight: '500',
    },
    signupContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 10,
    },
  });
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        backgroundColor={colors.surface}
        barStyle={isDark ? 'light-content' : 'dark-content'}
      />
      <View style={styles.header}>
        <Header NavName="Sign In" isProvid={true} Onpress={handleBackPress} />
      </View>

      {/* Content Container */}
      <ScrollView
        style={styles.contentContainer}
        contentContainerStyle={styles.scrollContentContainer}>
        <View style={styles.formContainer}>
          {/* Move checkbox to the top */}
          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => setRememberMe(!rememberMe)}>
              {rememberMe ? <Icon name="check" size={16} color="#fff" /> : null}
            </TouchableOpacity>
            <Text style={styles.checkboxLabel}>Login As Admin</Text>
          </View>

          {/* Add some space after the checkbox */}
          <View style={styles.spacer} />

          {rememberMe && (
            <>
              <View style={styles.labelContainer}>
                <Text style={styles.inputLabel}>Email</Text>
              </View>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="<EMAIL>"
                  placeholderTextColor={colors.placeholder}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
            </>
          )}

          <View style={styles.labelContainer}>
            <Text style={styles.inputLabel}>Password</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="••••••••••"
              placeholderTextColor={colors.placeholder}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!passwordVisible}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setPasswordVisible(!passwordVisible)}>
              <Icon
                name={passwordVisible ? 'eye-off' : 'eye'}
                size={20}
                color={colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleLogin}
            disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.buttonText}>Sign In</Text>
            )}
          </TouchableOpacity>

          {rememberMe && (
            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={handleForgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          )}

          <View style={styles.signupContainer}>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestMedium,
                color: colors.textSecondary,
              }}>
              Dont't Have an Account?{' '}
            </Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('RegistrationScreen')}>
              <Text
                style={{
                  fontSize: FontSizes.large,
                  fontFamily: Fonts.OnestBold,
                  color: colors.primary,
                }}>
                SignUp
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Trial Expired Modal */}
      <TrialExpiredModal
        visible={showTrialExpiredModal}
        onClose={handleTrialExpiredModalClose}
        onActivate={handleTrialExpiredActivation}
        userEmail={trialExpiredUserEmail}
      />
    </SafeAreaView>
  );
};

export default LoginScreen;
