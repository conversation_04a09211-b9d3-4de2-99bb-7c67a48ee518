import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {Inventory} from '../../server/types';
import {MaterialColors} from '../../constants/MaterialColors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface Props {
  Onpress?: () => void;
  OnpressIN?: () => void;
  Item?: Inventory;
  Stock?: number;
  Cost?: number;
  IsActive?: boolean;
  IsCompleted?: boolean;
  isShift?: boolean;
  isLocation?: boolean;
}

const TicketsDetailsForOther = ({
  Onpress,
  OnpressIN,
  Item,
  Stock,
  Cost,
  IsActive,
  IsCompleted,
  isShift = true,
  isLocation,
}: Props) => {
  const [loading, setLoading] = useState<boolean>(false);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const getBorderColor = () => {
    if (IsCompleted) return colors.success;
    if (IsActive) return colors.warning;
    return colors.border;
  };

  const getStatusIcon = () => {
    if (IsCompleted) return 'check-circle';
    if (IsActive) return 'clock-outline';
    return 'information-outline';
  };

  const getStatusColor = () => {
    if (IsCompleted) return colors.success;
    if (IsActive) return colors.warning;
    return colors.textSecondary;
  };

  const styles = StyleSheet.create({
    cardContainer: {
      marginVertical: 4,
      width: '100%',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 10,
      borderWidth: 1.5,
      elevation: 1,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: 2,
    },
    statusIconContainer: {
      marginRight: 8,
      paddingTop: 1,
    },
    headerContainer: {
      flex: 1,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    gameTitle: {
      fontSize: FontSizes.small,
      fontWeight: '600',
      fontFamily: Fonts.OnestBold,
      marginLeft: 4,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    locationText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      marginLeft: 4,
    },
    divider: {
      height: 1,
      marginVertical: 5,
      width: '100%',
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 6,
    },
    infoItem: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoLabel: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      marginLeft: 2,
    },
    infoValue: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
    },
    priceText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
    },
    footerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      width: '100%',
    },
    activateButton: {
      borderRadius: 4,
      paddingVertical: 3,
      paddingHorizontal: 8,
      flexDirection: 'row',
      alignItems: 'center',
      elevation: 1,
    },
    activateButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      marginLeft: 2,
    },
  });

  return (
    <TouchableOpacity
      style={[
        styles.cardContainer,
        {
          borderColor: getBorderColor(),
          backgroundColor: colors.surface,
          shadowColor: colors.shadow,
        },
      ]}
      onPress={isShift ? Onpress : OnpressIN}
      activeOpacity={0.7}>
      <View style={styles.cardContent}>
        <View style={styles.headerContainer}>
          <View style={styles.titleRow}>
            <Icon name="gamepad-variant" size={14} color={colors.primary} />
            <Text style={[styles.gameTitle, {color: colors.text}]}>
              {Item.ItemName.length > 15
                ? Item.ItemName.substring(0, 30)
                : Item.ItemName}
            </Text>
          </View>

          {isLocation && (
            <View style={styles.locationContainer}>
              <Icon name="map-marker" size={14} color={colors.textSecondary} />
              <Text
                style={[
                  styles.locationText,
                  {
                    color:
                      Item?.Location === 'NOTASSIGNED' || !Item?.Location
                        ? colors.error
                        : colors.success,
                  },
                ]}>
                {Item?.Location === 'NOTASSIGNED' || !Item?.Location
                  ? 'N/A'
                  : Item?.Location}
              </Text>
            </View>
          )}

          <View style={[styles.divider, {backgroundColor: colors.border}]} />

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Icon
                name="ticket-confirmation"
                size={14}
                color={colors.textSecondary}
              />
              <Text style={[styles.infoLabel, {color: colors.textSecondary}]}>
                Tickets:
              </Text>
              <Text style={[styles.infoValue, {color: colors.textSecondary}]}>
                {Stock}
              </Text>
            </View>

            <View style={styles.infoItem}>
              <Icon
                name="currency-usd"
                size={14}
                color={colors.textSecondary}
              />
              <Text
                style={[
                  styles.priceText,
                  {color: colors.textSecondary},
                ]}>{`${Cost?.toFixed(2)}`}</Text>
            </View>
          </View>

          <View style={styles.footerRow}>
            <TouchableOpacity
              style={[styles.activateButton, {backgroundColor: colors.primary}]}
              onPress={OnpressIN}>
              {isLocation ? (
                <MaterialIcons
                  name="published-with-changes"
                  size={14}
                  color="#FFFFFF"
                />
              ) : (
                <Icon name="play-circle-outline" size={14} color="#FFFFFF" />
              )}
              <Text style={styles.activateButtonText}>
                {isLocation ? 'Change Location' : 'Activate'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default TicketsDetailsForOther;
