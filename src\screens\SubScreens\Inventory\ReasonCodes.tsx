import {View, Text, Alert, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useCallback, useState} from 'react';
import {Reason_Codes, Setup_Reason_Codes} from '../../../server/types';
import {createItem, deleteItem} from '../../../server/service';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {GetItemsParamsNoFilter, showAlert} from '../../../utils/PublicHelper';
import Header from '../../../components/Inventory/Header';
import DataList from '../../../components/Inventory/AppList';
import AppButton from '../../../components/Inventory/AppButton';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type AdjustStockScreenRouteProp = RouteProp<any, 'ReasonCode'>;

const ReasonCodes: React.FC<{
  route: AdjustStockScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [reasonCodes, setReasonCodes] = useState<Reason_Codes[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectReason, setSelectReason] = useState<string>('5');

  useFocusEffect(
    useCallback(() => {
      geResonInit();
      setSelectReason(selectReason);
    }, []),
  );

  const geResonInit = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/reasoncodes/:Reason_Type',
      setReasonCodes,
      {Reason_Type: selectReason},
      false,
    );
  };

  const DeleteReason = (RemoveRason: Reason_Codes) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const result = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeleteReasonCode/:Reason_Type/:Reason_Code',
            {
              Reason_Type: RemoveRason.Reason_Type,
              Reason_Code: RemoveRason.Reason_Code,
            },
          );
          if (result) {
            GetItemsParamsNoFilter(
              (await getInventoryPort()).toString(),
              '/reasoncodes/:Reason_Type',
              setReasonCodes,
              {Reason_Type: RemoveRason.Reason_Type},
              false,
            );

            setSelectReason(RemoveRason.Reason_Type.toString());
          }
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const renderItem = ({item}: {item: Reason_Codes}) => (
    <View style={styles.reasonCodeCard}>
      <Text style={styles.reasonCodeText}>{item.Reason_Code}</Text>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => DeleteReason(item)}>
        <MaterialCommunityIcons
          name="delete-outline"
          color={colors.text}
          size={18}
        />
      </TouchableOpacity>
    </View>
  );

  const reasonOptions = [
    {label: 'Return To Vendor', value: '8'},
    {label: 'Inventory Adjustment', value: '5'},
  ];

  const getReasonCodesBySelection = async (SelectedReason: string) => {
    if (SelectedReason === '' || !SelectedReason) {
      setSelectReason('');
    } else {
      setSelectReason(SelectedReason);
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/reasoncodes/:Reason_Type',
        setReasonCodes,
        {Reason_Type: SelectedReason},
        false,
      );
    }
  };

  const CreateReason = () => {
    if (selectReason === '' || !selectReason) {
      Alert.alert('Please Select Reason Type!');
    } else {
      navigation.navigate('CreateReasonCode', {ItemData: selectReason});
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      justifyContent: 'space-between',
    },
    mainContainer: {
      paddingHorizontal: wp('2.5%'),
      flex: 1,
    },
    dropdownContainer: {
      marginVertical: hp('1.5%'),
    },
    listContainer: {
      marginTop: hp('1%'),
    },
    totalCountText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
      paddingVertical: hp('1.5%'),
    },
    reasonCodeCard: {
      backgroundColor: colors.card,
      paddingHorizontal: wp('4%'),
      marginBottom: hp('1%'),
      borderRadius: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    reasonCodeText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      paddingVertical: hp('1.5%'),
    },
    deleteButton: {
      padding: 8,
      borderRadius: 8,
      backgroundColor: colors.error,
    },
    bottomButtonContainer: {
      backgroundColor: colors.surface,
      paddingVertical: hp('1.5%'),
      paddingHorizontal: wp('2.5%'),
      position: 'absolute',
      right: 0,
      bottom: 0,
      left: 0,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        <Header NavName="Reason Codes" />

        <View style={styles.dropdownContainer}>
          <AppDropDown
            label="Reason Codes Types"
            options={reasonOptions}
            selectedValue={selectReason}
            onSelect={value => {
              getReasonCodesBySelection(value);
            }}
          />
        </View>

        <View style={styles.listContainer}>
          <Text style={styles.totalCountText}>
            Total Reason Codes: {reasonCodes.length || 0}
          </Text>

          <DataList
            data={reasonCodes}
            renderItem={renderItem}
            loading={loading}
          />
        </View>
      </View>

      <View style={styles.bottomButtonContainer}>
        <FAB
          label={'Create Reason Code'}
          position="bottomRight"
          onPress={() => CreateReason()}
        />
      </View>
    </View>
  );
};

export default ReasonCodes;
