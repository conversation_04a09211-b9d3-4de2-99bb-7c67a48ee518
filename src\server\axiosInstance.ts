// axiosInstance.ts
import axios, { AxiosInstance, AxiosError, AxiosRequestConfig } from 'axios';

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // Start with 1 second

const createAxiosInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    timeout: 30000, // Increased from 10000 to 30000 for large data
  });

  return instance;
};

export default createAxiosInstance;