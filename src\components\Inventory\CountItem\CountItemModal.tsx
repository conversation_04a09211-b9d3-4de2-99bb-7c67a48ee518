import React, {memo} from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import AppButton from '../AppButton';
import {Fonts} from '../../../styles/fonts';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

interface CountItemModalProps {
  visible: boolean;
  stockInput: string;
  onInputChange: (value: string) => void;
  onDone: () => void;
  onClose: () => void;
}

const CountItemModal: React.FC<CountItemModalProps> = ({
  visible,
  stockInput,
  onInputChange,
  onDone,
  onClose,
}) => {
  const colors = useThemeColors();

  console.log(
    'CountItemModal render - visible:',
    visible,
    'stockInput:',
    stockInput,
  );
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999,
    },
    modalContent: {
      backgroundColor: colors.card,
      width: '90%',
      padding: 20,
      borderRadius: 10,
      alignItems: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 10,
      zIndex: 10000,
    },
    modalHeader: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.5%'),
      marginBottom: 10,
      color: colors.text,
    },
    modalInput: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 5,
      width: '100%',
      height: 40,
      paddingHorizontal: 10,
      marginBottom: 20,
      backgroundColor: colors.surface,
      color: colors.text,
    },
    modalCloseButton: {
      padding: 5,
    },
    modalCloseButtonText: {
      fontSize: hp('2.5%'),
      color: colors.primary,
    },
    buttonContainer: {
      width: '45%',
    },
  });

  return (
    <Modal
      animationType="slide"
      transparent
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Enter New Stock</Text>
            <TouchableOpacity onPress={onClose} style={styles.modalCloseButton}>
              <Text style={styles.modalCloseButtonText}>X</Text>
            </TouchableOpacity>
          </View>
          <TextInput
            style={styles.modalInput}
            placeholder="New Stock"
            keyboardType="numeric"
            value={stockInput}
            onChangeText={onInputChange}
            autoFocus
          />
          <View style={styles.buttonContainer}>
            <AppButton Title="Done" OnPress={onDone} />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default memo(CountItemModal);
