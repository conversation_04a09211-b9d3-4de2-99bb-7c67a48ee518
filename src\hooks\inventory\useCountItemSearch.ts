import { useState, useEffect, useCallback, useRef } from 'react';
import { TextInput, Keyboard, Platform } from 'react-native';
import { Inventory_Filter } from '../../server/types';
import { 
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertOK 
} from '../../utils/PublicHelper';
import { getInventoryPort } from '../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useCountItemSearch = (
  masterData: Inventory_Filter[],
  filteredData: Inventory_Filter[],
  page: number,
  action: boolean,
  applyPagination: (data: Inventory_Filter[], page: number) => void,
  setFilteredData: (data: Inventory_Filter[]) => void,
  setLoading: (loading: boolean) => void,
  openModalForItem: (itemNum: string) => void,
  navigation: any
) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');
  const [showLookup, setShowLookup] = useState<boolean>(false);
  const [camera, setCamera] = useState(false);
  
  const textInputRef = useRef<TextInput>(null);

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Focus text input
  const focusTextInput = useCallback(() => {
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  }, []);

  // Clear search and reset focus
  const clearSearchAndFocus = useCallback(() => {
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }
    setSearchQuery('');
    setShowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  }, []);

  // Handle search change
  const onSearchChange = useCallback(async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      { ItemNum: text },
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);

    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          masterData,
          ['ItemName', 'ItemNum'],
          setFilteredData,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', { ItemData: text });
          } else {
            clearSearchAndFocus();
            applyPagination(filteredData, page);
          }
        } else {
          const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
          
          if (lotteryDepartment && lotteryDepartment === getBarcode[0]?.Dept_ID) {
            showAlertOK(
              'This item cannot be counted at the moment due to system restrictions or configuration settings. Please contact support if you believe this is an error',
              'Not Able to Count',
              'OK',
              clearSearchAndFocus,
            );
          } else if (!action) {
            showAlertOK(
              'To proceed, please initialize the physical count by clicking \'Start Physical Count\'',
              'Physical Count Not Started',
              'OK',
              clearSearchAndFocus,
            );
          } else {
            handleSearch(
              getBarcode[0]?.ItemNum || text,
              masterData,
              ['ItemName', 'ItemNum'],
              setFilteredData,
              setLoading,
            );
            openModalForItem(getBarcode[0]?.ItemNum || text);
          }
        }
      }
    } else {
      setSearchQuery('');
      setFilteredData(masterData);
      applyPagination(masterData, 1);
    }
  }, [
    showLookup,
    masterData,
    filteredData,
    page,
    action,
    setFilteredData,
    setLoading,
    applyPagination,
    openModalForItem,
    navigation,
    clearSearchAndFocus,
  ]);

  // Toggle lookup mode
  const toggleLookup = useCallback((checked: boolean) => {
    applyPagination(filteredData, page);
    setShowLookup(checked);
    setSearchQuery('');

    if (Platform.OS === 'android') {
      if (checked) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (checked) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, [filteredData, page, applyPagination]);

  // Handle done click for search
  const handleDoneClickSub = useCallback(() => {
    clearSearchAndFocus();
    applyPagination(filteredData, page);
  }, [clearSearchAndFocus, applyPagination, filteredData, page]);

  // Hide keyboard when tapping outside
  const handleOutsidePress = useCallback(() => {
    if (showLookup) {
      setShowLookup(false);
      Keyboard.dismiss();
    }
  }, [showLookup]);

  return {
    searchQuery,
    debouncedQuery,
    showLookup,
    camera,
    setCamera,
    textInputRef,
    onSearchChange,
    toggleLookup,
    handleDoneClickSub,
    handleOutsidePress,
    focusTextInput,
    clearSearchAndFocus,
  };
};
