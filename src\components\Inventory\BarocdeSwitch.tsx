import {View, Text, Switch} from 'react-native';
import React from 'react';
import {Secondary} from '../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import {useThemeColors} from '../../Theme/useThemeColors';

// Define types for props
interface Props {
  name?: string;
  value?: boolean;
  onValueChange?: (value: boolean) => void;
  Editable?: boolean;
}

const BarocdeSwitch = ({
  name,
  value,
  onValueChange,
  Editable = false,
}: Props) => {
  const colors = useThemeColors();
  return (
    <View
      style={{
        backgroundColor: colors.surface,
        borderRadius: 15,
        paddingHorizontal: wp('2.5%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: hp('2%'),
        marginVertical: hp('0.5%'),
        borderWidth: 1,
        borderColor: colors.border,
      }}>
      <Text
        style={{
          fontFamily: Fonts.OnestBold,
          fontSize: hp('2%'),
          color: colors.text,
        }}>
        {name}
      </Text>
      <Switch
        value={value}
        onValueChange={onValueChange}
        disabled={!Editable}
        trackColor={{false: colors.border, true: colors.primary}}
        thumbColor={value ? '#FFFFFF' : colors.disabled}
      />
    </View>
  );
};

export default BarocdeSwitch;
