import React, {useState} from 'react';
import {
  View,
  Text,
  Modal,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Field} from 'formik';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';

interface CustomDropdownProps {
  label?: string;
  options: {label: string; value: string}[];
  selectedValue: string | number;
  onSelect: (value: string) => void;
  onCreate?: () => void;
  error?: string;
  touched?: boolean;
  disabled?: boolean;
  isAdd?: boolean;
  isNotClear?: boolean;
  isRequired?: boolean;
  IsNotEdit?: boolean;
  placeholder?: string;
}

const SettingDrop: React.FC<CustomDropdownProps> = ({
  label,
  options,
  selectedValue,
  onSelect,
  onCreate,
  error,
  touched,
  disabled,
  isAdd = false,
  isNotClear = false,
  isRequired = false,
  IsNotEdit = false,
  placeholder,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Toggle the visibility of the dropdown
  const toggleDropdown = (): void => setIsOpen(!isOpen);

  // Handle the selection of an option
  const handleSelect = (item: string): void => {
    onSelect(item); // Update the selected value
    setIsOpen(false); // Close the dropdown
  };

  // Handle clearing the selection
  const handleClearSelection = (): void => {
    onSelect(''); // Clear the selected value
    setIsOpen(false); // Close the dropdown
  };

  // Handle disabled dropdown click
  const handleDisabledClick = (): void => {
    // This will be handled by the parent component's onSelect function
    // which can show appropriate messages
    onSelect('');
  };

  return (
    <View style={styles.container}>
      {/* Dropdown label */}
      {label && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {isRequired && <Text style={styles.requiredStar}> *</Text>}
        </View>
      )}

      {/* Selected Value (Displayed before opening dropdown) */}
      <View style={isAdd && styles.addContainer}>
        <TouchableOpacity
          style={[
            isAdd
              ? styles.selectedValueContainerAdd
              : styles.selectedValueContainer,
            error && touched && styles.errorContainer,
            disabled && styles.disabledContainer,
          ]}
          onPress={!disabled ? toggleDropdown : handleDisabledClick}
          activeOpacity={0.7}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons
              name="ticket-outline"
              color={MaterialColors.primary.dark}
              size={16}
            />
          </View>
          <Text style={styles.selectedValue}>
            {IsNotEdit
              ? 'Tag Along Items'
              : selectedValue
              ? options.find(option => option.value === selectedValue)?.label
              : placeholder || 'Select Lottery Department'}
          </Text>
          <View style={styles.chevronContainer}>
            <MaterialCommunityIcons
              name="chevron-down"
              size={20}
              color={MaterialColors.text.secondary}
            />
          </View>
        </TouchableOpacity>

        {isAdd && (
          <TouchableOpacity style={styles.addButton} onPress={onCreate}>
            <MaterialCommunityIcons
              name="plus-circle-outline"
              color={MaterialColors.primary.dark}
              size={28}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Dropdown Modal */}
      <Modal
        transparent={true}
        visible={isOpen}
        animationType="fade"
        onRequestClose={toggleDropdown}>
        <TouchableOpacity
          style={styles.overlay}
          onPress={toggleDropdown}
          activeOpacity={1}>
          <View style={styles.dropdown}>
            <View style={styles.dropdownHeader}>
              <Text style={styles.dropdownTitle}>
                {label || 'Select an option'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={toggleDropdown}>
                <MaterialCommunityIcons
                  name="close"
                  size={20}
                  color={MaterialColors.text.secondary}
                />
              </TouchableOpacity>
            </View>

            {/* Clear Selection Button */}
            {!isNotClear && (
              <TouchableOpacity
                style={styles.clearOption}
                onPress={handleClearSelection}>
                <Text style={styles.clearOptionText}>Clear Selection</Text>
              </TouchableOpacity>
            )}

            {/* Options List */}
            <FlatList
              data={options}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={[
                    styles.option,
                    selectedValue === item.value && styles.selectedOption,
                  ]}
                  onPress={() => handleSelect(item.value)}>
                  <Text
                    style={[
                      styles.optionText,
                      selectedValue === item.value && styles.selectedOptionText,
                    ]}>
                    {item.label}
                  </Text>
                  {selectedValue === item.value && (
                    <MaterialCommunityIcons
                      name="check"
                      size={16}
                      color={MaterialColors.primary.dark}
                    />
                  )}
                </TouchableOpacity>
              )}
              keyExtractor={item => item.value}
              showsVerticalScrollIndicator={true}
            />
          </View>
        </TouchableOpacity>
      </Modal>

      {error && touched && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  label: {
    fontSize: 14,
    fontFamily: Fonts.OnestMedium,
    color: MaterialColors.text.primary,
  },
  requiredStar: {
    color: MaterialColors.error.main,
    fontSize: 14,
  },
  addContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  selectedValueContainer: {
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: MaterialColors.surface,
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedValueContainerAdd: {
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: MaterialColors.surface,
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  iconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    backgroundColor: MaterialColors.primary.light,
  },
  selectedValue: {
    fontSize: 12,
    fontFamily: Fonts.OnestBold,
    color: MaterialColors.text.primary,
    flex: 1,
    paddingHorizontal: 12,
  },
  chevronContainer: {
    paddingLeft: 8,
  },
  errorContainer: {
    borderColor: MaterialColors.error.main,
  },
  disabledContainer: {
    opacity: 0.6,
  },
  addButton: {
    backgroundColor: MaterialColors.surface,
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: MaterialColors.grey[300],
  },
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  dropdown: {
    backgroundColor: MaterialColors.surface,
    width: '100%',
    maxHeight: '70%',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[200],
  },
  dropdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: MaterialColors.text.primary,
  },
  closeButton: {
    padding: 4,
  },
  clearOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[200],
  },
  clearOptionText: {
    fontSize: 15,
    color: MaterialColors.primary.main,
    fontFamily: Fonts.OnestMedium,
  },
  option: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: MaterialColors.grey[100],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedOption: {
    backgroundColor: MaterialColors.primary.lighter,
  },
  optionText: {
    fontSize: 15,
    color: MaterialColors.text.primary,
    fontFamily: Fonts.OnestMedium,
  },
  selectedOptionText: {
    color: MaterialColors.primary.main,
    fontWeight: '600',
  },
  errorText: {
    color: MaterialColors.error.main,
    fontSize: 12,
    marginTop: 5,
    fontFamily: Fonts.OnestMedium,
  },
});

export default SettingDrop;
