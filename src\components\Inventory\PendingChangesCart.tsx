import { View, Text } from 'react-native'
import React from 'react'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

type Props ={
    Name: string
}

const PendingChangesCart = (props: Props) => {
  return (
    <View>
     <View style={{}}>
        <Text style={{marginBottom: 10}}>{props.Name}</Text>

       <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', borderWidth: 2, borderColor: '#D2D2D2', borderRadius: 10, paddingHorizontal: 10, paddingVertical: 10}}>
        <View>
            <Text>New Item</Text>
            <Text>LundBerg Brown Rice Syrup - Count: 12</Text>
        </View>

        <View>
            <MaterialCommunityIcons name="chevron-right" color={"#000"} size={25} />
        </View>
       </View>

       <View style={{flexDirection: 'row', marginTop: 10, justifyContent: 'space-between', alignItems: 'center', borderWidth: 2, borderColor: '#D2D2D2', borderRadius: 10, paddingHorizontal: 10, paddingVertical: 10}}>
        <View>
            <Text>New Item</Text>
            <Text>LundBerg Brown Rice Syrup - Count: 12</Text>
        </View>

        <View>
            <MaterialCommunityIcons name="chevron-right" color={"#000"} size={25} />
        </View>
       </View>

     </View>
    </View>
  )
}

export default PendingChangesCart