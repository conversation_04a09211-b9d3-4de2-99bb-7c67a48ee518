import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import AppDropDown from '../../../components/Inventory/AppDropDown'
import AppTextInput from '../../../components/Inventory/AppTextInput'
import AppButton from '../../../components/Inventory/AppButton'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'

type NavProps = {
  navigation: NativeStackNavigationProp<any>; 
};

const DirectStoreDeposits: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: "100%", height: "100%"}}>
      <Header NavName='Credit DSD'/>

      <View style={{paddingHorizontal:10 }}>
        <AppDropDown />
        <AppTextInput PlaceHolder='Enter Refrence Number'/>

        <View style={{flexDirection: 'row', justifyContent: 'space-between', marginVertical: 10}}>
        <Text style={{fontSize: 18, fontWeight: 'bold'}}>Due Date</Text>
        <Text style={{fontSize: 18, fontWeight: 'bold'}}>jun30, 2024</Text>
        </View>
        <AppTextInput PlaceHolder='Enter Ship Via '/>

        <View>
            <AppButton Title='Add Items' OnPress={() => navigation.navigate("DSD")}/>
        </View>
      </View>
    </View>
  )
}

export default DirectStoreDeposits