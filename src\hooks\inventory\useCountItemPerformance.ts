import { useCallback, useMemo } from 'react';
import { Inventory_Filter } from '../../server/types';
import { CountItemInput } from './useCountItemData';

export const useCountItemPerformance = (
  displayData: Inventory_Filter[],
  inputValues: CountItemInput[],
  actionRef: React.MutableRefObject<boolean>
) => {
  // Memoized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    []
  );

  // Memoized getItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    []
  );

  // Memoized input values map for faster lookups
  const inputValuesMap = useMemo(() => {
    const map = new Map<string, string>();
    inputValues.forEach(input => {
      map.set(input.itemNum, input.value);
    });
    return map;
  }, [inputValues]);

  // Memoized function to get input value for an item
  const getInputValue = useCallback(
    (itemNum: string) => inputValuesMap.get(itemNum) || '',
    [inputValuesMap]
  );

  // Memoized function to check if item has input value
  const hasInputValue = useCallback(
    (itemNum: string) => {
      const value = inputValuesMap.get(itemNum);
      return actionRef.current && value !== undefined && value !== '';
    },
    [inputValuesMap, actionRef]
  );

  // FlatList performance props
  const flatListProps = useMemo(
    () => ({
      initialNumToRender: 10,
      maxToRenderPerBatch: 10,
      windowSize: 10,
      removeClippedSubviews: true,
      updateCellsBatchingPeriod: 75,
      showsVerticalScrollIndicator: false,
      maintainVisibleContentPosition: {
        minIndexForVisible: 0,
      },
    }),
    []
  );

  return {
    keyExtractor,
    getItemLayout,
    getInputValue,
    hasInputValue,
    flatListProps,
  };
};
