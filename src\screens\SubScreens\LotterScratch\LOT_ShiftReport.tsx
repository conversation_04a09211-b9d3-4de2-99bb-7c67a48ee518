import {View, Text, TouchableOpacity, Modal, StyleSheet} from 'react-native';
import React, {useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import DataList from '../../../components/Inventory/AppList';
import {
  GetAllItems,
  GetItemsParamsNoFilterNoReturn,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {Shift_Details} from '../../../Types/Lottery/Lottery_Types';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import DateTimePicker from '@react-native-community/datetimepicker';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {Employee} from '../../../server/types';
import {MaterialColors} from '../../../constants/MaterialColors';
import AssignBookCart from '../../../components/LotteryScratch/AssignBookCart';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const LOT_ShiftReport: React.FC<NavProps> = ({navigation}) => {
  const [shiftData, setShiftData] = useState<Shift_Details[]>([]);
  const [employee, setEmployee] = useState<Employee[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState<string>('daily');
  const [modalVisible, setModalVisible] = useState(false);
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);
  const [tempStartDate, setTempStartDate] = useState<Date | null>(null);
  const [tempEndDate, setTempEndDate] = useState<Date | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [filteredShiftData, setFilteredShiftData] = useState<Shift_Details[]>(
    [],
  );

  useEffect(() => {
    checkServerConnection();
  }, []);

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      getShiftDetails();
    }
  };
  const getShiftDetails = async () => {
    const data = await GetAllItems<Shift_Details[]>(
      (await getLotteryPort()).toString(),
      '/GetAllShift',
      setShiftData,
      setLoading,
    );

    GetAllItems<Employee[]>(
      (await getInventoryPort()).toString(),
      '/getAllEmployee',
      setEmployee,
      setLoading,
    );
  };

  const renderItem = async ({item}: {item: Shift_Details}) => {
    const GetShiftDetails = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetShiftDetails/:Shift_ID',
      {Shift_ID: item.Shift_ID},
    );

    const CashierName = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/loginEmployee/:Cashier_ID',
      {Cashier_ID: GetShiftDetails[0]?.Shift_Cashier_ID},
    );

    const GetGameDetails = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetAllShiftAllGames/:Shift_ID',
      {Shift_ID: item.Shift_ID},
    );
    const TicketMissedFound = GetGameDetails.some(
      item => item.Missing_Ticket > 0,
    );

    return (
      <AssignBookCart
        key={item.Shift_ID}
        CashierID={item.Shift_Cashier_ID}
        Date={new Date(item?.Shift_End_Time).toISOString().split('T')[0]}
        ShiftID={item.Shift_ID}
        Onpress={() => navigation.navigate('FinalizePage', {ItemData: item})}
        CashierName={CashierName[0]?.EmpName}
        IsShiftReport={TicketMissedFound && true}
      />
    );
  };

  useEffect(() => {
    filterData();
  }, [selectedFilter, startDate, endDate, shiftData]);

  const filterData = () => {
    const now = new Date();
    let filteredData = shiftData;

    if (selectedFilter === 'daily') {
      const past24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      filteredData = shiftData.filter(
        item => new Date(item.Shift_Start_Time) >= past24Hours,
      );
    } else if (selectedFilter === 'weekly') {
      const pastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filteredData = shiftData.filter(
        item => new Date(item.Shift_Start_Time) >= pastWeek,
      );
    } else if (selectedFilter === 'monthly') {
      const pastMonth = new Date();
      pastMonth.setMonth(pastMonth.getMonth() - 1);
      filteredData = shiftData.filter(
        item => new Date(item.Shift_Start_Time) >= pastMonth,
      );
    } else if (selectedFilter === 'yearly') {
      const pastYear = new Date();
      pastYear.setFullYear(pastYear.getFullYear() - 1);
      filteredData = shiftData.filter(
        item => new Date(item.Shift_Start_Time) >= pastYear,
      );
    } else if (selectedFilter === 'custom' && startDate && endDate) {
      filteredData = shiftData.filter(item => {
        const itemDate = new Date(item.Shift_Start_Time);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    setFilteredShiftData(filteredData);
  };

  const handleApplyFilter = () => {
    if (!tempStartDate || !tempEndDate) return;
    setStartDate(tempStartDate);
    setEndDate(tempEndDate);
    setSelectedFilter('custom');
    setModalVisible(false);
    filterData();
  };

  const handleClearFilter = () => {
    setTempStartDate(null);
    setTempEndDate(null);
    setSelectedFilter('daily');
    setModalVisible(false);
    filterData();
  };

  const setStartTime = (date: Date) => {
    const newDate = new Date(date);
    return new Date(
      newDate.getFullYear(),
      newDate.getMonth(),
      newDate.getDate(),
      0,
      0,
      0,
      0,
    );
  };

  const setEndTime = (date: Date) => {
    const newDate = new Date(date);
    return new Date(
      newDate.getFullYear(),
      newDate.getMonth(),
      newDate.getDate(),
      23,
      59,
      59,
      999,
    );
  };

  const employeeOption = employee.map(dept => ({
    label: dept.EmpName,
    value: dept.Cashier_ID,
  }));

  const filterByEmployee = (selectedEmployee: string) => {
    setSelectedEmployee(selectedEmployee);

    const employeeShiftDetails = shiftData.filter(
      item => item.Shift_Cashier_ID === selectedEmployee,
    );
    setFilteredShiftData(employeeShiftDetails);
  };

  const clearFilterSelection = () => {
    setSelectedEmployee('');
    setFilteredShiftData(shiftData);
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    radioGroupContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: colors.surface,
      borderRadius: 12,
      paddingVertical: hp('1%'),
      paddingHorizontal: wp('1%'),
      marginVertical: hp('1.5%'),
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
    radioOption: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('0.8%'),
    },
    radioButtonOuter: {
      height: 18,
      width: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('1%'),
    },
    radioButtonInner: {
      height: 10,
      width: 10,
      borderRadius: 5,
      backgroundColor: colors.primary,
    },
    radioButtonLabel: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.text,
    },
    filterContainer: {
      marginBottom: hp('1.5%'),
    },
    listContainer: {
      flex: 1,
    },
    modalBackdrop: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContainer: {
      backgroundColor: colors.surface,
      width: wp('85%'),
      borderRadius: 16,
      overflow: 'hidden',
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.4 : 0.25,
      shadowRadius: 4,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalHeaderText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    closeButtonContainer: {
      padding: 8,
    },
    dateSelectionContainer: {
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
    },
    dateSelectionTitle: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      marginBottom: hp('1%'),
    },
    dateButton: {
      backgroundColor: colors.surface,
      padding: hp('1.5%'),
      borderRadius: 8,
      marginBottom: hp('1.5%'),
      borderWidth: 1,
      borderColor: colors.border,
    },
    dateButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateIconContainer: {
      marginRight: wp('2%'),
    },
    dateTextContainer: {
      flex: 1,
    },
    dateLabel: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    dateValue: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    dateValuePlaceholder: {
      color: colors.placeholder,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      borderTopWidth: 1,
      borderTopColor: colors.border,
      padding: hp('2%'),
    },
    clearButtonContainer: {
      flex: 1,
      backgroundColor: colors.disabled,
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('1.5%'),
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 1.5,
    },
    clearButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    applyButtonContainer: {
      flex: 1,
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: wp('1.5%'),
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 1.5,
    },
    applyButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    disabledButton: {
      backgroundColor: colors.disabled,
      opacity: 0.6,
    },
  });
  return (
    <View style={styles.container}>
      <Header
        NavName="Shift Reports"
        isCalendar={true}
        Options={() => setModalVisible(true)}
      />
      <View style={{backgroundColor: colors.background, flex: 1}}>
        <View style={styles.radioGroupContainer}>
          {['daily', 'weekly', 'monthly', 'yearly'].map(option => (
            <TouchableOpacity
              key={option}
              onPress={() => setSelectedFilter(option)}
              style={styles.radioOption}>
              <View style={styles.radioButtonOuter}>
                {selectedFilter === option && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
              <Text style={styles.radioButtonLabel}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.filterContainer}>
          <AppDropDown
            label="Filter By Employee"
            options={employeeOption}
            selectedValue={selectedEmployee}
            onSelect={value => filterByEmployee(value)}
            isNotClear={true}
            isShiftReport={true}
            onShiftAll={() => clearFilterSelection()}
          />
        </View>

        <View style={styles.listContainer}>
          <DataList
            data={filteredShiftData}
            renderItem={renderItem}
            loading={loading}
          />
        </View>
      </View>
    </View>
  );
};

export default LOT_ShiftReport;
