import {useCallback, useState, useEffect} from 'react';
import {Alert, Platform, Keyboard, TextInput} from 'react-native';
import {GetItemsParamsNoFilter} from '../utils/PublicHelper';
import {hasPermission} from '../utils/permissionHelper';
import {getInventoryPort} from '../server/InstanceTypes';
import {
  Inventory_Filter,
  InventoryVendor,
  AdditionalInfo,
} from '../server/types';

export interface UseLookupItemsReturn {
  // Pagination state
  page: number;
  setPage: (page: number) => void;
  displayData: Inventory_Filter[];
  setDisplayData: (data: Inventory_Filter[]) => void;
  loading: boolean;
  refreshing: boolean;

  // List methods
  applyPagination: (data: Inventory_Filter[], currentPage: number) => void;
  handleLoadMore: () => void;
  handleRefresh: () => Promise<void>;
  handleItemPress: (item: Inventory_Filter) => Promise<void>;

  // Render methods
  keyExtractor: (item: Inventory_Filter) => string;
  getItemLayout: (
    data: any,
    index: number,
  ) => {length: number; offset: number; index: number};

  // UI state methods
  handleOutsidePress: () => void;
  toggleLookup: (value: boolean) => void;

  // Constants
  ITEMS_PER_PAGE: number;
}

interface UseLookupItemsProps {
  filteredData: Inventory_Filter[];
  isMountedRef: React.MutableRefObject<boolean>;
  showLookup: boolean;
  setshowLookup: (value: boolean) => void;
  setSearchQuery: (query: string) => void;
  setInventoryVendor: (data: InventoryVendor[]) => void;
  setInventoryAdditional: (data: AdditionalInfo[]) => void;
  textInputRef: React.RefObject<TextInput>;
  navigation: any;
  route: any;
}

export const useLookupItems = ({
  filteredData,
  isMountedRef,
  showLookup,
  setshowLookup,
  setSearchQuery,
  setInventoryVendor,
  setInventoryAdditional,
  textInputRef,
  navigation,
  route,
}: UseLookupItemsProps): UseLookupItemsReturn => {
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);

  const ITEMS_PER_PAGE = 20;

  // Apply pagination to display data
  const applyPagination = useCallback(
    (data: Inventory_Filter[], currentPage: number) => {
      if (!isMountedRef.current) {
        return;
      }

      const endIndex = currentPage * ITEMS_PER_PAGE;
      const paginatedItems = data.slice(0, endIndex);
      setDisplayData(paginatedItems);
    },
    [ITEMS_PER_PAGE, isMountedRef],
  );

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (!isMountedRef.current) {
      return;
    }

    if (page * ITEMS_PER_PAGE < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, ITEMS_PER_PAGE, applyPagination, isMountedRef]);

  // Apply pagination when page changes
  useEffect(() => {
    if (!isMountedRef.current) {
      return;
    }

    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData, applyPagination, isMountedRef]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    if (!isMountedRef.current) {
      return;
    }

    setRefreshing(true);
    setPage(1);

    try {
      // Refresh logic would go here if needed
      // For now, just reset pagination
      applyPagination(filteredData, 1);
    } catch (error) {
      console.error('Error during refresh:', error);
    } finally {
      if (isMountedRef.current) {
        setRefreshing(false);
      }
    }
  }, [filteredData, applyPagination, isMountedRef]);

  // Handle item selection with better error handling
  const handleItemPress = useCallback(
    async (item: Inventory_Filter) => {
      if (!isMountedRef.current) {
        return;
      }

      try {
        const isAuthorized = await hasPermission('CFA_Inven_Edit');

        if (!isAuthorized) {
          Alert.alert('You do not have permission to edit inventory.');
          return;
        }

        const port = await getInventoryPort();

        const [vendorItemsResult, inventoryAdditionalResult] =
          await Promise.allSettled([
            GetItemsParamsNoFilter(
              port.toString(),
              '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
              setInventoryVendor,
              {
                Vendor_Number: item.Vendor_Number,
                ItemNum: item.ItemNum,
              },
              false,
            ),
            GetItemsParamsNoFilter(
              port.toString(),
              '/getInventoryAdditional/:ItemNum',
              setInventoryAdditional,
              {
                ItemNum: item.ItemNum,
              },
              false,
            ),
          ]);

        if (!isMountedRef.current) {
          return;
        }

        const vendorItems =
          vendorItemsResult.status === 'fulfilled'
            ? vendorItemsResult.value
            : [];

        const additionalData =
          inventoryAdditionalResult.status === 'fulfilled'
            ? inventoryAdditionalResult.value
            : [];

        setInventoryAdditional(additionalData || []);

        const lotteryDepartment = route.params?.LOTTERYDEPT;

        if (route.params?.isFromLottery) {
          if (lotteryDepartment) {
            if (lotteryDepartment === item.Dept_ID) {
              navigation.navigate('Barcode', {
                ItemData: [item],
                VENDORITEM: vendorItems,
                ADDITIONAL: additionalData,
                CANEDIT: false,
                isFromLottery: true,
                LOTTERYDEPT: lotteryDepartment,
              });
            } else {
              Alert.alert(
                'Wrong Department',
                'This item does not belong to the lottery department.',
              );
            }
          } else {
            navigation.navigate('Barcode', {
              ItemData: [item],
              VENDORITEM: vendorItems,
              ADDITIONAL: additionalData,
              CANEDIT: false,
              isFromLottery: true,
            });
          }
        } else {
          navigation.navigate('Barcode', {
            ItemData: [item],
            VENDORITEM: vendorItems,
            ADDITIONAL: additionalData,
            CANEDIT: true,
          });
        }
      } catch (error) {
        console.error('Error handling item press:', error);
        if (isMountedRef.current) {
          Alert.alert('Error', 'Failed to process item selection.');
        }
      }
    },
    [
      navigation,
      route.params,
      setInventoryVendor,
      setInventoryAdditional,
      isMountedRef,
    ],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => (item.ItemNum || '').toString(),
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: 88, // approximate height of item
      offset: 88 * index,
      index,
    }),
    [],
  );

  // Toggle keyboard/lookup with better state management
  const toggleLookup = useCallback(
    (value: boolean) => {
      applyPagination(filteredData, page);
      setshowLookup(value);
      setSearchQuery('');
      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          textInputRef.current?.blur();
        }, 50);
      }
    },
    [
      applyPagination,
      filteredData,
      page,
      setshowLookup,
      setSearchQuery,
      textInputRef,
    ],
  );

  const handleOutsidePress = useCallback(() => {
    if (!isMountedRef.current) {
      return;
    }

    if (showLookup) {
      // Only trigger when keyboard is ON
      setshowLookup(false);
      Keyboard.dismiss();
      toggleLookup(false); // Turn off keyboard state
    }
  }, [showLookup, setshowLookup, isMountedRef, toggleLookup]);

  return {
    page,
    setPage,
    displayData,
    setDisplayData,
    loading,
    refreshing,
    applyPagination,
    handleLoadMore,
    handleRefresh,
    handleItemPress,
    keyExtractor,
    getItemLayout,
    handleOutsidePress,
    toggleLookup,
    ITEMS_PER_PAGE,
  };
};
