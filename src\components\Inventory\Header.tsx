import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useCallback, useRef} from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useNavigation} from '@react-navigation/native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import {Primary} from '../../constants/Color';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

type NavProps = {
  NavName: string;
  Onpress?: () => void;
  isProvid?: boolean;
  isOption?: boolean;
  isCalendar?: boolean;
  Options?: () => void;
  isChoice?: boolean;
};

const Header: React.FC<NavProps> = ({
  NavName,
  Onpress,
  isProvid = false,
  isOption = false,
  isCalendar = false,
  Options,
  isChoice = false,
}) => {
  const nav = useNavigation();
  const colors = useThemeColors();
  const {isDark} = useTheme();
  const isNavigatingRef = useRef(false);

  // Debounced navigation to prevent multiple rapid calls
  const handleBackPress = useCallback(() => {
    if (isNavigatingRef.current) return;

    isNavigatingRef.current = true;

    try {
      if (isProvid && Onpress) {
        Onpress();
      } else {
        nav.goBack();
      }
    } catch (error) {
      console.error('Error during navigation:', error);
    }

    // Reset flag after a short delay
    setTimeout(() => {
      isNavigatingRef.current = false;
    }, 500);
  }, [isProvid, Onpress, nav]);

  const handleOptionsPress = useCallback(() => {
    try {
      if (Options) {
        Options();
      }
    } catch (error) {
      console.error('Error in options press:', error);
    }
  }, [Options]);

  return (
    <View style={[
      styles.container,
      {borderBottomColor: colors.border},
    ]}>
      <View style={styles.leftContainer}>
        <TouchableOpacity
          onPress={handleBackPress}
          activeOpacity={0.7}
          disabled={isNavigatingRef.current}>
          <View style={[styles.backButton, {backgroundColor: colors.primary}]}>
            <MaterialCommunityIcons
              name="arrow-left"
              color={'white'}
              size={hp('2.5%')}
            />
          </View>
        </TouchableOpacity>

        <Text
          style={[
            isOption ? styles.titleTextOption : styles.titleText,
            {color: colors.text},
          ]}
          numberOfLines={1}
          ellipsizeMode="tail">
          {NavName}
        </Text>
      </View>

      {isOption && (
        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: colors.surface}]}
          onPress={handleOptionsPress}
          activeOpacity={0.7}>
          {isChoice ? (
            <MaterialIcons
              name="add"
              color={colors.textSecondary}
              size={hp('3%')}
            />
          ) : (
            <MaterialCommunityIcons
              name="camera"
              color={colors.primary}
              size={hp('3%')}
            />
          )}
        </TouchableOpacity>
      )}

      {isCalendar && (
        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: colors.surface}]}
          onPress={handleOptionsPress}
          activeOpacity={0.7}>
          <MaterialIcons
            name="calendar-month"
            color={colors.textSecondary}
            size={hp('3%')}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: hp('2%'),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp('2.5%'),
    borderBottomWidth: 0.5,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    width: hp('5%'),
    height: hp('4%'),
    borderRadius: hp('3%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    fontSize: hp('2.4%'),
    marginLeft: wp('3%'),
    fontFamily: Fonts.OnestBold,
    letterSpacing: -0.3,
    lineHeight: hp('3.2%'),
    flex: 1,
  },
  titleTextOption: {
    fontSize: hp('2.4%'),
    marginLeft: wp('3%'),
    fontFamily: Fonts.OnestBold,
    letterSpacing: -0.3,
    lineHeight: hp('3.2%'),
    flex: 1,
    marginRight: wp('2%'),
  },
  actionButton: {
    paddingHorizontal: wp('2.5%'),
    paddingVertical: hp('1%'),
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: hp('5%'),
    minHeight: hp('4%'),
  },
});

export default Header;
