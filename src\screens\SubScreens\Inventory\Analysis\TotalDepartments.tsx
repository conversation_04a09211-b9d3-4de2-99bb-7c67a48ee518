import {View, Text, Alert, StyleSheet} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Department} from '../../../../server/types';
import Header from '../../../../components/Inventory/Header';
import DataList from '../../../../components/Inventory/AppList';
import {GetAllItems, handleSearch} from '../../../../utils/PublicHelper';
import {getInventoryPort} from '../../../../server/InstanceTypes';
import {Fonts} from '../../../../styles/fonts';
import AppButton from '../../../../components/Inventory/AppButton';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import Search from '../../../../components/Inventory/Search';
import {TextInput} from 'react-native-gesture-handler';
import {hasPermission} from '../../../../utils/permissionHelper';
import {MaterialColors} from '../../../../constants/MaterialColors';
import FAB from '../../../../components/common/FAB';
import {useThemeColors} from '../../../../Theme/useThemeColors';
import {useTheme} from '../../../../Theme/ThemeContext';

const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

type BarcodeScreenRouteProp = RouteProp<any, 'TotalDeparments'>;
const TotalDepartments: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentsFilter, setDepartmentsFilter] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      getInitDept();
    }, []),
  );
  const getInitDept = async () => {
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
      false,
    );

    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartmentsFilter,
      setLoading,
      false,
    );
  };

  const renderItem = ({item}: {item: Department}) => (
    <View style={styles.departmentCard}>
      <View style={styles.departmentHeader}>
        <Text style={styles.departmentName}>{item.Description}</Text>
      </View>
    </View>
  );

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      departments,
      ['Dept_ID', 'Description'],
      setDepartmentsFilter,
      setLoading,
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: 12,
    },
    departmentCard: {
      backgroundColor: colors.card,
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1.8%'),
      marginBottom: hp('1.2%'),
      borderRadius: 12,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.12,
      shadowRadius: 3.5,
      borderLeftColor: colors.primary,
    },
    departmentHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    deptIdChip: {
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('0.6%'),
      borderRadius: 16,
    },
    deptIdText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.primary,
    },
    departmentName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      flex: 1,
      marginLeft: wp('3%'),
    },
  });
  return (
    <View style={styles.container}>
      <Header NavName="Total Departments" />
      <Search
        textInputRef={textInputRef}
        PlaceHolder="Search..."
        Value={searchQuery}
        onChange={onSearchChange}
        keyboardON={true}
      />
      <Text
        style={{
          marginVertical: 8,
          fontFamily: Fonts.OnestBold,
          color: colors.text,
          fontSize: FontSizes.large,
          paddingVertical: hp('1%'),
        }}>
        Total Departments: ({departmentsFilter.length})
      </Text>
      <View style={{height: hp('73%')}}>
        <DataList
          data={departmentsFilter}
          renderItem={renderItem}
          loading={loading}
        />
      </View>
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          left: 0,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
          backgroundColor: colors.surface,
        }}>
        <FAB
          label="Add New Department"
          position="bottomRight"
          onPress={async () => {
            const isAuthorized = await hasPermission('CFA_Depts_Add');

            if (!isAuthorized) {
              Alert.alert('You do not have permission to add departments.');
              return;
            }
            navigation.navigate('NewDepartment');
          }}
        />
      </View>
    </View>
  );
};

export default TotalDepartments;
