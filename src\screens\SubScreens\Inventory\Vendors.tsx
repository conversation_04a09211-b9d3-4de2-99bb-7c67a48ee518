import {<PERSON>, <PERSON>rollView, Alert, StyleSheet} from 'react-native';
import React, {useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Vendor} from '../../../server/types';
import {applyDefaultsVendor} from '../../../Validator/Inventory/Barcode';
import {
  createData,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {useThemeColors} from '../../../Theme/useThemeColors';
import FAB from '../../../components/common/FAB';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const Vendors: React.FC<NavProps> = ({navigation: _navigation}) => {
  const [vendorNumber, setVendorNumber] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [company, setCompany] = useState<string>('');
  const [address1, setAddress1] = useState<string>('');
  const [address2, setAddress2] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [state, setState] = useState<string>('');
  const [zipCode, setZipCode] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [vendorTerms, setVendorTerms] = useState<string>('');

  const AddVendor = async () => {
    if (vendorNumber === '' || company === '' || firstName === '') {
      Alert.alert('Please Enter Required Field');
      return;
    }

    const getVendor = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/getVendorDetails/:Vendor_Number',
      {Vendor_Number: vendorNumber},
    );

    if (Array.isArray(getVendor) && getVendor.length === 0) {
      const inventoryAdjustData: Partial<Vendor> = {
        Vendor_Number: vendorNumber,
        First_Name: firstName,
        Last_Name: lastName,
        Company: company,
        Address_1: address1,
        Address_2: address2,
        City: city,
        State: state,
        Zip_Code: zipCode,
        Phone: phone,
        Vendor_Terms: vendorTerms,
      };
      const applyDefault = applyDefaultsVendor(inventoryAdjustData);
      const result = await createData<Vendor>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createvendor',
      });

      if (result) {
        Alert.alert('Vendor Added Success!');
      }
    } else {
      Alert.alert('Vendor Already Exists');
    }
  };

  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      width: '100%',
      height: '100%',
      paddingHorizontal: wp('2.5%'),
    },
    contentContainer: {
      paddingHorizontal: 10,
      flex: 1,
    },
    scrollContainer: {
      height: hp('85%'),
    },
  });

  return (
    <View style={styles.container}>
      <Header NavName="Add Vendor" />

      <View style={styles.contentContainer}>
        <View style={styles.scrollContainer}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <AppTextInput
              PlaceHolder="Enter Vendor Number"
              Title="Vendor Number"
              Value={vendorNumber}
              onChangeText={text => setVendorNumber(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter First Name"
              Title="First Name"
              Value={firstName}
              onChangeText={text => setFirstName(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter Last Name"
              Title="Last Name"
              Value={lastName}
              onChangeText={text => setLastName(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Company"
              Title="Company"
              Value={company}
              onChangeText={text => setCompany(text)}
              isRequired={true}
            />

            <AppTextInput
              PlaceHolder="Enter Address 1"
              Title="Address 1"
              Value={address1}
              onChangeText={text => setAddress1(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Address 2"
              Title="Address 2"
              Value={address2}
              onChangeText={text => setAddress2(text)}
            />
            <AppTextInput
              PlaceHolder="Enter City"
              Title="City"
              Value={city}
              onChangeText={text => setCity(text)}
            />

            <AppTextInput
              PlaceHolder="Enter State"
              Title="State"
              Value={state}
              onChangeText={text => setState(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Zip Code"
              Title="Zip Code"
              Value={zipCode}
              onChangeText={text => setZipCode(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Phone"
              Title="Phone"
              Value={phone}
              onChangeText={text => setPhone(text)}
            />

            <AppTextInput
              PlaceHolder="Enter Vendor Terms"
              Title="Vendor Terms"
              Value={vendorTerms}
              onChangeText={text => setVendorTerms(text)}
            />
          </ScrollView>
        </View>
      </View>

      <FAB
        label="Add Vendor"
        position="bottomRight"
        onPress={() => AddVendor()}
      />
    </View>
  );
};

export default Vendors;
