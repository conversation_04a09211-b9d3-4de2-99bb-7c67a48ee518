import AsyncStorage from '@react-native-async-storage/async-storage';
import { LabelMargins, DEFAULT_MARGINS } from '../Types/PrinterTypes';

const MARGIN_STORAGE_KEY = 'label_margins';

/**
 * Save label margins to AsyncStorage
 */
export const saveMargins = async (margins: LabelMargins): Promise<void> => {
  try {
    await AsyncStorage.setItem(MARGIN_STORAGE_KEY, JSON.stringify(margins));
  } catch (error) {
    console.error('Error saving margins:', error);
    throw new Error('Failed to save margin settings');
  }
};

/**
 * Get label margins from AsyncStorage
 */
export const getMargins = async (): Promise<LabelMargins> => {
  try {
    const savedMargins = await AsyncStorage.getItem(MARGIN_STORAGE_KEY);

    if (savedMargins) {
      const parsedMargins = JSON.parse(savedMargins);
      return parsedMargins;
    }

    return DEFAULT_MARGINS;
  } catch (error) {
    console.error('Error loading margins:', error);
    return DEFAULT_MARGINS;
  }
};

/**
 * Reset margins to default values
 */
export const resetMargins = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(MARGIN_STORAGE_KEY, JSON.stringify(DEFAULT_MARGINS));
  } catch (error) {
    console.error('Error resetting margins:', error);
    throw new Error('Failed to reset margin settings');
  }
};

/**
 * Apply margins to EPL coordinates
 * @param x Original X coordinate
 * @param y Original Y coordinate
 * @param margins Margin configuration
 * @returns Adjusted coordinates
 */
export const applyMargins = (x: number, y: number, margins: LabelMargins): { x: number; y: number } => {
  const adjustedX = x + margins.left;
  const adjustedY = y + margins.top;

  return {
    x: adjustedX,
    y: adjustedY,
  };
};

/**
 * Validate margin values
 */
export const validateMargins = (margins: Partial<LabelMargins>): string[] => {
  const errors: string[] = [];
  
  if (margins.top !== undefined && (margins.top < -50 || margins.top > 100)) {
    errors.push('Top margin must be between -50 and 100');
  }
  
  if (margins.right !== undefined && (margins.right < -50 || margins.right > 100)) {
    errors.push('Right margin must be between -50 and 100');
  }
  
  if (margins.bottom !== undefined && (margins.bottom < -50 || margins.bottom > 100)) {
    errors.push('Bottom margin must be between -50 and 100');
  }
  
  if (margins.left !== undefined && (margins.left < -50 || margins.left > 100)) {
    errors.push('Left margin must be between -50 and 100');
  }
  
  return errors;
};
