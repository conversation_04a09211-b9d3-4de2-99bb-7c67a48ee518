import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Keyboard,
  Alert,
  TouchableWithoutFeedback,
  Platform,
  Modal,
} from 'react-native';
import React, {useCallback, useRef, useState} from 'react';

import {Inventory_Filter, TagAlong} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  GetItemsParamsNoFilterNoReturn,
  showAlert,
  showAlertOK,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {Fonts} from '../../../styles/fonts';
import Header from '../../../components/Inventory/Header';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {createItem} from '../../../server/service';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useCodeScanner} from 'react-native-vision-camera';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useInventoryList} from '../../../hooks/useInventoryList';
import InventoryList from '../../../components/common/InventoryList';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

// Memoized Item component for optimal rendering performance

type BarcodeScreenRouteProp = RouteProp<any, 'AddTagAlong'>;
const TagAlongAdd: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  // Initialize inventory list hook
  const inventoryList = useInventoryList({
    itemsPerPage: 20,
    debounceDelay: 300,
  });

  const [filter, setFilter] = useState<boolean>(false);
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [isCreate] = useState<boolean>(route.params?.IsCreate || false);

  const [itemNumber] = useState<string>(route.params?.ItemData || '');

  const textInputRef = useRef<TextInput>(null);

  const colors = useThemeColors();

  // Initialize data on screen focus
  useFocusEffect(
    useCallback(() => {
      inventoryList.getInventoryData();
      setFilter(false);
      inventoryList.setSearchQuery('');
      setshowLookup(false);
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.blur();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, [inventoryList]),
  );

  // Add item to tag along functionality - fix type to accept Inventory_Filter
  const addItemsToTagAlong = async (tagAlongItem: Inventory_Filter) => {
    if (isCreate) {
      if (route.params?.ItemData === tagAlongItem.ItemNum) {
        showAlertOK(
          'Item Not Longer Available to Add!',
          'Not A Valid Item',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            inventoryList.setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }
      try {
        const existingData = await AsyncStorage.getItem('SetTagAlongs');
        const existingArray = existingData ? JSON.parse(existingData) : [];

        const indexToUpdate = existingArray.some(
          (item: any) => item.TagAlong_ItemNum === tagAlongItem?.ItemNum,
        );
        if (indexToUpdate) {
          showAlertOK(
            'This Item Already Added to Tag Along!',
            'Item Already Added',
            'OK',
            () => {
              if (textInputRef.current) {
                textInputRef.current.clear();
                textInputRef.current.blur();
              }

              inventoryList.setSearchQuery('');
              Keyboard.dismiss();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 200);
            },
          );
        } else {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const createTag: TagAlong = {
            ItemNum: route.params?.ItemData,
            Quantity: 1,
            Store_ID: ValidStore,
            TagAlong_ItemNum: tagAlongItem?.ItemNum || '',
          };

          existingArray.push(createTag);
          await AsyncStorage.setItem(
            'SetTagAlongs',
            JSON.stringify(existingArray),
          );
          Alert.alert('Tag Along Added');
          navigation.goBack();
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      if (route.params?.ItemData === tagAlongItem.ItemNum) {
        showAlertOK(
          'Item Not Longer Available to Add!',
          'Not A Valid Item',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            inventoryList.setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }

      const getExist = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/checkExistsTagAlong/:ItemNum/:TagAlong_ItemNum',
        {ItemNum: itemNumber, TagAlong_ItemNum: tagAlongItem.ItemNum},
      );
      if (Array.isArray(getExist) && getExist.length === 0) {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;
        const createTag: TagAlong = {
          ItemNum: itemNumber,
          Quantity: 1,
          Store_ID: ValidStore,
          TagAlong_ItemNum: tagAlongItem.ItemNum || '',
        };

        const result = await createItem(
          (await getInventoryPort()).toString(),
          '/createtagalongs',
          createTag,
        );
        if (result) {
          Alert.alert('Tag Along Added');
          navigation.goBack();
        }
      } else {
        showAlertOK(
          'This Item Already Added to Tag Along!',
          'Item Already Added',
          'OK',
          () => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            inventoryList.setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          },
        );
      }
    }
  };

  // Toggle keyboard/lookup
  const toggleLookup = useCallback(
    (value: boolean) => {
      setshowLookup(value);
      inventoryList.setSearchQuery('');

      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        inventoryList.setSearchQuery('');
        Keyboard.dismiss();
      }
    },
    [inventoryList],
  );
  // Handle search
  const handleSearchChange = async (text: string) => {
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter[]>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );

    const searchValue = getBarcode?.[0]?.ItemNum || text;
    inventoryList.setSearchQuery(searchValue);
    setCamera(false);

    if (text.trim() === '') {
      inventoryList.setSearchQuery('');
      return;
    } else {
      if (showLookup) {
        inventoryList.performSearch(text, inventoryList.filteredData);
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: searchValue,
            });
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            inventoryList.setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          inventoryList.performSearch(searchValue, inventoryList.filteredData);
        }
      }
    }
  };

  // Optimized rendering components - now removed as we use InventoryList component

  // Optimized key extractor - now removed as we use InventoryList component

  // GetItemLayout for optimized rendering - now removed as we use InventoryList component

  const handleOutsidePress = () => {
    if (showLookup) {
      setshowLookup(false);
      Keyboard.dismiss();
    }
  };

  const handleDoneClick = () => {
    setshowLookup(false);
    if (textInputRef.current) {
      textInputRef.current.clear();
      textInputRef.current.blur();
    }

    inventoryList.setSearchQuery('');
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 200);
  };

  const [camera, setCamera] = useState(false);
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0 && codes[0].value) {
        handleSearchChange(codes[0].value);
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginBottom: 5,
    },
    itemCountContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: hp('1%'),
      paddingHorizontal: wp('2%'),
    },
    itemCountText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    listWrapper: {
      height: '84%',
    },
  });

  return (
    <TouchableWithoutFeedback onPress={handleOutsidePress}>
      <View style={styles.container}>
        <Header
          NavName="Link Items"
          isOption={true}
          Options={() => {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            inventoryList.setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);

            setCamera(!camera);
          }}
        />

        <View style={styles.searchContainer}>
          <AppSearchWIthFilter
            OnSearch={handleSearchChange}
            SearchValue={inventoryList.searchQuery}
            OnSearchSet={() => setFilter(true)}
            isEnableFilter={inventoryList.isEnableFilter}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={toggleLookup}
            OnSubmitEditing={handleDoneClick}
          />

          <View style={styles.itemCountContainer}>
            <Text style={styles.itemCountText}>
              Total Items: ({inventoryList.filteredData.length || 0})
            </Text>
          </View>
        </View>

        {/* List Container */}
        <View style={styles.listContainer}>
          <View style={styles.listWrapper}>
            <InventoryList
              listData={inventoryList}
              onItemPress={addItemsToTagAlong}
              itemHeight={88}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>

        {/* Filter Modal */}
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => inventoryList.setSelectedDepartment(text)}
          Vedor={text => inventoryList.setSelectedVendor(text)}
          Brand={text => inventoryList.setSelectedBrand(text)}
          Category={text => inventoryList.setSelectedSubCategory(text)}
          EnableFilter={() => {}} // This is handled by the hook internally
          selectedDepartment={inventoryList.selectedDepartment}
          selectedVendor={inventoryList.selectedVendor}
          selectedBrand={inventoryList.selectedBrand}
          selectedSubCategory={inventoryList.selectedSubCategory}
          isEnableFilter={inventoryList.isEnableFilter}
        />

        <Modal
          animationType="fade"
          transparent={false}
          visible={camera}
          onRequestClose={() => setCamera(false)}>
          <AppScanner
            codeScanner={codeScanner}
            onClose={() => {
              setCamera(false);
              textInputRef?.current?.focus();
            }}
          />
        </Modal>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default TagAlongAdd;
