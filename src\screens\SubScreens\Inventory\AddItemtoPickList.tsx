import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  Keyboard,
  Platform,
} from 'react-native';
import React, {useCallback, useRef, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {Invoice_Itemized, Invoice_Totals} from '../../../server/types';
import {
  calculatePriceWithVAT1,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
  updateData,
} from '../../../utils/PublicHelper';
import DataList from '../../../components/Inventory/AppList';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {deleteItem} from '../../../server/service';
import {applyDefaultsInvoiceTotals} from '../../../Validator/Inventory/Barcode';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'AddItemPickList'>;
const AddItemtoPickList: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [pickListItem, setPickListItem] = useState<Invoice_Totals>(
    route.params?.ItemData,
  );
  const [validRef] = useState<string>(route.params?.VALIDREF);
  const [invoiceItem, setInvoiceItem] = useState<Invoice_Itemized[]>([]);
  const [initial, setInitial] = useState<Invoice_Itemized[]>([]);
  const [loading] = useState<boolean>(false);
  const textInputRef2 = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [totalCost, setTotalCost] = useState<number>(
    0 || route.params?.ItemData?.Total_Cost,
  );
  const [totalPrice, setTotalPrice] = useState<number>(
    0 || route.params?.ItemData?.Total_Price,
  );
  const [totalPriceVat, setTotalPriceVat] = useState<number>(
    0 || route.params?.ItemData?.Total_Tax1,
  );
  const [grandTotal, setGrandTotal] = useState<number>(
    0 || route.params?.ItemData?.Grand_Total,
  );
  const [camera, setCamera] = useState<boolean>(false);
  const [cameraModal, setCameraModal] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookupMain, setShowLookupMain] = useState<boolean>(false);
  const [isBarcodeScanned, setIsBarcodeScanned] = useState(true);
  const [filter, setFilter] = useState<boolean>(false);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      setIsBarcodeScanned(false);
      setSearchQuery('');
      setShowLookupMain(false);
      setTimeout(() => {
        if (textInputRef2.current) {
          textInputRef2.current.blur();
          setTimeout(() => {
            if (textInputRef2.current) {
              textInputRef2.current.focus();
            }
          }, 50);
        }
      }, 50);
    }, []),
  );

  // const toggleLookup = (checked: boolean) => {
  //   if (checked) {
  //     setshowLookup(checked);
  //     textInputRef?.current?.blur();
  //     setTimeout(() => {
  //       textInputRef?.current?.focus();
  //     }, 1200);
  //   } else {
  //     Keyboard.dismiss();
  //     setTimeout(() => {
  //       textInputRef?.current?.focus();
  //     }, 1200);
  //     setshowLookup(checked);
  //   }
  // };

  // const toggleLookupMain = (checked: boolean) => {
  //   if (checked) {
  //     setShowLookupMain(checked);
  //     textInputRef2?.current?.blur();
  //     setTimeout(() => {
  //       textInputRef2?.current?.focus();
  //     }, 1200);
  //   } else {
  //     Keyboard.dismiss();
  //     setShowLookupMain(checked);
  //   }
  // };
  const toggleLookupMain = useCallback((value: boolean) => {
    setShowLookupMain(value);
    setSearchQuery('');
    setInitial(invoiceItem);

    if (Platform.OS === 'android') {
      if (value) {
        if (textInputRef2.current) {
          textInputRef2.current.clear();
          textInputRef2.current.blur();
        }
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
          }
        }, 200);
        // setInitial(invoiceItem);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef2.current) {
            textInputRef2.current.blur();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
          }
        }, 200);
        //setInitial(invoiceItem);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef2.current?.focus();
      }, 100);
      setInitial(invoiceItem);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
      setInitial(invoiceItem);
    }
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await init();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
    setRefreshing(false);
  };

  useFocusEffect(
    useCallback(() => {
      init();
    }, []),
  );
  const init = async () => {
    GetItemsParamsNoFilter<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getinvoiceholditems/:Invoice_Number',
      setInvoiceItem,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      false,
    );
    GetItemsParamsNoFilter<Invoice_Itemized[]>(
      (await getInventoryPort()).toString(),
      '/getinvoiceholditems/:Invoice_Number',
      setInitial,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      false,
    );
    GetItemsParamsNoFilter<Invoice_Totals>(
      (await getInventoryPort()).toString(),
      '/GetInvoiceTotal/:Invoice_Number',
      setPickListItem,
      {Invoice_Number: route.params?.ItemData?.Invoice_Number},
      true,
    );
  };

  const removeHoldItems = (ItemMized: Invoice_Itemized) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const qtyCost =
            Number(ItemMized.Quantity) > 1
              ? Number(ItemMized.CostPer) * Number(ItemMized.Quantity)
              : ItemMized.CostPer;
          const qtyPrice =
            Number(ItemMized.Quantity) > 1
              ? Number(ItemMized.PricePer) * Number(ItemMized.Quantity)
              : ItemMized.PricePer;
          const VatPrice =
            Number(calculatePriceWithVAT1(Number(qtyPrice))) - Number(qtyPrice);
          const GrandTotal = Number(VatPrice) + Number(qtyPrice);

          const result = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeleteOnHoldItems/:Invoice_Number/:ItemNum',
            {
              Invoice_Number: pickListItem?.Invoice_Number,
              ItemNum: ItemMized?.ItemNum,
            },
          );

          if ((result as any)?.success) {
            if (invoiceItem.length < 2) {
              updateInvoiceTotals(true, 0, 0, 0, 0, 0, 0);
            } else {
              updateInvoiceTotals(
                true,
                Number(pickListItem.Total_Price) - qtyPrice,
                Number(pickListItem.Total_Cost) - qtyCost,
                Number(pickListItem.Total_Tax1) - VatPrice,
                Number(pickListItem.Grand_Total) - GrandTotal,
                Number(pickListItem.Total_Price) - qtyPrice,
                Number(pickListItem.Total_Price) - qtyPrice,
              );
            }
            GetItemsParamsNoFilter<Invoice_Itemized[]>(
              (await getInventoryPort()).toString(),
              '/getinvoiceholditems/:Invoice_Number',
              setInvoiceItem,
              {Invoice_Number: route.params?.ItemData?.Invoice_Number},
              false,
            );
            GetItemsParamsNoFilter<Invoice_Itemized[]>(
              (await getInventoryPort()).toString(),
              '/getinvoiceholditems/:Invoice_Number',
              setInitial,
              {Invoice_Number: route.params?.ItemData?.Invoice_Number},
              false,
            );
          }
        } else {
          console.log('Item will not be deleted');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const updateInvoiceTotals = async (
    isDelete?: boolean,
    Total_Price?: number,
    Total_Cost?: number,
    Total_Tax1?: number,
    Grand_Total?: number,
    Taxed_Sales?: number,
    Total_UndiscountedSale?: number,
  ) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: route.params?.ItemData?.Invoice_Number,
        ReferenceInvoiceNumber:
          route.params?.ItemData?.Invoice_Number.toString(),
        Orig_OnHoldID: route.params?.ItemData?.Orig_OnHoldID,
        Store_ID: route.params?.ItemData?.Store_ID,
        Cashier_ID: route.params?.ItemData?.Cashier_ID,
        CustNum: route.params?.ItemData?.CustNum,
        DateTime: route.params?.ItemData?.DateTime,
        Total_Price: !isDelete ? totalPrice : Total_Price,
        Total_Cost: !isDelete ? totalCost : Total_Cost,
        Total_Tax1: !isDelete ? totalPriceVat : Total_Tax1,
        Grand_Total: !isDelete ? grandTotal : Grand_Total,
        Station_ID: route.params?.ItemData?.Station_ID,
        Payment_Method: route.params?.ItemData?.Payment_Method,
        Status: route.params?.ItemData?.Status,
        Taxed_1: route.params?.ItemData?.Taxed_1,
        Taxed_Sales: !isDelete ? totalPrice : Taxed_Sales,
        Dirty: route.params?.ItemData?.Dirty,
        CourseOrderingProgress: route.params?.ItemData?.CourseOrderingProgress,
        Total_UndiscountedSale: !isDelete ? totalPrice : Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);
      setPickListItem(applyDefult);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        setPickListItem(applyDefult);
        // setSuccess(false)
      }
    } catch (error) {}
  };

  const onSearchInvoiceItems = async (text: string) => {
    setCamera(false);
    setSearchQuery(text);

    if (text) {
      if (showLookupMain) {
        // Simple text search in invoice items
        const filtered = invoiceItem.filter(
          item =>
            item.DiffItemName?.toLowerCase().includes(text.toLowerCase()) ||
            item.ItemNum?.toLowerCase().includes(text.toLowerCase()),
        );
        setInitial(filtered);
      } else {
        // Navigate to add item screen
        const getBarcode = await GetItemsParamsNoFilterNoReturn(
          (await getInventoryPort()).toString(),
          '/inventory/:ItemNum',
          {ItemNum: text},
        );
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          showAlert('Item not found. Do you want to create a new item?').then(
            async result => {
              if (result) {
                navigation.navigate('ItemType', {ItemData: text});
              } else {
                if (textInputRef2.current) {
                  textInputRef2.current.clear();
                  textInputRef2.current.blur();
                }

                setSearchQuery('');
                setShowLookupMain(false);
                Keyboard.dismiss();
                setTimeout(() => {
                  if (textInputRef2.current) {
                    textInputRef2.current.focus();
                  }
                }, 200);
              }
            },
          );
        } else {
          // First check for exact matches
          const exactMatch = invoiceItem.find(
            item =>
              item.DiffItemName?.toLowerCase() === text.toLowerCase() ||
              item.ItemNum?.toLowerCase() === text.toLowerCase(),
          );

          if (exactMatch) {
            // If exact match found, show only that item
            setInitial([exactMatch]);
          } else {
            // No exact match found, this means it's a new item that should be added
            // Navigate to add new item screen
            navigation.navigate('AddPickListEachItems', {
              ItemData:
                getBarcode && Array.isArray(getBarcode) && getBarcode.length > 0
                  ? getBarcode[0]
                  : null,
              PickItem: pickListItem,
            });
          }
        }
      }
    } else {
      setInitial(invoiceItem);
    }
  };

  const renderItem = ({item}: {item: Invoice_Itemized}) => {
    return (
      <TouchableOpacity>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: colors.card, // was MaterialColors.surface
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('1%'),
            borderRadius: 15,
            borderBottomWidth: 1,
            borderBottomColor: colors.border, // was MaterialColors.grey[200]
            marginVertical: hp('0.5%'),
          }}>
          <View style={{gap: 2, flex: 1, marginRight: wp('2%')}}>
            <Text
              style={{
                fontSize: FontSizes.small,
                fontFamily: Fonts.OnestBold,
                color: colors.text, // Add this
              }}>
              {item.DiffItemName}
            </Text>
            <Text
              style={{
                fontSize: FontSizes.small,
                color: colors.textSecondary, // was '#A1A1A1'
                fontFamily: Fonts.OnestMedium,
              }}>
              Qty: {item.Quantity}
            </Text>

            <Text
              style={{
                fontSize: FontSizes.large,
                color: MaterialColors.primary.main,
                fontFamily: Fonts.OnestBold,
              }}>
              ${item.PricePer.toFixed(2)}
            </Text>
          </View>

          <TouchableOpacity
            onPress={() => removeHoldItems(item)}
            style={{
              padding: wp('2%'),
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <MaterialCommunityIcons
              name="delete-outline"
              color={MaterialColors.error.main}
              size={28}
            />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const codeScannerMain = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchInvoiceItems(codes[0].value || '');
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const codeScannerModal = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchInvoiceItems(codes[0].value || '');
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const NavigationHandler = () => {
    if (invoiceItem.length <= 0) {
      showAlert(
        `You haven't added any items yet. Do you want to continue with the Pick & Hold process?`,
      )
        .then(async result => {
          if (result) {
          } else {
            updateInvoiceTotals_Valid(pickListItem);
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      navigation.navigate('ItemPickList');
    }
  };

  const updateInvoiceTotals_Valid = async (pickListItem: Invoice_Totals) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: pickListItem.Invoice_Number,
        ReferenceInvoiceNumber: pickListItem.Invoice_Number.toString(),
        Orig_OnHoldID: pickListItem.Orig_OnHoldID,
        Store_ID: pickListItem.Store_ID,
        Cashier_ID: pickListItem.Cashier_ID,
        CustNum: pickListItem.CustNum,
        DateTime: pickListItem.DateTime,
        Total_Price: pickListItem.Total_Price,
        Total_Cost: pickListItem.Total_Cost,
        Total_Tax1: pickListItem.Total_Tax1,
        Grand_Total: pickListItem.Grand_Total,
        Station_ID: pickListItem.Station_ID,
        Payment_Method: pickListItem.Payment_Method,
        Status: 'C',
        Taxed_1: pickListItem.Taxed_1,
        Taxed_Sales: pickListItem.Taxed_Sales,
        Dirty: pickListItem.Dirty,
        CourseOrderingProgress: pickListItem.CourseOrderingProgress,
        Total_UndiscountedSale: pickListItem.Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        await deleteItem(
          (await getInventoryPort()).toString(),
          '/DeleteOnHold/:Invoice_Number',
          {Invoice_Number: pickListItem?.Invoice_Number},
        );

        navigation.navigate('ItemPickList');
      }
    } catch (error) {}
  };

  const handleDoneClick = () => {
    if (textInputRef2.current) {
      textInputRef2.current.clear();
      textInputRef2.current.blur();
    }

    setSearchQuery('');
    setShowLookupMain(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef2.current) {
        textInputRef2.current.focus();
      }
    }, 200);
    setInitial(invoiceItem);
  };

  return (
    <View
      style={{
        backgroundColor: colors.background, // was Backround
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header
          NavName="Add Item to PickList"
          isProvid={true}
          Onpress={() => NavigationHandler()}
          isOption={true}
          Options={() => {
            if (textInputRef2.current) {
              textInputRef2.current.clear();
              textInputRef2.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef2.current) {
                textInputRef2.current.focus();
              }
            }, 200);
            setInitial(invoiceItem);
            setCamera(!camera);
          }}
        />

        {/* <Header NavName="Add Item to PickList" /> */}

        <View
          style={{
            backgroundColor: colors.card, // was MaterialColors.background
            paddingHorizontal: wp('2.5%'),
            paddingVertical: hp('2%'),
            gap: 5,
            borderRadius: 15,
          }}>
          <Text
            style={{
              fontSize: FontSizes.medium,
              fontFamily: Fonts.OnestBold,
              color: colors.text, // Add this
            }}>
            Refrence Number: {validRef}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestMedium,
                color: MaterialColors.text.secondary,
              }}>
              {
                new Date(route.params?.ItemData?.DateTime)
                  .toISOString()
                  .split('T')[0]
              }
            </Text>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestMedium,
                color: MaterialColors.text.secondary,
              }}>
              Total Price:
              {pickListItem.Total_Price
                ? `$ ${pickListItem.Total_Price.toFixed(2)}`
                : 'N/A'}
            </Text>
          </View>
        </View>

        <View style={{paddingVertical: hp('2%')}}>
          {/* <Search
            textInputRef={textInputRef2}
            PlaceHolder="Search..."
            Value={searchQuery}
            onChange={onSearchInvoiceItems}
            keyboardON={showLookupMain}
          /> */}
          <AppSearchWIthFilter
            OnSearch={onSearchInvoiceItems}
            SearchValue={searchQuery}
            Keyboardon={showLookupMain}
            textInputRef={textInputRef2}
            onToggleLookup={toggleLookupMain}
            IsFilter={false}
            OnSubmitEditing={() => handleDoneClick()}
          />
        </View>

        <View style={{}}>
          <View>
            <Text
              style={{
                fontSize: FontSizes.medium,
                fontFamily: Fonts.OnestBold,
                color: colors.text, // was MaterialColors.text.primary
                paddingVertical: hp('1%'),
              }}>
              {`Items Added: (${initial.length || 0})`}
            </Text>
          </View>
        </View>

        <DataList
          data={initial}
          loading={loading}
          renderItem={renderItem}
          Hight="58%"
          refreshing={refreshing}
          onRefresh={onRefresh}
        />
      </View>

      <View
        style={{
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: 0,
          backgroundColor: colors.surface, // was MaterialColors.surface
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
        }}>
        <FAB
          label={'Add Items To List'}
          position="bottomRight"
          onPress={() =>
            navigation.navigate('AddItemsToListScreen', {
              pickListItem: pickListItem,
              invoiceItem: invoiceItem,
            })
          }
        />
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={camera}
        onRequestClose={() => setCamera(!camera)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerMain}
            onClose={() => {
              setCamera(false);
              textInputRef2?.current?.focus();
            }}
          />
        </View>
      </Modal>

      <Modal
        animationType="slide"
        transparent={true}
        visible={cameraModal}
        onRequestClose={() => setCameraModal(!cameraModal)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner
            codeScanner={codeScannerModal}
            onClose={() => {
              setCameraModal(false);
              textInputRef2?.current?.focus();
            }}
          />
        </View>
      </Modal>

      <View style={{flex: 1}}>
        <AppFilter
          isVisible={filter}
          setIsVisble={setFilter}
          Department={text => setSelectedDepartment(text)}
          Vedor={text => setSelectedVendor(text)}
          Brand={text => setSelectedBrand(text)}
          Category={text => setSelectedSubCategory(text)}
          EnableFilter={setIsEnableFilter}
          selectedDepartment={selectedDepartment}
          selectedVendor={selectedVendor}
          selectedBrand={selectedBrand}
          selectedSubCategory={selectedSubCategory}
          isEnableFilter={isEnableFilter}
        />
      </View>
    </View>
  );
};

export default AddItemtoPickList;
