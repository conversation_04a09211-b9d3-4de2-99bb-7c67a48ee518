import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Header from '../../../../components/Inventory/Header';
import DataList from '../../../../components/Inventory/AppList';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../../utils/PublicHelper';
import {Game_Details} from '../../../../Types/Lottery/Lottery_Types';
import {
  getInventoryPort,
  getLotteryPort,
} from '../../../../server/InstanceTypes';
import {Fonts} from '../../../../styles/fonts';
import DateTimePicker from '@react-native-community/datetimepicker';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AppLoader from '../../../../components/Inventory/AppLoader';
import AppFocus from '../../../../components/Inventory/AppFocus';
import AppButton from '../../../../components/Inventory/AppButton';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AppTextInput from '../../../../components/Inventory/AppTextInput';
import {applyDefaultsGameDetails} from '../../../../Validator/Lottery/Lottery_Validator';
import {MaterialColors} from '../../../../constants/MaterialColors';
import {useThemeColors} from '../../../../Theme/useThemeColors';
import {useTheme} from '../../../../Theme/ThemeContext';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

const MissingTickets = () => {
  const [lotterGame, setLotterGame] = useState<Game_Details[]>([]);
  const [lotterGameFil, setLotterGameFil] = useState<Game_Details[]>([]);
  const [filteredLotterGame, setFilteredLotterGame] = useState<Game_Details[]>(
    [],
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [resetStock, setResetStock] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState<string>('daily');
  const [modalVisible, setModalVisible] = useState(false);
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);
  const [tempStartDate, setTempStartDate] = useState<Date | null>(null);
  const [tempEndDate, setTempEndDate] = useState<Date | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [appLoader, setAppLoader] = useState<boolean>(false);
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [selectedMissing, setSelectedMissings] = useState<Game_Details>();

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useEffect(() => {
    INIT();
  }, []);

  useEffect(() => {
    filterData();
  }, [selectedFilter, startDate, endDate, lotterGame]);

  const filterData = () => {
    setAppLoader(true);
    const now = new Date();
    let filteredData = lotterGame;

    if (selectedFilter === 'daily') {
      const past24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      filteredData = lotterGame.filter(
        item => new Date(item.Date) >= past24Hours,
      );
    } else if (selectedFilter === 'weekly') {
      const pastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filteredData = lotterGame.filter(item => new Date(item.Date) >= pastWeek);
    } else if (selectedFilter === 'monthly') {
      const pastMonth = new Date();
      pastMonth.setMonth(pastMonth.getMonth() - 1);
      filteredData = lotterGame.filter(
        item => new Date(item.Date) >= pastMonth,
      );
    } else if (selectedFilter === 'yearly') {
      const pastYear = new Date();
      pastYear.setFullYear(pastYear.getFullYear() - 1);
      filteredData = lotterGame.filter(item => new Date(item.Date) >= pastYear);
    } else if (selectedFilter === 'custom' && startDate && endDate) {
      filteredData = lotterGame.filter(item => {
        const itemDate = new Date(item.Date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    setFilteredLotterGame(filteredData);
    setAppLoader(false);
  };

  const handleApplyFilter = () => {
    if (!tempStartDate || !tempEndDate) return;
    setStartDate(tempStartDate);
    setEndDate(tempEndDate);
    setSelectedFilter('custom');
    setModalVisible(false);
    filterData();
  };

  const handleClearFilter = () => {
    setTempStartDate(null);
    setTempEndDate(null);
    setSelectedFilter('daily');
    setModalVisible(false);
    filterData();
  };

  const INIT = async () => {
    await GetAllItemsWithFilter(
      (await getLotteryPort()).toString(),
      '/GetAllGames',
      setLotterGame,
      setLotterGameFil,
      setLoading,
      false,
    );
  };

  const setStartTime = (date: Date) => {
    const newDate = new Date(date);
    return new Date(
      newDate.getFullYear(),
      newDate.getMonth(),
      newDate.getDate(),
      0,
      0,
      0,
      0,
    );
  };

  const setEndTime = (date: Date) => {
    const newDate = new Date(date);
    return new Date(
      newDate.getFullYear(),
      newDate.getMonth(),
      newDate.getDate(),
      23,
      59,
      59,
      999,
    );
  };

  const renderItem = async ({item}: {item: Game_Details}) => {
    const GetShiftDetails = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetShiftDetails/:Shift_ID',
      {Shift_ID: item.Shift_ID},
    );

    const CashierName = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/loginEmployee/:Cashier_ID',
      {Cashier_ID: GetShiftDetails[0]?.Shift_Cashier_ID},
    );

    return (
      <View style={styles.ticketItemContainer}>
        <View style={styles.ticketInfoContainer}>
          <View
            style={{flexDirection: 'row', alignItems: 'center', width: '80%'}}>
            <Text style={styles.cashierText}>
              Cashier: {CashierName[0]?.EmpName}
            </Text>

            <Text style={styles.shiftIdText}>Shift ID: {item.Shift_ID}</Text>
          </View>
          <Text style={styles.gameNoText}>Game No: {item.Game_ID}</Text>
          <Text style={styles.missingTicketsText}>
            Missing Tickets:{' '}
            {item?.Missing_Ticket > 0
              ? '-' + item.Missing_Ticket
              : item.Missing_Ticket}
          </Text>
        </View>

        {item?.Missing_Ticket > 0 && (
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => {
              setSelectedMissings(item);
              setResetStock(true);
            }}>
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const UpdateMissings = async () => {
    try {
      if (
        !currentBarcode ||
        currentBarcode === undefined ||
        currentBarcode === null
      ) {
        Alert.alert('Please Enter the Updated Missings');
        return;
      }
      const getMissingTickets = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        {
          Shift_ID: selectedMissing?.Shift_ID,
          Location: selectedMissing?.Location,
        },
      );

      const shiftGameData: Partial<Game_Details> = {
        Game_ID: getMissingTickets[0]?.Game_ID,
        Open_Book: getMissingTickets[0]?.Open_Book,
        Close_Book: getMissingTickets[0]?.Close_Book,
        Stock_Level_Open: getMissingTickets[0]?.Stock_Level_Open,
        Stock_Level_Close: getMissingTickets[0]?.Stock_Level_Close,
        Shift_Open_Serial: getMissingTickets[0]?.Shift_Open_Serial,
        Shift_Close_Serial: getMissingTickets[0]?.Shift_Close_Serial,
        Missing_Ticket: Number(currentBarcode),
        Location: getMissingTickets[0]?.Location,
        Shift_ID: getMissingTickets[0]?.Shift_ID,
        Date: getMissingTickets[0]?.Date,
        Reset: getMissingTickets[0]?.Reset,
      };
      const applyDefault = applyDefaultsGameDetails(shiftGameData);
      const result = await updateData<Game_Details>({
        baseURL: (await getLotteryPort()).toString(),
        data: applyDefault,
        endpoint: '/updatelotgamesdetails',
      });

      if (result) {
        INIT();
        setResetStock(false);
        Alert.alert('Missing Updated Success');
      }
    } catch (error) {
      console.log('Error', error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    filterOptionsRow: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginVertical: hp('1.5%'),
      backgroundColor: colors.surface,
      borderRadius: 8,
      paddingVertical: hp('1%'),
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    filterOption: {
      flex: 1,
      alignItems: 'center',
    },
    radioContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
    },
    radioOuter: {
      height: 18,
      width: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 6,
    },
    radioInner: {
      height: 10,
      width: 10,
      borderRadius: 5,
      backgroundColor: colors.primary,
    },
    filterText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    listContainer: {
      marginTop: hp('1%'),
    },
    ticketItemContainer: {
      backgroundColor: colors.card,
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('2%'),
      marginBottom: hp('1.2%'),
      borderRadius: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    ticketInfoContainer: {
      flex: 1,
    },
    cashierText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
      marginBottom: 4,
      marginRight: 12,
    },
    shiftIdText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      marginBottom: 4,
    },
    gameNoText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      marginBottom: 4,
    },
    missingTicketsText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    editButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: wp('3%'),
      paddingVertical: hp('1%'),
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    editButtonText: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.small,
      color: '#FFFFFF',
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
      backgroundColor: colors.card,
      width: wp('85%'),
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.4 : 0.25,
      shadowRadius: 8,
      elevation: 5,
    },
    modalHeader: {
      textAlign: 'center',
      flexDirection: 'column',
      marginBottom: -20,
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    closeButton: {
      padding: 8,
    },
    datePickersContainer: {
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
    },
    datePickerButton: {
      backgroundColor: colors.surface,
      padding: hp('1.5%'),
      borderRadius: 8,
      marginBottom: hp('1.5%'),
      borderWidth: 1,
      borderColor: colors.border,
    },
    datePickerLabel: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      marginBottom: 5,
    },
    datePickerValue: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: hp('2%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    actionButton: {
      flex: 1,
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 1.5,
    },
    applyButton: {
      backgroundColor: colors.primary,
      marginRight: wp('2%'),
    },
    disabledButton: {
      backgroundColor: colors.disabled,
      opacity: 0.6,
    },
    clearButton: {
      backgroundColor: colors.textSecondary,
      marginLeft: wp('2%'),
    },
    actionButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    clearButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    modalBackdrop: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContainer: {
      backgroundColor: colors.card,
      width: wp('85%'),
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.4 : 0.25,
      shadowRadius: 8,
      elevation: 5,
    },
    modalHeaderText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    closeButtonContainer: {
      padding: 8,
    },
    inputContainerWrapper: {
      paddingHorizontal: wp('5%'),
      marginVertical: hp('2%'),
    },
    inputLabel: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      marginBottom: 5,
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: wp('5%'),
      paddingVertical: hp('2%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    cancelButton: {
      flex: 1,
      backgroundColor: colors.textSecondary,
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('2%'),
    },
    cancelButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    updateButton: {
      flex: 1,
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: hp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: wp('2%'),
    },
    updateButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    updateButtonText: {
      color: '#FFFFFF',
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
  });
  return (
    <View style={styles.container}>
      <Header
        NavName="Total Missing Tickets"
        isCalendar={true}
        Options={() => setModalVisible(true)}
      />
      <View style={styles.filterOptionsRow}>
        {['daily', 'weekly', 'monthly', 'yearly'].map(option => (
          <TouchableOpacity
            key={option}
            onPress={() => setSelectedFilter(option)}
            style={styles.filterOption}>
            <View style={styles.radioContainer}>
              <View style={styles.radioOuter}>
                {selectedFilter === option && (
                  <View style={styles.radioInner} />
                )}
              </View>
              <Text style={styles.filterText}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.listContainer}>
        <DataList
          data={filteredLotterGame}
          renderItem={renderItem}
          loading={loading}
          Hight="82%"
        />
      </View>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        statusBarTranslucent
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Date Range</Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}>
                {/* <MaterialIcons
                  name="close"
                  size={24}
                  color={MaterialColors.error.main}
                /> */}
              </TouchableOpacity>
            </View>

            <View style={styles.datePickersContainer}>
              <TouchableOpacity
                onPress={() => setShowStartPicker(true)}
                style={styles.datePickerButton}>
                <Text style={styles.datePickerLabel}>Start Date</Text>
                <Text style={styles.datePickerValue}>
                  {tempStartDate ? tempStartDate.toDateString() : 'Select'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setShowEndPicker(true)}
                style={styles.datePickerButton}>
                <Text style={styles.datePickerLabel}>End Date</Text>
                <Text style={styles.datePickerValue}>
                  {tempEndDate ? tempEndDate.toDateString() : 'Select'}
                </Text>
              </TouchableOpacity>
            </View>

            {showStartPicker && (
              <DateTimePicker
                value={tempStartDate || new Date()}
                mode="date"
                display="default"
                onChange={(event, date) => {
                  setShowStartPicker(false);
                  if (date) setTempStartDate(setStartTime(date));
                }}
              />
            )}

            {showEndPicker && (
              <DateTimePicker
                value={tempEndDate || new Date()}
                mode="date"
                display="default"
                onChange={(event, date) => {
                  setShowEndPicker(false);
                  if (date) setTempEndDate(setEndTime(date));
                }}
              />
            )}

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                onPress={handleApplyFilter}
                disabled={!tempStartDate || !tempEndDate}
                style={[
                  styles.actionButton,
                  styles.applyButton,
                  (!tempStartDate || !tempEndDate) && styles.disabledButton,
                ]}>
                <Text style={styles.actionButtonText}>Apply Filter</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleClearFilter}
                style={[styles.actionButton, styles.clearButton]}>
                <Text style={styles.clearButtonText}>Clear</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        key="resetModal"
        animationType="fade"
        transparent={true}
        statusBarTranslucent
        visible={resetStock}
        onRequestClose={() => setResetStock(false)}>
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalHeaderText}>Update Missing Tickets</Text>
              <TouchableOpacity
                style={styles.closeButtonContainer}
                onPress={() => setResetStock(false)}>
                {/* <MaterialIcons
                  name="close"
                  size={22}
                  color={MaterialColors.error.main}
                /> */}
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainerWrapper}>
              <Text style={styles.inputLabel}>
                Enter new value for missing tickets
              </Text>
              <AppTextInput
                PlaceHolder="Enter number of tickets"
                Title="Missing Tickets"
                onChangeText={value => {
                  setCurrentBarcode(value);
                }}
              />
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setResetStock(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.updateButton}
                onPress={() => UpdateMissings()}>
                <View style={styles.updateButtonContent}>
                  <Text style={styles.updateButtonText}>Update</Text>
                  <MaterialIcons
                    name="done"
                    size={18}
                    color={MaterialColors.text.onDark}
                    style={{marginLeft: 4}}
                  />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <AppLoader modalVisible={appLoader} setModalVisible={setAppLoader} />
    </View>
  );
};

export default MissingTickets;
