import AsyncStorage from '@react-native-async-storage/async-storage';

const PERMISSION_STORAGE_KEY = 'permissions';

export const savePermissionsToStorage = async permissions => {
  try {
    await AsyncStorage.setItem(
      PERMISSION_STORAGE_KEY,
      JSON.stringify(permissions),
    );
  } catch (error) {
    console.error('Error saving permissions:', error);
  }
};

export const getPermissionsFromStorage = async () => {
  try {
    const savedPermissions = await AsyncStorage.getItem(PERMISSION_STORAGE_KEY);
    return savedPermissions ? JSON.parse(savedPermissions) : {};
  } catch (error) {
    console.error('Error loading permissions:', error);
    return {};
  }
};

export const getEmployeeFromStorage = async () => {
  try {
    //const employeeData = await AsyncStorage.getItem('EMPLOYEE');
    const lotteryPermissions = await AsyncStorage.getItem(
      'LOTTERY_PERMISSIONS',
    );

    //const parsedEmployee = employeeData ? JSON.parse(employeeData) : {};
    const parsedLotteryPermissions = lotteryPermissions
      ? JSON.parse(lotteryPermissions)
      : {};
    // Merge lottery permissions with employee data dynamically
    return {...parsedLotteryPermissions};
  } catch (error) {
    console.error('Error loading employee data:', error);
    return null;
  }
};

export const hasPermission = async permissionKey => {
  const employee = await getEmployeeFromStorage();

  if (!employee) {
    console.error('No employee data found in storage.');
    return false;
  }

  return (
    employee[permissionKey] === 'Y' ||
    employee[permissionKey] === 'O' ||
    employee[permissionKey] === true
  );
};

export const navigateIfAuthorized = async (
  navigation,
  screenName,
  requiredPermission,
  params = {},
) => {
  const isAuthorized = await hasPermission(requiredPermission);

  if (isAuthorized) {
    navigation.navigate(screenName, params);
  } else {
    alert('You do not have permission to access this page.');
  }
};
