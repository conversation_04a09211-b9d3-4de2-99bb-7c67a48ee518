import {View, Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
  UpdatePurchaseOrder,
} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  createData,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  showAlert,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import DataList from '../../../components/Inventory/AppList';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import {deleteItem} from '../../../server/service';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type CountItemRouteProp = RouteProp<any, 'ReturnToVendor'>;

const ReturnVendor: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [mainPO, setMainPO] = useState<PurchaseOrder>(route?.params?.ItemData);
  const [itemPO, setItemPO] = useState<PurchaseOrderItems[]>([]);
  const [_initialPO, setInitialPO] = useState<PurchaseOrderItems[]>([]);
  const [_itemPOFilter, setItemPOFilter] = useState<PurchaseOrderItems[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [poNumber, _setPoNumber] = useState<string>(
    route?.params?.ItemData?.PO_Number || mainPO.PO_Number || '',
  );
  const [poType, _setPoType] = useState<number>(route?.params?.POTYPE || 0);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useEffect(() => {
    console.log('Workout', poType, route?.params?.POTYPE);
  }, [0]);

  const getInitialDetails = useCallback(async () => {
    GetItemsWithParams(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setItemPO,
      setItemPOFilter,
      setLoading,
      {PO_Number: poNumber},
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderitems/:PO_Number',
      setInitialPO,
      {PO_Number: poNumber},
      false,
    );
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpounique/:PO_Number',
      setMainPO,
      {PO_Number: poNumber || mainPO.PO_Number},
      true,
    );
  }, [poNumber, mainPO.PO_Number]);

  useFocusEffect(
    useCallback(() => {
      getInitialDetails();
    }, [getInitialDetails]),
  );

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <View>
        <PurchaseOrderItemCart
          Name={item?.ItemName}
          Ordered={item?.Quan_Ordered}
          RecivedNow={item?.Quan_Received}
          DamagedNow={item.Quan_Damaged}
          Delete={() => DeletePurchaseItem(item?.PO_Number, item.ItemNum, item)}
          ItemCost={Number(item.Quan_Ordered) * Number(item.CostPer)}
        />
      </View>
    );
  };

  const DeletePurchaseItem = (
    ponumber: number,
    itemnum: string,
    item: PurchaseOrderItems,
  ) => {
    if (poType === 1) {
      removeListItem(ponumber, itemnum, item);
    } else {
      removeDirectPurchase(ponumber, itemnum, item);
    }
  };

  const removeDirectPurchase = (
    PONumber: number,
    Barcode: string,
    PoItems: PurchaseOrderItems,
  ) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          if (_initialPO.length === 1) {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );

            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              updatePO(0, 0);
              GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/getpounique/:PO_Number',
                setMainPO,
                {
                  PO_Number:
                    route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
                },
                true,
              );
            }
          } else {
            const result = await deleteItem(
              (await getInventoryPort()).toString(),
              '/DeletePOItems/:PO_Number/:ItemNum',
              {PO_Number: PONumber, ItemNum: Barcode},
            );
            if (result.success) {
              const updatedArray = itemPO.filter(
                item => item.ItemNum !== Barcode,
              );
              setItemPO(updatedArray);
              const TotalCostCal =
                Number(PoItems.Quan_Ordered) * Number(PoItems.CostPer);
              const TotalCostValid =
                Number(mainPO.Total_Cost) - Number(TotalCostCal);
              const TotalCost = mainPO.Total_Cost <= 0 ? 0 : TotalCostValid;

              const ExpectedCal =
                Number(mainPO.ExpectedAmountToReceive) -
                Number(PoItems.Quan_Ordered);
              const ExpectedRecive =
                mainPO.ExpectedAmountToReceive <= 0 ? 0 : ExpectedCal;
              updatePO(TotalCost, ExpectedRecive);
              GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/getpounique/:PO_Number',
                setMainPO,
                {
                  PO_Number:
                    route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
                },
                true,
              );
            }
          }
          //AdjustInventory(PoItems);
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };
  const removeListItem = (
    PONumber: number,
    Barcode: string,
    PoItems: PurchaseOrderItems,
  ) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          let TotalCost = 0;
          let ExpectedRecive = 0;
          const positiveTotal = Math.abs(mainPO.Total_Cost); //14

          const calQuanOrdered = Math.abs(PoItems.Quan_Ordered); //20
          const calTotals = Number(calQuanOrdered) * Number(PoItems.CostPer); // 20 * 0.70 = 14

          const TotalCostCal = Number(positiveTotal) - Number(calTotals); // 14 - 14 = 0

          const positiveExpected = Math.abs(mainPO.ExpectedAmountToReceive); // -20

          const ExpectedReciveCal =
            Number(positiveExpected) - Number(calQuanOrdered); // 20 - 20

          if (TotalCostCal > 0) {
            const TotalCostIN = '-' + TotalCostCal;

            const TotalExpectIN = '-' + ExpectedReciveCal;

            TotalCost = Number(TotalCostIN);

            ExpectedRecive = Number(TotalExpectIN);
          } else {
            const TotalCostIN = TotalCostCal;

            const TotalExpectIN = ExpectedReciveCal;

            TotalCost = Number(TotalCostIN);

            ExpectedRecive = Number(TotalExpectIN);
          }

          const result = await deleteItem(
            (await getInventoryPort()).toString(),
            '/DeletePOItems/:PO_Number/:ItemNum',
            {PO_Number: PONumber, ItemNum: Barcode},
          );
          if (result.success) {
            const updatedArray = itemPO.filter(
              item => item.ItemNum !== Barcode,
            );
            setItemPO(updatedArray);
            updatePO(TotalCost, ExpectedRecive);
            GetItemsParamsNoFilter(
              (await getInventoryPort()).toString(),
              '/getpounique/:PO_Number',
              setMainPO,
              {
                PO_Number:
                  route?.params?.ItemData?.PO_Number || mainPO.PO_Number,
              },
              true,
            );
            //AdjustInventory(PoItems);
          }
        } else {
          console.log('Item will not be deleted');
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const updatePO = async (TotalCost?: number, Expected?: number) => {
    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPO.PO_Number,
      Total_Cost: TotalCost,
      Total_Cost_Received: poType === 1 ? TotalCost : 0,
      ExpectedAmountToReceive: Expected,
      Store_ID: mainPO.Store_ID,
      DateTime: mainPO.DateTime,
      Reference: mainPO.Reference,
      Vendor_Number: mainPO.Vendor_Number,
      Ship_Via: mainPO.Ship_Via,
      Status: 'C',
      Cashier_ID: mainPO.Cashier_ID,
      Due_Date: mainPO.Due_Date,
      Last_Modified: mainPO.Last_Modified,
      Cancel_Date: mainPO.DateTime,
      Order_Reason: mainPO.Order_Reason,
      POType: poType === 1 ? 1 : 2,
      Dirty: true,
      Print_Notes_On_PO: true,
      ShipTo_1: mainPO.ShipTo_1,
      ShipTo_2: mainPO.ShipTo_2,
      ShipTo_4: mainPO.ShipTo_4,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getpounique/:PO_Number',
        setMainPO,
        {PO_Number: route?.params?.ItemData?.PO_Number || mainPO.PO_Number},
        true,
      );
    } else {
      console.log('Error!!');
    }
  };

  const AdjustStocking = () => {
    if (itemPO.length > 0) {
      if (poType === 1) {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Return(quan);
        });
      } else {
        itemPO.map((quan: PurchaseOrderItems) => {
          AdjustInventory_Direct(quan);
        });
      }
    } else {
      Alert.alert('Please Select Direct Purchase Items');
    }
  };

  const AdjustInventory_Direct = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );

    const adjustStock =
      Number(PurchaseItems.Quan_Ordered) +
      Number((getInventory as any)[0].In_Stock);
    const inventoryData: Partial<Inventory> = {
      ItemNum: (getInventory as any)[0]?.ItemNum,
      ItemName: (getInventory as any)[0]?.ItemName,
      Dept_ID: (getInventory as any)[0]?.Dept_ID,
      Cost: (getInventory as any)[0]?.Cost,
      Price: (getInventory as any)[0]?.Price,
      Retail_Price: (getInventory as any)[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: (getInventory as any)[0]?.Date_Created,
      Last_Sold: (getInventory as any)[0]?.Last_Sold,
      Location: (getInventory as any)[0]?.Location,
      Vendor_Number: (getInventory as any)[0]?.Vendor_Number,
      Vendor_Part_Num: (getInventory as any)[0]?.Vendor_Part_Num,
      Reorder_Level: (getInventory as any)[0]?.Reorder_Level,
      Reorder_Quantity: (getInventory as any)[0]?.Reorder_Quantity,
      ReOrder_Cost: (getInventory as any)[0]?.ReOrder_Cost,
      Unit_Size: (getInventory as any)[0]?.Unit_Size,
      Unit_Type: (getInventory as any)[0]?.Unit_Type,
      FoodStampable: (getInventory as any)[0]?.FoodStampable,
      Tax_1: (getInventory as any)[0]?.Tax_1[0],
      Tax_2: (getInventory as any)[0]?.Tax_2[0],
      Tax_3: (getInventory as any)[0]?.Tax_3[0],
      Tax_4: (getInventory as any)[0]?.Tax_4[0],
      Tax_5: (getInventory as any)[0]?.Tax_5[0],
      Tax_6: (getInventory as any)[0]?.Tax_6[0],
      Check_ID: (getInventory as any)[0]?.Check_ID,
      Check_ID2: (getInventory as any)[0]?.Check_ID2,
      Store_ID: (getInventory as any)[0]?.Store_ID,
      ItemName_Extra: (getInventory as any)[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: (getInventory as any)[0].ItemNum,
        Store_ID: ValidStore,
        Quantity: adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: (getInventory as any)[0].Cost,
      };

      const applyDefaultInventory =
        applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefaultInventory,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Direct Purchase Items Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };

  const AdjustInventory_Return = async (PurchaseItems: PurchaseOrderItems) => {
    const getInventory = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: PurchaseItems.ItemNum},
    );
    const adjustStock =
      Number((getInventory as any)[0]?.In_Stock) +
      Number(PurchaseItems.Quan_Ordered);
    const inventoryData: Partial<Inventory> = {
      ItemNum: (getInventory as any)[0]?.ItemNum,
      ItemName: (getInventory as any)[0]?.ItemName,
      Dept_ID: (getInventory as any)[0]?.Dept_ID,
      Cost: (getInventory as any)[0]?.Cost,
      Price: (getInventory as any)[0]?.Price,
      Retail_Price: (getInventory as any)[0]?.Retail_Price,
      In_Stock: adjustStock,
      Date_Created: (getInventory as any)[0]?.Date_Created,
      Last_Sold: (getInventory as any)[0]?.Last_Sold,
      Location: (getInventory as any)[0]?.Location,
      Vendor_Number: (getInventory as any)[0]?.Vendor_Number,
      Vendor_Part_Num: (getInventory as any)[0]?.Vendor_Part_Num,
      Reorder_Level: (getInventory as any)[0]?.Reorder_Level,
      Reorder_Quantity: (getInventory as any)[0]?.Reorder_Quantity,
      ReOrder_Cost: (getInventory as any)[0]?.ReOrder_Cost,
      Unit_Size: (getInventory as any)[0]?.Unit_Size,
      Unit_Type: (getInventory as any)[0]?.Unit_Type,
      FoodStampable: (getInventory as any)[0]?.FoodStampable,
      Tax_1: (getInventory as any)[0]?.Tax_1[0],
      Tax_2: (getInventory as any)[0]?.Tax_2[0],
      Tax_3: (getInventory as any)[0]?.Tax_3[0],
      Tax_4: (getInventory as any)[0]?.Tax_4[0],
      Tax_5: (getInventory as any)[0]?.Tax_5[0],
      Tax_6: (getInventory as any)[0]?.Tax_6[0],
      Check_ID: (getInventory as any)[0]?.Check_ID,
      Check_ID2: (getInventory as any)[0]?.Check_ID2,
      Store_ID: (getInventory as any)[0]?.Store_ID,
      ItemName_Extra: (getInventory as any)[0]?.ItemName_Extra,
    };
    const applyDefault = applyDefaults(inventoryData);

    const result = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatebarcode',
    });

    if (result) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: (getInventory as any)[0]?.ItemNum,
        Store_ID: ValidStore,
        Quantity: '-' + adjustStock,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'C',
        Description: 'ITEM CREATION',
        Cashier_ID: ValideCashier,
        CostPer: (getInventory as any)[0]?.Cost,
      };

      const applyDefaultReturn =
        applyDefaultsInventoryAdjust(inventoryAdjustData);
      const invenIN = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefaultReturn,
        endpoint: '/createinvetoryin',
      });
      if (invenIN) {
        Alert.alert('Vendor Return Created');
        navigation.navigate('More');
      } else {
        Alert.alert('Failed to Add Item');
      }
    }
  };

  const NavHandle = () => {
    if (itemPO.length > 0) {
      showAlert(
        poType === 1
          ? 'Vendor Return Items Not Saved Yet, Would You Like to Save Close?'
          : 'Direct Purchase Items Not Saved Yet, Would You Like to Save Close?',
      )
        .then(async result => {
          if (result) {
            AdjustStocking();
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      showAlert(
        "You didn't add any items to this direct purchase order!",
        'Confirmation',
        true,
        'OK',
        'Discard Order',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('More');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const isReturnOrDsd = poType === 1 ? false : true;

  const navigateToVendorItems = () => {
    navigation.navigate('ReturnVendorItemsScreen', {
      mainPO,
      itemPO,
      isReturnOrDsd,
      isEnableFilter: false,
      setFilter: () => {},
      poType,
    });
  };

  const styles = StyleSheet.create({
    rootContainer: {
      backgroundColor: colors.background,
      flex: 1,
      justifyContent: 'space-between',
    },
    mainContainer: {
      paddingHorizontal: wp('2.5%'),
      flex: 1,
    },
    vendorInfoCard: {
      backgroundColor: colors.surface,
      paddingVertical: hp('1.2%'),
      paddingHorizontal: wp('3%'),
      borderRadius: 8,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: hp('1%'),
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    vendorDetails: {
      gap: hp('0.5%'),
    },
    vendorName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
    },
    vendorNumber: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
    },
    totalCost: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
    },
    addItemsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
      marginVertical: hp('0.5%'),
    },
    addItemsText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.primary,
      paddingVertical: hp('0.5%'),
      paddingHorizontal: wp('1.5%'),
    },
    bottomButtonContainer: {
      position: 'absolute',
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: colors.surface,
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
  });

  return (
    <View style={styles.rootContainer}>
      <View style={styles.mainContainer}>
        <Header
          NavName={poType === 1 ? 'Return to Vendor' : 'Direct Purchase'}
          isProvid={true}
          Onpress={() => NavHandle()}
        />

        <View style={styles.vendorInfoCard}>
          <View style={styles.vendorDetails}>
            <Text style={styles.vendorName}>{mainPO.Company}</Text>
            <Text style={styles.vendorNumber}>
              Vendor Number: {mainPO.Vendor_Number}
            </Text>
          </View>
          <Text style={styles.totalCost}>
            $
            {mainPO.Total_Cost.toFixed(2).includes('-0.0')
              ? '0.00'
              : mainPO.Total_Cost.toFixed(2)}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.addItemsButton}
          onPress={navigateToVendorItems}>
          <Text style={styles.addItemsText}>Add Items</Text>
          <AntDesign
            name="pluscircle"
            size={hp('3.3%')}
            color={colors.primary}
          />
        </TouchableOpacity>

        <DataList
          data={itemPO}
          renderItem={renderItem}
          loading={loading}
          Hight="70%"
        />
      </View>

      <View style={styles.bottomButtonContainer}>
        <FAB
          label="Save & Close"
          position="bottomRight"
          onPress={() => AdjustStocking()}
        />
      </View>
    </View>
  );
};

export default ReturnVendor;
