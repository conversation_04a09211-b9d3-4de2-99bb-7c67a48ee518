import {View, Text, TouchableOpacity, ViewStyle, TextStyle} from 'react-native';
import React from 'react';
import {Backround, Primary} from '../../constants/Color';
import {Fonts} from '../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
type Props = {
  Title?: string;
  OnPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  BackRound?: string;
};

const AppButton = (props: Props) => {
  const colors = useThemeColors();
  return (
    <View>
      <TouchableOpacity
        disabled={props.disabled}
        style={[
          {
            backgroundColor: props.BackRound || colors.primary,
            paddingVertical: hp('1.8%'),
            borderRadius: 30,
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            opacity: props.disabled ? 0.6 : 1,
          },
          props.style,
        ]}
        onPress={props.OnPress}>
        <Text
          style={[
            {
              fontSize: hp('2.2%'),
              color: '#FFFFFF',
              fontFamily: Fonts.OnestMedium,
            },
            props.textStyle,
          ]}>
          {props.Title}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default AppButton;
