import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  Modal,
  StyleSheet,
  TextInput,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Backround, Primary} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import SearchBar from '../../../components/LotteryScratch/SearchBar';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  createData,
  formatNumber,
  GetAllItems,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {Department, Inventory, Inventory_In} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import TicketsDetails from '../../../components/LotteryScratch/TicketsDetails';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
  Activate_Book,
  Game_Details,
  Remove_Book,
} from '../../../Types/Lottery/Lottery_Types';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {createItem} from '../../../server/service';
import TicketsDetailsForOther from '../../../components/LotteryScratch/TicketDetailsForOther';
import {MaterialColors} from '../../../constants/MaterialColors';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import Search from '../../../components/Inventory/Search';

type BarcodeScreenRouteProp = RouteProp<any, 'ActivaBooks'>;
const LOT_ActivateBooks: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [removeModal, setRemoveModal] = useState<boolean>(false);
  const [lottery, setLottery] = useState<Inventory[]>([]);
  const [lotteryFilter, setLotteryFilter] = useState<Inventory[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [allGames, setAllGames] = useState<Activate_Book[]>([]);
  const [selectedLottery, setSelectedLottery] = useState<Inventory>();
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [barcodeData, setBarcodeData] = useState<{[key: string]: any}>({});
  const [searchQuery, setSearchQuery] = useState<string>('');

  const textInputRef = useRef<TextInput>(null);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      checkServerConnection();
    }, []),
  );

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      getInitDept();
      GetDepartmentsbyTickets();
    }
  };

  const getInitDept = async () => {
    const data = await GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
    );
    GetAllItems<Activate_Book[]>(
      (await getLotteryPort()).toString(),
      '/GetAllActiveBooks',
      setAllGames,
      setLoading,
    );
  };

  const GetDepartmentsbyTickets = async () => {
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      const data = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/inventorybydepidwithLocation/:Dept_ID',
        setLottery,
        {Dept_ID: lotteryDepartment},
      );
      setLotteryFilter(data);
      setSelectedDepartment(lotteryDepartment);
    } else {
      showAlert(
        'Lottery Department Not Found Would You Like to Select the Lottery Department?',
        'Lottery Not Found!',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('LotterySetup');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const ActivateBook = (InventoryItem: Inventory) => {
    setSelectedLottery(InventoryItem);
    if (InventoryItem.In_Stock > 0) {
      showAlert(
        `You've got [${InventoryItem.In_Stock}] tickets left! Sell these out or remove the book to activate a new one.`,
        'Confirmation',
        true,
        'OK',
        'Remove Book',
      )
        .then(async result => {
          if (result) {
            setRemoveModal(true);
          } else {
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } else {
      navigation.navigate('ActivateByBooks', {ItemData: InventoryItem});
    }
  };

  const renderItem = ({item}: {item: Inventory}) => {
    return (
      <View>
        <TicketsDetailsForOther
          isShift={false}
          Item={item}
          OnpressIN={() => ActivateBook(item)}
          Stock={item.In_Stock}
          Cost={Number(item?.Price)}
        />
      </View>
    );
  };
  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      lottery,
      ['ItemNum', 'ItemName'],
      setLotteryFilter,
      setLoading,
    );
  };

  const RemoveBook = async () => {
    const inventoryData: Partial<Inventory> = {
      ItemNum: selectedLottery?.ItemNum,
      ItemName: selectedLottery?.ItemName,
      Dept_ID: selectedLottery?.Dept_ID,
      Cost: selectedLottery?.Cost,
      Price: selectedLottery?.Price,
      Retail_Price: selectedLottery?.Retail_Price,
      In_Stock: 0,
      Date_Created: selectedLottery?.Date_Created,
      Last_Sold: selectedLottery?.Last_Sold,
      Location: selectedLottery?.Location,
      Vendor_Number: selectedLottery?.Vendor_Number,
      Vendor_Part_Num: selectedLottery?.Vendor_Part_Num,
      Reorder_Level: selectedLottery?.Reorder_Level,
      Reorder_Quantity: selectedLottery?.Reorder_Quantity,
      ReOrder_Cost: selectedLottery?.ReOrder_Cost,
      Unit_Size: selectedLottery?.Unit_Size,
      Unit_Type: selectedLottery?.Unit_Type,
      FoodStampable: selectedLottery?.FoodStampable,
      Tax_1: selectedLottery?.Tax_1[0],
      Tax_2: selectedLottery?.Tax_2[0],
      Tax_3: selectedLottery?.Tax_3[0],
      Tax_4: selectedLottery?.Tax_4[0],
      Tax_5: selectedLottery?.Tax_5[0],
      Tax_6: selectedLottery?.Tax_6[0],
      Check_ID: selectedLottery?.Check_ID,
      Check_ID2: selectedLottery?.Check_ID2,
      Store_ID: selectedLottery?.Store_ID,
      ItemName_Extra: selectedLottery?.ItemName_Extra,
    };

    const applyDefaultInv = applyDefaults(inventoryData);
    const resultInv = await updateData<Inventory>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefaultInv,
      endpoint: '/updatebarcode',
    });

    if (resultInv) {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;
      const CashierID = await AsyncStorage.getItem('SWIPEID');
      const ValideCashier = CashierID === null ? '100101' : CashierID;

      const inventoryAdjustData: Partial<Inventory_In> = {
        ItemNum: selectedLottery?.ItemNum,
        Store_ID: ValidStore,
        Quantity:
          selectedLottery?.In_Stock > 0 ? '-' + selectedLottery?.In_Stock : 0,
        DateTime: getFormateDate(Date()),
        Dirty: true,
        TransType: 'L',
        Description: 'LOTTERY SCRATCH',
        Cashier_ID: ValideCashier,
        CostPer: selectedLottery?.Cost,
      };

      const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
      const result = await createData<Inventory_In>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createinvetoryin',
      });

      if (result) {
        const LotteryRemove: Remove_Book = {
          Store_ID: ValidStore,
          DateTime: getFormateDate(Date()),
          EndSerial: currentBarcode,
          Remove_Stock_Level: selectedLottery?.In_Stock,
        };

        const result = await createItem(
          (await getLotteryPort()).toString(),
          '/createremovebook',
          LotteryRemove,
        );
        if (result) {
          Alert.alert('Tickets Removed Success!');
          getInitDept();
          GetDepartmentsbyTickets();
          setRemoveModal(false);
        }
      }
    }
  };
  const styles = StyleSheet.create({
    container: {
      justifyContent: 'space-between',
      flex: 1,
    },
    modalContainer: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    innerContainer: {
      paddingHorizontal: wp('2.5%'),
    },
  });
  return (
    <View
      style={{
        backgroundColor: colors.background,
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Activate Books" />
      <Search
        textInputRef={textInputRef}
        PlaceHolder="Search..."
        Value={searchQuery}
        onChange={onSearchChange}
        keyboardON={true}
      />
      {false && (
        <AppDropDown
          label="Department"
          options={departmentOptions}
          selectedValue={selectedDepartment}
          onSelect={value => GetDepartmentsbyTickets(value)}
        />
      )}
      <Text
        style={{
          textAlign: 'center',
          fontSize: FontSizes.medium,
          fontFamily: Fonts.OnestBold,
          color: colors.text,
          margin: 15,
        }}>{`(${lottery.length}) Games Found`}</Text>
      <View>
        <DataList
          data={lotteryFilter}
          renderItem={renderItem}
          loading={loading}
          Hight="75%"
          IsCreate={true}
          IsCreateMessage="Lottery Games Not Found Would You Like to Create New Game?"
          IsCreateBtnLabel="Add New Game"
          onIsCreate={async () => {
            const lotteryDepartment = await AsyncStorage.getItem(
              'LOTTERY_DEP_ID',
            );

            if (lotteryDepartment) {
              navigation.navigate('Lookup', {
                isFromLottery: true,
                LOTTERYDEPT: lotteryDepartment,
              });
            }
          }}
        />
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={removeModal}
        onRequestClose={() => setRemoveModal(false)}>
        <View style={styles.modalContainer}>
          <View
            style={{
              backgroundColor: colors.surface,
              width: '93%',
              height: '50%',
              borderRadius: 15,
              paddingVertical: 10,
              shadowColor: colors.shadow,
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: isDark ? 0.4 : 0.25,
              shadowRadius: 8,
              elevation: 8,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: wp('2.5%'),
                paddingVertical: hp('1.5%'),
              }}>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('2.5%'),
                  color: colors.text,
                }}>
                Reset Shift Tickets
              </Text>

              <TouchableOpacity
                style={{paddingLeft: 10}}
                onPress={() => setRemoveModal(false)}>
                <AntDesign
                  name="closecircle"
                  color={colors.error}
                  size={hp('4%')}
                />
              </TouchableOpacity>
            </View>

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingVertical: hp('2%'),
                width: '100%',
              }}>
              <View style={{width: '100%', paddingHorizontal: 10}}>
                <AppFocus
                  PlaceHolder="Scan Book Number"
                  Title="Scan Book Number"
                  onChangeText={value => {
                    const formattedSerial = formatNumber(value);
                    setCurrentBarcode(formattedSerial);
                  }}
                />
              </View>
            </View>

            <View
              style={{
                paddingHorizontal: wp('2.5%'),
                position: 'absolute',
                right: 0,
                left: 0,
                bottom: 0,
                marginBottom: 10,
              }}>
              <AppButton Title="Reset" OnPress={() => RemoveBook()} />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default LOT_ActivateBooks;
