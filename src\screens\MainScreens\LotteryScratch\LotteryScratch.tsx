import {
  View,
  Text,
  ScrollView,
  Alert,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import MoreScreen from '../../../components/Inventory/MoreScreen';
import {showAlert} from '../../../utils/PublicHelper';
import {hasPermission} from '../../../utils/permissionHelper';
import {MaterialColors} from '../../../constants/MaterialColors';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Octicons from 'react-native-vector-icons/Octicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const LotteryScratch: React.FC<NavProps> = ({navigation}) => {
  const [status, setStatus] = useState<boolean>(false);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const menuOptions = [
    {
      name: 'Start/End Shift',
      description: 'Manage your current shift',
      permission: 'StartOrEndShift',
      navigate: 'IntialSetup',
      icon: (
        <MaterialIcons
          name="access-time"
          size={hp('3%')}
          color={colors.primary}
        />
      ),
    },
    {
      name: 'Activate Books',
      description: 'Activate new lottery books',
      permission: 'ActivateBook',
      navigate: 'ActivaBooks',
      icon: (
        <MaterialIcons name="book" size={hp('3%')} color={colors.primary} />
      ),
    },
    {
      name: 'Game Lookup',
      description: 'Search and view game details',
      permission: 'CreateNewGame',
      navigate: async () => {
        const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');

        if (lotteryDepartment) {
          navigation.navigate('Lookup', {
            isFromLottery: true,
            LOTTERYDEPT: lotteryDepartment,
          });
        } else {
          showAlert(
            'Lottery Department Not Found. Would you like to select the Lottery Department?',
            'Lottery Not Found!',
          )
            .then(result => {
              if (result) {
                navigation.navigate('LotterySetup');
              }
            })
            .catch(error => {
              console.error('Error showing alert', error);
            });
        }
      },
      icon: (
        <FontAwesome5 name="search" size={hp('2.8%')} color={colors.primary} />
      ),
    },
    {
      name: 'Organize Slot',
      description: 'Arrange and organize lottery slots',
      permission: 'OrganizeSlot',
      navigate: 'OrganizeSlot',
      icon: (
        <Octicons name="organization" size={hp('3%')} color={colors.primary} />
      ),
    },
    {
      name: 'View Shift Report',
      description: 'Review shift performance and details',
      permission: 'ViewShift',
      navigate: 'ShiftReport',
      icon: (
        <MaterialCommunityIcons
          name="file-document-outline"
          size={hp('3%')}
          color={colors.primary}
        />
      ),
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: wp('5%'),
    },
    scrollContent: {
      paddingVertical: hp('2%'),
      paddingBottom: hp('5%'),
    },
    headerTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('3%'),
      marginBottom: hp('2%'),
      textAlign: 'center',
    },
    descriptionCard: {
      borderRadius: 16,
      padding: hp('2%'),
      marginBottom: hp('3%'),
    },
    descriptionText: {
      fontFamily: Fonts.OnestRegular,
      fontSize: hp('1.5%'),
      lineHeight: hp('2%'),
    },
    menuListContainer: {
      borderRadius: 16,
      overflow: 'hidden',
      marginTop: hp('1%'),
    },
    itemWithBorder: {
      borderBottomWidth: 1,
    },
  });

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        <Text style={[styles.headerTitle, {color: colors.text}]}>
          Lottery Instant Games
        </Text>

        <View
          style={[styles.descriptionCard, {backgroundColor: colors.surface}]}>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              fontSize: hp('1.8%'),
              color: colors.primary,
              marginBottom: hp('0.5%'),
            }}>
            Instant Game Management DashBoard
          </Text>
          <Text style={[styles.descriptionText, {color: colors.textSecondary}]}>
            Get instant access to all the tools you need to manage your games
            effectively. From tracking shifts and activating new games to
            analyzing performance and organizing your inventory, our platform is
            designed to streamline your workflow. What would you like to do
            today?
          </Text>
        </View>

        <View
          style={[styles.menuListContainer, {backgroundColor: colors.surface}]}>
          {menuOptions.map((option, index) => (
            <View
              key={index}
              style={[
                index !== menuOptions.length - 1 && [
                  styles.itemWithBorder,
                  {borderBottomColor: colors.border},
                ],
              ]}>
              <MoreScreen
                PageName={option.name}
                Description={option.description}
                Icon={option.icon}
                Onpress={async () => {
                  // Check permission
                  const isAuthorized = await hasPermission(option.permission);
                  if (!isAuthorized) {
                    Alert.alert(`You do not have permission to ${option.name}`);
                    return;
                  }

                  // Navigate based on type
                  if (typeof option.navigate === 'function') {
                    await option.navigate();
                  } else {
                    navigation.navigate(option.navigate);
                  }
                }}
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default LotteryScratch;
