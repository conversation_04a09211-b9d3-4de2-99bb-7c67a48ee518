import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import PendingChangesCart from '../../../components/Inventory/PendingChangesCart'
import Header from '../../../components/Inventory/Header'
import AppButton from '../../../components/Inventory/AppButton'

const PendingChanges = () => {
  return (
    <View style={{backgroundColor: Backround, width: "100%", height: "100%"}}>
        <Header NavName='Pending Changes'/>
      <View style={{paddingHorizontal: 10, marginTop: 20}}>
         <PendingChangesCart Name='Inventory Edits(2)'/>
         <View style={{marginTop: 50}}>
            <PendingChangesCart Name='Inventory Taking - Count & Adjust(5)'/>
         </View>

         <View style={{marginTop: 50}}>
         <PendingChangesCart  Name='Purchase Order Created(10)'/>
         </View>
      </View>
      <View style={{ paddingHorizontal: 10, marginTop: 90}}>
        <AppButton Title='Send to Pos'/>
      </View>
    </View>
  )
}

export default PendingChanges