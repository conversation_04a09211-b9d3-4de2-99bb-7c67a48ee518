import React, {useCallback, useRef, useState} from 'react';
import {View, TextInput, Keyboard, Platform, StyleSheet} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useFocusEffect} from '@react-navigation/native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

// Components
import Header from '../../../components/Inventory/Header';
import ItemPickListCart from '../../../components/Inventory/ItemPickListCart';
import DataList from '../../../components/Inventory/AppList';
import FAB from '../../../components/common/FAB';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';

// Utils & Services
import {
  GetAllItemsWithFilter,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import {applyDefaultsInvoiceTotals} from '../../../Validator/Inventory/Barcode';
import {deleteItem} from '../../../server/service';
import {getInventoryPort} from '../../../server/InstanceTypes';

// Types
import {Invoice_Totals} from '../../../server/types';

// Theme
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const ItemPickList: React.FC<NavProps> = ({navigation}) => {
  const [itemPickList, setItemPickList] = useState<Invoice_Totals[]>([]);
  const [filterPickList, setFilterPickList] = useState<Invoice_Totals[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showLookup, setShowLookup] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);

  const getInvoiceHoldID = useCallback(async () => {
    const data = await GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getinvoicehold',
      setItemPickList,
      setFilterPickList,
      setLoading,
    );

    if (!data) {
      showAlertOK(
        'Database Connection Failed, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
      return;
    }
    setItemPickList(data);
  }, []);

  useFocusEffect(
    useCallback(() => {
      getInvoiceHoldID();
    }, [getInvoiceHoldID]),
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await getInvoiceHoldID();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [getInvoiceHoldID]);

  const onSearchChange = useCallback(
    async (text: string) => {
      setSearchQuery(text);
      setLoading(true);

      if (text === '') {
        setFilterPickList(itemPickList);
      } else {
        const lowercaseText = text.toLowerCase();
        const filtered = itemPickList.filter(
          item =>
            item.OnHoldID?.toString().toLowerCase().includes(lowercaseText) ||
            item.Invoice_Number?.toString()
              .toLowerCase()
              .includes(lowercaseText),
        );
        setFilterPickList(filtered);
      }

      setTimeout(() => setLoading(false), 100);
    },
    [itemPickList],
  );

  const removePO = useCallback((RemovePO: Invoice_Totals) => {
    showAlert(
      'Are you sure you want to delete this on-hold order? All associated data will be lost.',
      'Delete On-Hold Order?',
    )
      .then(async result => {
        if (result) {
          await updateInvoiceTotals(RemovePO);
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  }, []);

  const updateInvoiceTotals = useCallback(
    async (pickListItem: Invoice_Totals) => {
      try {
        setLoading(true);
        const pickListData: Partial<Invoice_Totals> = {
          Invoice_Number: pickListItem.Invoice_Number,
          ReferenceInvoiceNumber: pickListItem.Invoice_Number.toString(),
          Orig_OnHoldID: pickListItem.Orig_OnHoldID,
          Store_ID: pickListItem.Store_ID,
          Cashier_ID: pickListItem.Cashier_ID,
          CustNum: pickListItem.CustNum,
          DateTime: pickListItem.DateTime,
          Total_Price: pickListItem.Total_Price,
          Total_Cost: pickListItem.Total_Cost,
          Total_Tax1: pickListItem.Total_Tax1,
          Grand_Total: pickListItem.Grand_Total,
          Station_ID: pickListItem.Station_ID,
          Payment_Method: pickListItem.Payment_Method,
          Status: 'C',
          Taxed_1: pickListItem.Taxed_1,
          Taxed_Sales: pickListItem.Taxed_Sales,
          Dirty: pickListItem.Dirty,
          CourseOrderingProgress: pickListItem.CourseOrderingProgress,
          Total_UndiscountedSale: pickListItem.Total_UndiscountedSale,
        };

        const applyDefult = applyDefaultsInvoiceTotals(pickListData);
        const inventoryPort = (await getInventoryPort()).toString();

        const result = await updateData<Invoice_Totals>({
          baseURL: inventoryPort,
          data: applyDefult,
          endpoint: '/updateinvoice',
        });

        if (result) {
          await deleteItem(inventoryPort, '/DeleteOnHold/:Invoice_Number', {
            Invoice_Number: pickListItem.Invoice_Number,
          });
          await getInvoiceHoldID();
        }
      } catch (error) {
        console.error('Error updating invoice totals:', error);
      } finally {
        setLoading(false);
      }
    },
    [getInvoiceHoldID],
  );

  const renderItem = useCallback(
    ({item}: {item: Invoice_Totals}) => {
      return (
        <ItemPickListCart
          key={item.Invoice_Number}
          Reference={item?.OnHoldID || null}
          CreatedDate={new Date(item.DateTime).toISOString().split('T')[0]}
          OnPress={() =>
            navigation.navigate('AddItemPickList', {
              ItemData: item,
              VALIDREF: item.OnHoldID,
            })
          }
          GrandTotal={Number(item.Total_Price.toFixed(2))}
          OnPressDelete={() => removePO(item)}
        />
      );
    },
    [navigation, removePO],
  );

  const toggleLookup = useCallback(
    async (value: boolean) => {
      setShowLookup(value);
      setSearchQuery('');
      await getInvoiceHoldID();

      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            textInputRef.current?.blur();
            setTimeout(() => {
              textInputRef.current?.focus();
            }, 50);
          }, 50);
        } else {
          setSearchQuery('');
          Keyboard.dismiss();
          setTimeout(() => {
            textInputRef.current?.blur();
            setTimeout(() => {
              textInputRef.current?.focus();
            }, 50);
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        setSearchQuery('');
        Keyboard.dismiss();
      }
    },
    [getInvoiceHoldID],
  );

  const handleDoneClick = useCallback(async () => {
    setShowLookup(false);
    await getInvoiceHoldID();
    setSearchQuery('');
    Keyboard.dismiss();
    setTimeout(() => {
      textInputRef.current?.blur();
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 50);
    }, 50);
  }, [getInvoiceHoldID]);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      flex: 1,
    },
    contentContainer: {
      paddingHorizontal: wp('3%'),
      paddingTop: hp('1%'),
      flex: 1,
    },
    bottomContainer: {
      paddingHorizontal: wp('4%'),
      backgroundColor: colors.surface,
      elevation: 16,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -6},
      shadowOpacity: isDark ? 0.4 : 0.2,
      shadowRadius: 12,
      borderTopLeftRadius: wp('6%'),
      borderTopRightRadius: wp('6%'),
      paddingVertical: hp('2%'),
      paddingBottom: hp('2.5%'),
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
    listContainer: {
      marginTop: hp('1%'),
      flex: 1,
      paddingBottom: hp('10%'), // Reduced space for FAB
      paddingHorizontal: wp('0.5%'),
    },
    searchContainer: {
      marginTop: hp('1%'),
      marginBottom: hp('0.5%'),
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Header NavName="Pick & Hold" />

        <View style={styles.searchContainer}>
          <AppSearchWIthFilter
            OnSearch={onSearchChange}
            SearchValue={searchQuery}
            Keyboardon={showLookup}
            textInputRef={textInputRef}
            onToggleLookup={toggleLookup}
            IsFilter={false}
            OnSubmitEditing={handleDoneClick}
          />
        </View>

        <View style={styles.listContainer}>
          <DataList
            data={filterPickList}
            renderItem={renderItem}
            loading={loading}
            Hight="100%"
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <FAB
          label="Create New PickList"
          position="bottomRight"
          onPress={() => navigation.navigate('CreatePickList')}
        />
      </View>
    </View>
  );
};

export default ItemPickList;
