import {View, Text, Alert, StyleSheet} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {MaterialColors} from '../../../constants/MaterialColors';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import Header from '../../../components/Inventory/Header';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderItems,
  UpdatePurchaseOrder,
  UpdatePurchaseOrderItems,
  VendorItem,
} from '../../../server/types';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsPurchaseOrderItem,
  applyDefaultsPurchaseOrderItemUpdate,
  applyDefaultsUpdatePurchaseOrder,
} from '../../../Validator/Inventory/Barcode';
import {
  createData,
  GetCount,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  updateData,
} from '../../../utils/PublicHelper';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';

const validationSchema = Yup.object().shape({
  receive: Yup.string().required('Recived is required'),
  damage: Yup.string(),
});

type BarcodeScreenRouteProp = RouteProp<any, 'PurchaseItemView'>;
const PurchaseItemView: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [mainPo, setMainPO] = useState<PurchaseOrder>(route.params?.MainPO);
  const [poItems, setPoItems] = useState<PurchaseOrderItems>(
    route.params?.ItemData,
  );
  const [isDirectPurchase, setIsDirectPurchase] = useState<boolean>(
    route.params?.Direct || false,
  );
  const [received, setReceived] = useState<number>(
    route.params?.ItemData?.Quan_Received,
  );
  const [damaged, setDamaged] = useState<number>(
    route.params?.ItemData?.Quan_Damaged,
  );
  const [adjust, setAdjust] = useState<Inventory>();
  const [vendorItem, setVendorItem] = useState<VendorItem[]>(
    route.params?.VendorItem,
  );
  const [exists, setExists] = useState<boolean>(false);

  useFocusEffect(
    useCallback(() => {
      getUniue();
    }, []),
  );

  const getUniue = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getpounique/:PO_Number',
      setMainPO,
      {PO_Number: route.params?.MainPO?.PO_Number || mainPo.PO_Number},
      true,
    );
  };

  const initialValues = {
    receive: received || '',
    damage: damaged || '',
  };

  const updatePurchase = async (Values: any) => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    try {
      if (poItems.Quan_Ordered === undefined) {
        const checkCaseQty =
          vendorItem[0].NumPerVenCase === 0 ||
          vendorItem[0].NumPerVenCase === undefined
            ? Number(Values.receive)
            : Number(vendorItem[0].NumPerVenCase) * Number(Values.receive);
        const checkNumCase =
          vendorItem[0].NumPerVenCase === 0 ||
          vendorItem[0].NumPerVenCase === undefined
            ? 0
            : vendorItem[0].NumPerVenCase;

        const poItmes: Partial<PurchaseOrderItems> = {
          PO_Number: mainPo.PO_Number,
          ItemNum: vendorItem[0].ItemNum,
          Quan_Ordered: checkCaseQty,
          CostPer: vendorItem[0].CostPer,
          Vendor_Part_Number: vendorItem[0].Vendor_Part_Num || '',
          CasePack:
            vendorItem[0].NumPerVenCase === 0 ||
            vendorItem[0].NumPerVenCase === undefined
              ? 0
              : Number(Values.receive),
          Store_ID: ValidStore,
          destStore_ID: ValidStore,
          NumberPerCase: checkNumCase,
        };

        const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);
        const createResult = await createData<PurchaseOrderItems>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createpodetails',
        });
        if (createResult) {
          const TotalCost =
            Number(checkCaseQty) * Number(vendorItem[0]?.CostPer);
          updatePO(
            'O',
            Number(mainPo.Total_Cost_Received) + Number(TotalCost),
            Number(mainPo.ExpectedAmountToReceive) + Number(checkCaseQty),
            true,
            Number(Values.damage),
          );
        }
      } else {
        const validationHigh = Number(Values.receive);
        if (
          validationHigh > poItems.Quan_Ordered ||
          Number(Values.receive) > poItems.Quan_Ordered ||
          Number(Values.damage) > poItems.Quan_Ordered
        ) {
          Alert.alert(
            `Enter Correct Value You Orderd: ${poItems.Quan_Ordered}`,
          );
        } else {
          const HighDamage =
            Number(Values.damage) > Number(Values.receive) ? true : false;
          if (HighDamage) {
            Alert.alert(
              `Entered damaged Value Higher than Recive ${Values.receive}`,
            );
          } else {
            const poItmes: Partial<UpdatePurchaseOrderItems> = {
              PO_Number: poItems.PO_Number,
              ItemNum: poItems.ItemNum,
              Quan_Ordered: poItems.Quan_Ordered,
              CostPer: poItems.CostPer,
              Vendor_Part_Number: poItems.Vendor_Part_Number,
              CasePack: poItems.CasePack,
              Store_ID: poItems.Store_ID,
              destStore_ID: poItems.destStore_ID,
              NumberPerCase: poItems.NumberPerCase,
              Quan_Received: Values.receive,
              Quan_Damaged: Values.damage,
            };

            const recivedamagecal =
              Number(Values.receive) - Number(Values.damage);
            const stateCal = received - damaged;
            const updateRecive = recivedamagecal - stateCal;
            const updateCost = Number(poItems.CostPer) * Number(updateRecive);
            const balanceExpect =
              Number(mainPo.ExpectedAmountToReceive) - Number(Values.receive);

            // Apply default values to the invoice item
            const applyDefault = applyDefaultsPurchaseOrderItemUpdate(poItmes);

            // Create the data
            const updateResult = await updateData<UpdatePurchaseOrderItems>({
              baseURL: (await getInventoryPort()).toString(),
              data: applyDefault,
              endpoint: '/updatepodetails',
            });
            if (updateResult) {
              if (received > 0) {
                if (Number(Values.receive) > Number(received)) {
                  const calNewRecived =
                    Number(Values.receive) - Number(received); // 6 - 5 = 1
                  updatePO(
                    'O',
                    Number(mainPo.Total_Cost_Received) + Number(updateCost),
                    Number(mainPo.ExpectedAmountToReceive) -
                      Number(calNewRecived),
                    false,
                    Number(Values.damage),
                  );
                } else {
                  if (Number(Values.receive) < Number(received)) {
                    const calNewRecived =
                      Number(received) - Number(Values.receive);
                    updatePO(
                      'O',
                      Number(mainPo.Total_Cost_Received) + Number(updateCost),
                      Number(mainPo.ExpectedAmountToReceive) +
                        Number(calNewRecived),
                      false,
                      Number(Values.damage),
                    );
                  } else {
                    updatePO(
                      'O',
                      Number(mainPo.Total_Cost_Received) + Number(updateCost),
                      mainPo.ExpectedAmountToReceive,
                      false,
                      Number(Values.damage),
                    );
                  }
                }
              } else {
                updatePO(
                  'O',
                  Number(mainPo.Total_Cost_Received) + Number(updateCost),
                  balanceExpect,
                  false,
                  Number(Values.damage),
                );
              }
              setReceived(Values.receive);
              setDamaged(Values.damage);
              GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/getpounique/:PO_Number',
                setMainPO,
                {
                  PO_Number:
                    route.params?.MainPO?.PO_Number || mainPo.PO_Number,
                },
                true,
              );

              const getInventory = await GetItemsParamsNoFilter(
                (await getInventoryPort()).toString(),
                '/inventory/:ItemNum',
                setAdjust,
                {ItemNum: poItems.ItemNum},
              );

              const storeId = await AsyncStorage.getItem('STOREID');
              const ValidStore = storeId === null ? '1001' : storeId;
              const CashierID = await AsyncStorage.getItem('SWIPEID');
              const ValideCashier = CashierID === null ? '100101' : CashierID;
              //update Stock
              const adjustStock =
                Number(recivedamagecal) + Number(getInventory[0].In_Stock);
              const inventoryData: Partial<Inventory> = {
                ItemNum: getInventory[0]?.ItemNum,
                ItemName: getInventory[0]?.ItemName,
                Dept_ID: getInventory[0]?.Dept_ID,
                Cost: getInventory[0]?.Cost,
                Price: getInventory[0]?.Price,
                Retail_Price: getInventory[0]?.Retail_Price,
                In_Stock: adjustStock,
                Date_Created: getInventory[0]?.Date_Created,
                Last_Sold: getInventory[0]?.Last_Sold,
                Location: getInventory[0]?.Location,
                Vendor_Number: getInventory[0]?.Vendor_Number,
                Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
                Reorder_Level: getInventory[0]?.Reorder_Level,
                Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
                ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
                Unit_Size: getInventory[0]?.Unit_Size,
                Unit_Type: getInventory[0]?.Unit_Type,
                FoodStampable: getInventory[0]?.FoodStampable,
                Tax_1: getInventory[0]?.Tax_1[0],
                Tax_2: getInventory[0]?.Tax_2[0],
                Tax_3: getInventory[0]?.Tax_3[0],
                Tax_4: getInventory[0]?.Tax_4[0],
                Tax_5: getInventory[0]?.Tax_5[0],
                Tax_6: getInventory[0]?.Tax_6[0],
                Check_ID: getInventory[0]?.Check_ID,
                Check_ID2: getInventory[0]?.Check_ID2,
                Store_ID: getInventory[0]?.Store_ID,
                ItemName_Extra: getInventory[0]?.ItemName_Extra,
              };
              const applyDefault = applyDefaults(inventoryData);

              const result = await updateData<Inventory>({
                baseURL: (await getInventoryPort()).toString(),
                data: applyDefault,
                endpoint: '/updatebarcode',
              });
              if (result) {
                const inventoryAdjustData: Partial<Inventory_In> = {
                  ItemNum: getInventory[0].ItemNum,
                  Store_ID: ValidStore,
                  Quantity: adjustStock,
                  DateTime: getFormateDate(Date()),
                  Dirty: true,
                  TransType: 'C',
                  Description: 'ITEM CREATION',
                  Cashier_ID: ValideCashier,
                  CostPer: getInventory[0].Cost,
                };
                const applyDefault =
                  applyDefaultsInventoryAdjust(inventoryAdjustData);
                await createData<Inventory_In>({
                  baseURL: (await getInventoryPort()).toString(),
                  data: applyDefault,
                  endpoint: '/createinvetoryin',
                });
              }
              navigation.goBack();
            } else {
              Alert.alert('Error');
            }
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updatePO = async (
    Status?: string,
    TotalCostReceived?: number,
    Expected?: number,
    isCreate?: boolean,
    Damaged?: number,
  ) => {
    const GetDamaged = await GetCount(
      (await getInventoryPort()).toString(),
      '/getPoDamaged',
    );

    const poItmes: Partial<UpdatePurchaseOrder> = {
      PO_Number: mainPo.PO_Number,
      Total_Cost: isCreate ? TotalCostReceived : mainPo.Total_Cost,
      Total_Cost_Received: isCreate
        ? mainPo.Total_Cost_Received
        : TotalCostReceived,
      ExpectedAmountToReceive: Number(GetDamaged),
      Store_ID: mainPo.Store_ID,
      DateTime: mainPo.DateTime,
      Reference: mainPo.Reference,
      Vendor_Number: mainPo.Vendor_Number,
      Ship_Via: mainPo.Ship_Via,
      Status: Status || 'O',
      Cashier_ID: mainPo.Cashier_ID,
      Due_Date: mainPo.Due_Date,
      Last_Modified: mainPo.Last_Modified,
      Cancel_Date: mainPo.DateTime,
    };

    const applyDefault = applyDefaultsUpdatePurchaseOrder(poItmes);
    console.log(applyDefault, 'APPLY DEFUALT');

    const result = await updateData<UpdatePurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/updatePoSummary',
    });
    if (result) {
      if (isCreate) {
        Alert.alert('Purchase Item Added');
      }
    }
  };

  useEffect(() => {
    console.log(poItems.CostPer, 'COST');
  }, []);

  const addListItem = async (Values: any) => {
    if (exists) {
      Alert.alert('Item Already Added!');
    } else {
      if (Number(Values.receive) > 0) {
        try {
          const checkNumCase =
            poItems.NumPerVenCase === 0 || poItems.NumPerVenCase === undefined
              ? 0
              : poItems.NumPerVenCase;

          const getBarcode = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            '/inventory/:ItemNum',
            {ItemNum: poItems.ItemNum || poItems[0].ItemNum},
          );

          const poItmesApply: Partial<PurchaseOrderItems> = {
            PO_Number: 0,
            ItemNum: poItems.ItemNum,
            ItemName: poItems.ItemName,
            Quan_Ordered: Number(Values.receive),
            CostPer:
              poItems.CostPer > 0 ? poItems.CostPer : getBarcode[0]?.Cost,
            Vendor_Part_Number: poItems.Vendor_Part_Number,
            CasePack: Number(Values.receive),
            Store_ID: '1001',
            destStore_ID: '1001',
            NumberPerCase: checkNumCase,
          };

          const applyDefault = applyDefaultsPurchaseOrderItem(poItmesApply);

          const existingData = await AsyncStorage.getItem('DIRECTPO');
          let list = [];

          // If there's existing data, parse it to an array; otherwise, initialize it as an empty array
          if (existingData) {
            try {
              list = JSON.parse(existingData);
              if (!Array.isArray(list)) {
                // If the parsed data is not an array, initialize it as an empty array
                list = [];
              }
            } catch (error) {
              // If JSON parsing fails, initialize list as empty
              console.error('Failed to parse data from AsyncStorage:', error);
              list = [];
            }
          }

          // Push the new item to the list
          list.push(applyDefault);

          // Save the updated list back to AsyncStorage
          await AsyncStorage.setItem('DIRECTPO', JSON.stringify(list));
          setExists(true);

          Alert.alert('Item Added!');
        } catch (error) {
          console.error('Error updating AsyncStorage:', error);
          Alert.alert('An error occurred while adding the item.');
        }
      } else {
        Alert.alert('Please Enter Correct Values!');
      }
    }
  };

  return (
    <View style={styles.container}>
      <Header NavName={poItems.ItemName || poItems[0]?.ItemName} />

      <View style={styles.itemCard}>
        <Text style={styles.itemName}>
          {poItems.ItemName || route.params?.ItemData[0]?.ItemName}
        </Text>

        <View style={styles.statusRow}>
          <Text style={styles.statusText}>Received: {received || 0}</Text>
          <Text style={styles.statusText}>Damaged: {damaged || 0}</Text>
        </View>
        <Text style={styles.orderedText}>
          Ordered: {poItems.Quan_Ordered || 0}
        </Text>
      </View>

      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={values => {
          if (isDirectPurchase) {
            addListItem(values);
          } else {
            updatePurchase(values);
          }
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View style={styles.formContainer}>
            <View style={styles.inputsContainer}>
              <AppTextInput
                PlaceHolder={
                  poItems.Quan_Ordered === undefined ? 'Quantity' : 'Receiving'
                }
                Title={
                  poItems.Quan_Ordered === undefined ? 'Quantity' : 'Received'
                }
                Value={values.receive}
                onChangeText={handleChange('receive')}
                onBlur={handleBlur('receive')}
                error={errors.receive}
                touched={touched.receive}
                isNumeric
              />

              {poItems.Quan_Ordered && (
                <AppTextInput
                  PlaceHolder="Damaged"
                  Title="Damaged"
                  Value={values.damage}
                  onChangeText={handleChange('damage')}
                  onBlur={handleBlur('damage')}
                  error={errors.damage}
                  touched={touched.damage}
                  isNumeric
                />
              )}
            </View>

            <View style={styles.buttonContainer}>
              <AppButton Title={'Save & Close'} OnPress={handleSubmit} />
            </View>
          </View>
        )}
      </Formik>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: MaterialColors.background,
    paddingHorizontal: wp('2.5%'),
  },
  itemCard: {
    backgroundColor: MaterialColors.surface,
    paddingHorizontal: wp('4%'),
    paddingVertical: hp('2%'),
    marginVertical: hp('1.5%'),
    borderRadius: 12,
  },
  itemName: {
    fontFamily: Fonts.OnestBold,
    fontSize: FontSizes.medium,
    color: MaterialColors.text.primary,
    marginBottom: 4,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  statusText: {
    fontFamily: Fonts.OnestBold,
    fontSize: FontSizes.medium,
    color: MaterialColors.text.secondary,
    marginRight: wp('5%'),
  },
  orderedText: {
    fontFamily: Fonts.OnestBold,
    fontSize: FontSizes.medium,
    color: MaterialColors.primary.main,
  },
  formContainer: {
    marginTop: hp('2%'),
  },
  inputsContainer: {
    marginBottom: hp('2%'),
  },
  buttonContainer: {
    marginTop: hp('4%'),
    paddingHorizontal: wp('2%'),
  },
});

export default PurchaseItemView;
