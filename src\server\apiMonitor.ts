// apiMonitor.ts
import { AppState } from 'react-native';

class ApiMonitor {
  private static instance: ApiMonitor;
  private activeCalls: Map<string, number> = new Map();
  private callHistory: Array<{endpoint: string, timestamp: number, screen: string}> = [];
  private currentScreen: string = 'Unknown';
  
  private constructor() {
    // Initialize
  }
  
  public static getInstance(): ApiMonitor {
    if (!ApiMonitor.instance) {
      ApiMonitor.instance = new ApiMonitor();
    }
    return ApiMonitor.instance;
  }
  
  public setCurrentScreen(screenName: string): void {
    this.currentScreen = screenName;
  }
  
  public trackApiCall(endpoint: string): void {
    // Only track when app is active
    if (AppState.currentState !== 'active') return;
    
    // Record this call
    const timestamp = Date.now();
    this.callHistory.push({
      endpoint,
      timestamp,
      screen: this.currentScreen
    });
    
    // Limit history size
    if (this.callHistory.length > 100) {
      this.callHistory.shift();
    }
    
    // Track active calls
    const count = this.activeCalls.get(endpoint) || 0;
    this.activeCalls.set(endpoint, count + 1);
    
    // Log suspicious activity (multiple calls to same endpoint)
    if (count > 2) {
      console.warn(`Multiple calls to ${endpoint} detected while on ${this.currentScreen} screen`);
    }
  }
  
  public trackApiResponse(endpoint: string): void {
    const count = this.activeCalls.get(endpoint) || 0;
    if (count > 0) {
      this.activeCalls.set(endpoint, count - 1);
    }
  }
  
  public getCallHistory(): Array<{endpoint: string, timestamp: number, screen: string}> {
    return [...this.callHistory];
  }
  
  public getActiveCalls(): Map<string, number> {
    return new Map(this.activeCalls);
  }
}

export default ApiMonitor.getInstance();