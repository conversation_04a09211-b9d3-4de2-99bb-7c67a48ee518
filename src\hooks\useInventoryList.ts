import {useState, useEffect, useCallback} from 'react';
import {Inventory_Filter} from '../server/types';
import {GetAllItemsWithFilter} from '../utils/PublicHelper';
import {getInventoryPort} from '../server/InstanceTypes';

export interface UseInventoryListProps {
  onDataLoaded?: (data: Inventory_Filter[]) => void;
  itemsPerPage?: number;
  debounceDelay?: number;
}

export interface UseInventoryListReturn {
  // Data state
  masterData: Inventory_Filter[];
  filteredData: Inventory_Filter[];
  displayData: Inventory_Filter[];

  // Loading states
  loading: boolean;
  initialLoading: boolean;
  refreshing: boolean;

  // Search state
  searchQuery: string;
  debouncedQuery: string;

  // Pagination state
  page: number;

  // Filter state
  selectedDepartment: string;
  selectedVendor: string;
  selectedBrand: string;
  selectedSubCategory: string;
  isEnableFilter: boolean;

  // Actions
  setSearchQuery: (query: string) => void;
  setSelectedDepartment: (dept: string) => void;
  setSelectedVendor: (vendor: string) => void;
  setSelectedBrand: (brand: string) => void;
  setSelectedSubCategory: (subCategory: string) => void;
  getInventoryData: () => Promise<void>;
  handleLoadMore: () => void;
  handleRefresh: () => Promise<void>;
  applyPagination: (data: Inventory_Filter[], currentPage: number) => void;
  resetFilters: () => void;
  performSearch: (text: string, data: Inventory_Filter[]) => void;
}

export const useInventoryList = ({
  onDataLoaded,
  itemsPerPage = 20,
  debounceDelay = 300,
}: UseInventoryListProps = {}): UseInventoryListReturn => {
  // State for all data and filtered data
  const [masterData, setMasterData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');

  // Pagination state
  const [page, setPage] = useState<number>(1);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Filter state
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);

  // Pagination function
  const applyPagination = useCallback(
    (data: Inventory_Filter[], currentPage: number) => {
      const endIndex = currentPage * itemsPerPage;
      const paginatedItems = data.slice(0, endIndex);
      setDisplayData(paginatedItems);
    },
    [itemsPerPage],
  );

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, debounceDelay);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery, debounceDelay]);

  // Determine if filters are active
  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply filters and search
  useEffect(() => {
    // Skip if masterData isn't loaded yet
    if (masterData.length === 0) {
      return;
    }

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    setFilteredData(filtered);
    applyPagination(filtered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
    applyPagination,
  ]);

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (page * itemsPerPage < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, itemsPerPage, applyPagination]);

  // Apply pagination when page changes
  useEffect(() => {
    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData, applyPagination]);

  // Fetch inventory data
  const getInventoryData = useCallback(async () => {
    try {
      setLoading(true);
      const port = await getInventoryPort();
      GetAllItemsWithFilter(
        port.toString(),
        '/getInventoryFilter',
        (data: unknown) => {
          const inventoryData = data as Inventory_Filter[];
          setMasterData(inventoryData);
          setFilteredData(inventoryData);
          applyPagination(inventoryData, 1);
          setInitialLoading(false);
          setLoading(false);
          onDataLoaded?.(inventoryData);
        },
        // We'll handle displayData ourselves
        () => {},
        setLoading,
        false,
      );
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  }, [applyPagination, onDataLoaded]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await getInventoryData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  }, [getInventoryData]);

  // Reset all filters
  const resetFilters = useCallback(() => {
    setSelectedDepartment('');
    setSelectedVendor('');
    setSelectedBrand('');
    setSelectedSubCategory('');
    setSearchQuery('');
    setPage(1);
  }, []);

  // Perform search functionality
  const performSearch = useCallback(
    (text: string, data: Inventory_Filter[]) => {
      if (text.trim() === '') {
        setSearchQuery('');
        setFilteredData(masterData);
        return;
      }

      const searchTerm = text.toLowerCase();
      const searchResults = data.filter(
        item =>
          (item.ItemName?.toLowerCase() || '').includes(searchTerm) ||
          (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm),
      );

      setDisplayData(searchResults);
    },
    [masterData],
  );

  return {
    // Data state
    masterData,
    filteredData,
    displayData,

    // Loading states
    loading,
    initialLoading,
    refreshing,

    // Search state
    searchQuery,
    debouncedQuery,

    // Pagination state
    page,

    // Filter state
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    isEnableFilter,

    // Actions
    setSearchQuery,
    setSelectedDepartment,
    setSelectedVendor,
    setSelectedBrand,
    setSelectedSubCategory,
    getInventoryData,
    handleLoadMore,
    handleRefresh,
    applyPagination,
    resetFilters,
    performSearch,
  };
};
