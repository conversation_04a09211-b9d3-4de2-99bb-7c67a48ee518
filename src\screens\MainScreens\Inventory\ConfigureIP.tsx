import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import React, {useState} from 'react';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import AppButton from '../../../components/Inventory/AppButton';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import {GetAllItemsNoProps} from '../../../utils/PublicHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Fonts} from '../../../styles/fonts';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../../constants/MaterialColors';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import Header from '../../../components/Inventory/Header';
import {getLotteryPort} from '../../../server/InstanceTypes';

// Define NavProps for navigation
type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const Configure: React.FC<NavProps> = ({navigation}) => {
  const [ip, setIp] = useState<string>('');
  const [storeId, setStoreId] = useState<string>('');
  const [stationID, setstationID] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Function to validate the IP address using regex
  const validateIP = (ip: string): boolean => {
    const ipv4Regex =
      /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Regex.test(ip);
  };

  // Handle the IP validation when the input changes
  const handleIPChange = (ip: string) => {
    setIp(ip);
    //setIsValid(validateIP(ip)); // Validate the IP whenever it changes
  };

  // Function to create organization info
  const createOrganizationInfo = async () => {
    try {
      // Get data from AsyncStorage
      const userDataString = await AsyncStorage.getItem('userData');
      const organizationDataString = await AsyncStorage.getItem(
        'organizationData',
      );

      if (!userDataString || !organizationDataString) {
        console.log('User data or organization data not found in AsyncStorage');
        return false;
      }

      const userData = JSON.parse(userDataString);
      const organizationData = JSON.parse(organizationDataString);

      const orgData = {
        Org_ID: userData.id.toString(),
        Org_Name: organizationData.organizationName,
        Org_Email: userData.email,
        Org_Phone: '1234567890',
        Org_City: userData.address.city,
        Org_State: userData.address.state,
        Org_ZipCode: userData.address.zipCode,
      };

      const response = await fetch(
        `${await getLotteryPort()}/organization-info`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(orgData),
        },
      );

      if (response.ok) {
        const result = await response.json();
        console.log('Organization info created successfully:', result);
        return true;
      } else {
        console.error(
          'Failed to create organization info:',
          await response.text(),
        );
        return false;
      }
    } catch (error) {
      console.error('Error creating organization info:', error);
      return false;
    }
  };

  const configuredIP = async () => {
    setLoading(true);
    const result = await GetAllItemsNoProps(
      `http://${ip}:8090/api`,
      '/GetDepartments',
    );

    if (result === undefined) {
      setLoading(false);
      Alert.alert(
        'Error',
        'Unable to connect to the server. Please check your IP address.',
      );
    } else {
      await AsyncStorage.setItem('LOCALIP', ip);
      await AsyncStorage.setItem('STOREID', storeId);
      await AsyncStorage.setItem('STATIONID', stationID);

      // Create organization info after successful IP configuration
      await createOrganizationInfo();

      setLoading(false);
      Alert.alert('Success', 'Connection successful!');
      navigation.navigate('Home');
    }
  };

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderBottomWidth: 1,
      elevation: 2,
    },
    backButton: {
      padding: 6,
      borderRadius: 20,
    },
    headerTitle: {
      flex: 1,
      fontSize: 14,
      fontWeight: '600',
      textAlign: 'center',
    },
    headerRight: {
      width: 20,
    },
    contentContainer: {
      flex: 1,
      padding: 24,
    },
    formContainer: {
      width: '100%',
    },
    formTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 12,
      marginBottom: 24,
      fontFamily: Fonts.OnestMedium,
    },
    labelContainer: {
      marginBottom: 3,
    },
    inputLabel: {
      fontSize: 12,
      fontWeight: '500',
      fontFamily: Fonts.OnestMedium,
    },
    inputContainer: {
      marginBottom: 5,
      borderRadius: 12,
      overflow: 'hidden',
    },
    loader: {
      marginVertical: 20,
    },
    button: {
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 16,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    primaryButton: {
      // backgroundColor removed - now dynamic
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 12,
      fontWeight: '600',
      fontFamily: Fonts.OnestBold,
    },
  });

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: colors.background}]}>
      {/* <StatusBar
        backgroundColor={colors.surface}
        barStyle={isDark ? 'light-content' : 'dark-content'}
      /> */}

      {/* Header with back button and title */}
      <Header
        NavName="Service Conifiguration"
        isProvid={true}
        Onpress={() => navigation.navigate('LoginScreen')}
      />
      <View style={styles.contentContainer}>
        <View style={styles.formContainer}>
          <Text style={[styles.formTitle, {color: colors.text}]}>
            Configure IP
          </Text>

          <Text style={[styles.subtitle, {color: colors.textSecondary}]}>
            Set your Local IP for connecting to the database
          </Text>

          <View style={styles.labelContainer}>
            <Text style={[styles.inputLabel, {color: colors.text}]}>
              Local IP
            </Text>
          </View>
          <View style={styles.inputContainer}>
            <AppTextInput
              PlaceHolder="Enter Your Local IP"
              onChangeText={handleIPChange}
              Value={ip}
            />
          </View>

          <View style={styles.labelContainer}>
            <Text style={[styles.inputLabel, {color: colors.text}]}>
              Store ID
            </Text>
          </View>
          <View style={styles.inputContainer}>
            <AppTextInput
              PlaceHolder="Enter Your Store ID"
              onChangeText={store => setStoreId(store)}
              Value={storeId}
            />
          </View>

          <View style={styles.labelContainer}>
            <Text style={[styles.inputLabel, {color: colors.text}]}>
              Station ID
            </Text>
          </View>
          <View style={styles.inputContainer}>
            <AppTextInput
              PlaceHolder="Enter Your Station ID"
              onChangeText={store => setstationID(store)}
              Value={stationID}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              {backgroundColor: colors.primary, shadowColor: colors.shadow},
              loading && styles.disabledButton,
            ]}
            onPress={() => configuredIP()}
            disabled={loading}>
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Configure Connection</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Configure;
