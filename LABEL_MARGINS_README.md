# Label Margins Feature

## Overview

This feature allows users to customize the margins (top, right, bottom, left) for label printing. The margins are applied to all label templates and help users fine-tune the positioning of content on their labels.

## Features Implemented

### 1. Margin Configuration Screen

- **Location**: `src/screens/SubScreens/PrintLabel/LabelMarginConfig.tsx`
- **Features**:
  - Input fields for top, right, bottom, and left margins
  - Real-time validation (margins must be between -50 and 100 units)
  - Preview of current settings
  - Save and reset functionality
  - Error handling and user feedback

### 2. Margin Storage System

- **Location**: `src/utils/marginHelper.ts`
- **Features**:
  - Save/load margins from AsyncStorage
  - Default margin values (all set to 0)
  - Validation functions
  - Coordinate transformation utilities

### 3. Updated Printer Functions

- **Location**: `src/utils/PrinterHelper.ts`
- **Changes**:
  - Added margin support to `sendPrintLabelOne`, `sendPrintLabelTwo`, and `sendPrintLabelThree`
  - All EPL coordinates are now adjusted based on user-defined margins
  - Margins are loaded automatically when printing

### 4. Navigation Integration

- **Print Label Screen**: Added margin configuration button in the header
- **Settings Screen**: Added "Label Margins" option in settings menu
- **Navigation**: Added route to `LabelMarginConfig` screen in `App.tsx`

## How to Use

### Accessing Margin Configuration

1. **From Print Label Screen**: Tap the margin icon (format-align-justify) in the header
2. **From Settings**: Go to Settings → Label Margins

### Configuring Margins

1. Open the Label Margins configuration screen
2. Enter values for each margin:
   - **Top**: Moves content down (positive) or up (negative)
   - **Right**: Moves content left (positive) or right (negative)
   - **Bottom**: Moves content up (positive) or down (negative)
   - **Left**: Moves content right (positive) or left (negative)
3. Values must be between -50 and 100 units
4. Tap "Save Margins" to apply changes
5. Use "Reset" to return all margins to 0

### Margin Units

- Margins are measured in printer units (typically dots)
- Positive values move content inward from the edge
- Negative values move content outward (may cause clipping)

## Technical Implementation

### Type Definitions

```typescript
interface LabelMargins {
  top: number;
  right: number;
  bottom: number;
  left: number;
}
```

### Storage Key

- Margins are stored in AsyncStorage under the key: `'label_margins'`

### Coordinate Transformation

The `applyMargins` function transforms original coordinates:

```typescript
const adjustedCoords = applyMargins(originalX, originalY, margins);
// Result: { x: originalX + margins.left, y: originalY + margins.top }
```

## Future Enhancements

### Remaining Print Functions

The following print functions still need margin support:

- `sendPrintLabelFour`
- `sendPrintLabelFive`
- `sendPrintLabelSix`
- `sendPrintLabelBulkPricing`
- `sendPrintLabelSalePricing`
- `sendPrintLabelMixAndMatch`
- `sendPrintLabelRound`

### Suggested Improvements

1. **Per-Template Margins**: Allow different margins for each label template
2. **Visual Preview**: Show a visual preview of how margins affect label layout
3. **Import/Export**: Allow users to backup and restore margin settings
4. **Preset Margins**: Provide common margin presets for different printer types
5. **Advanced Validation**: Check margins against actual label dimensions

## Files Modified/Created

### New Files

- `src/screens/SubScreens/PrintLabel/LabelMarginConfig.tsx`
- `src/utils/marginHelper.ts`
- `src/Types/PrinterTypes.ts` (consolidated)

### Modified Files

- `src/utils/PrinterHelper.ts` (added margin support to first 3 print functions)
- `src/screens/MainScreens/PrintLabel/PrintLabel.tsx` (added margin config button)
- `src/screens/MainScreens/Inventory/Settings.tsx` (added settings option)
- `App.tsx` (added navigation route)

### Removed Files

- `src/Types/PrinterTypes.ts` (duplicate, consolidated into `src/types/PrinterTypes.ts`)

## Testing Recommendations

1. **Basic Functionality**:

   - Test saving and loading margins
   - Verify margins are applied to printed labels
   - Test validation (values outside -50 to 100 range)

2. **Edge Cases**:

   - Test with extreme margin values
   - Test with negative margins
   - Test reset functionality

3. **Integration**:

   - Test navigation from both Print Label screen and Settings
   - Verify margins persist across app restarts
   - Test with different label templates

4. **User Experience**:
   - Test error messages and validation feedback
   - Verify button states and loading indicators
   - Test on different screen sizes
