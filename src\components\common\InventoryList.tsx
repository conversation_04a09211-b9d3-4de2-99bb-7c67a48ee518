import React, {memo, useCallback, useMemo, RefObject} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {Inventory_Filter} from '../../server/types';
import {UseInventoryListReturn} from '../../hooks/useInventoryList';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import LottieView from 'lottie-react-native';
import {Fonts} from '../../styles/fonts';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

// Font sizes consistent with the app
const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

export interface InventoryListProps {
  // Data and state from the hook
  listData: UseInventoryListReturn;

  // Interaction handlers
  onItemPress: (item: Inventory_Filter) => void;
  onItemLongPress?: (item: Inventory_Filter) => void;

  // Customization props
  itemHeight?: number;
  customItemRenderer?: (
    item: Inventory_Filter,
    onPress: (item: Inventory_Filter) => void,
    onLongPress?: (item: Inventory_Filter) => void,
  ) => React.ReactElement;
  customEmptyComponent?: (searchQuery: string) => React.ReactElement;
  customLoadingComponent?: () => React.ReactElement;
  customFooterComponent?: (loading: boolean) => React.ReactElement;

  // Style customization
  containerStyle?: ViewStyle;
  listContentStyle?: ViewStyle;
  itemCardStyle?: ViewStyle;
  itemTextStyle?: TextStyle;
  priceTextStyle?: TextStyle;

  // List configuration
  flatListRef?: RefObject<FlatList>;
  numColumns?: number;
  horizontal?: boolean;
  showsVerticalScrollIndicator?: boolean;
  showsHorizontalScrollIndicator?: boolean;

  // Performance optimization
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
  windowSize?: number;
  removeClippedSubviews?: boolean;
  updateCellsBatchingPeriod?: number;

  // Additional props
  testID?: string;
}

// Memoized Item component for optimal rendering performance
const DefaultInventoryItem = memo(
  ({
    item,
    onPress,
    onLongPress,
    itemCardStyle,
    itemTextStyle,
    priceTextStyle,
  }: {
    item: Inventory_Filter;
    onPress: (item: Inventory_Filter) => void;
    onLongPress?: (item: Inventory_Filter) => void;
    itemCardStyle?: ViewStyle;
    itemTextStyle?: TextStyle;
    priceTextStyle?: TextStyle;
  }) => {
    const colors = useThemeColors();
    const {isDark} = useTheme();

    const handlePress = useCallback(() => onPress(item), [item, onPress]);
    const handleLongPress = useCallback(
      () => onLongPress?.(item),
      [item, onLongPress],
    );

    const defaultStyles = useMemo(
      () =>
        StyleSheet.create({
          itemCard: {
            backgroundColor: colors.card,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: wp('4%'),
            paddingVertical: hp('1.8%'),
            marginBottom: hp('1.2%'),
            borderRadius: 12,
            borderLeftColor: colors.primary,
            shadowColor: colors.shadow,
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: isDark ? 0.3 : 0.1,
            shadowRadius: 4,
            elevation: 3,
            ...itemCardStyle,
          },
          itemDetails: {
            flex: 1,
            gap: hp('0.5%'),
          },
          itemName: {
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.medium,
            color: colors.text,
            ...itemTextStyle,
          },
          itemCost: {
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.small,
            color: colors.textSecondary,
            ...itemTextStyle,
          },
          priceContainer: {
            backgroundColor: isDark ? colors.surface : colors.primary + '20', // 20% opacity for light mode
            paddingVertical: hp('0.8%'),
            paddingHorizontal: wp('2%'),
            borderRadius: 8,
            justifyContent: 'center',
            alignItems: 'center',
          },
          priceText: {
            fontFamily: Fonts.OnestBold,
            fontSize: FontSizes.large,
            color: colors.primary,
            ...priceTextStyle,
          },
        }),
      [colors, isDark, itemCardStyle, itemTextStyle, priceTextStyle],
    );

    return (
      <TouchableOpacity
        style={defaultStyles.itemCard}
        onPress={handlePress}
        onLongPress={onLongPress ? handleLongPress : undefined}
        activeOpacity={0.8}>
        <View style={defaultStyles.itemDetails}>
          <Text style={defaultStyles.itemName}>{item.ItemName}</Text>
          <Text style={defaultStyles.itemCost}>{`Cost: ${item.Cost?.toFixed(
            2,
          )}`}</Text>
        </View>
        <View style={defaultStyles.priceContainer}>
          <Text style={defaultStyles.priceText}>${item.Price}</Text>
        </View>
      </TouchableOpacity>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if the item ID changes
    return prevProps.item.ItemNum === nextProps.item.ItemNum;
  },
);

// Default Empty list component
const DefaultEmptyListComponent = memo(
  ({searchQuery}: {searchQuery: string}) => {
    const colors = useThemeColors();

    const styles = useMemo(
      () =>
        StyleSheet.create({
          emptyContainer: {
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: hp('10%'),
          },
          animationContainer: {
            borderRadius: 20,
            backgroundColor: 'transparent',
          },
          lottie: {
            height: 200,
            width: 200,
            backgroundColor: 'transparent',
          },
          emptyText: {
            fontSize: 16,
            color: colors.textSecondary,
            textAlign: 'center',
            marginTop: 20,
            fontFamily: Fonts.OnestMedium,
          },
        }),
      [colors],
    );

    return (
      <View style={styles.emptyContainer}>
        <View style={styles.animationContainer}>
          <LottieView
            style={styles.lottie}
            source={require('../../assets/Lotties/Nodata.json')}
            autoPlay
            loop
          />
        </View>
        <Text style={styles.emptyText}>
          {searchQuery
            ? 'No matching items found'
            : 'No inventory items available'}
        </Text>
      </View>
    );
  },
);

// Default Footer loading component
const DefaultListFooter = memo(({loading}: {loading: boolean}) => {
  const colors = useThemeColors();

  const styles = useMemo(
    () =>
      StyleSheet.create({
        footer: {
          padding: 15,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        },
        footerText: {
          marginLeft: 10,
          fontFamily: Fonts.OnestMedium,
          color: colors.textSecondary,
        },
      }),
    [colors],
  );

  if (!loading) {
    return null;
  }

  return (
    <View style={styles.footer}>
      <ActivityIndicator size="small" color={colors.primary} />
      <Text style={styles.footerText}>Loading more items...</Text>
    </View>
  );
});

// Default Loading component
const DefaultLoadingComponent = memo(() => {
  const colors = useThemeColors();

  const styles = useMemo(
    () =>
      StyleSheet.create({
        centered: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: hp('10%'),
        },
      }),
    [],
  );

  return (
    <View style={styles.centered}>
      <ActivityIndicator size="large" color={colors.primary} />
    </View>
  );
});

export const InventoryList: React.FC<InventoryListProps> = ({
  listData,
  onItemPress,
  onItemLongPress,
  itemHeight = 88,
  customItemRenderer,
  customEmptyComponent,
  customLoadingComponent,
  customFooterComponent,
  containerStyle,
  listContentStyle,
  itemCardStyle,
  itemTextStyle,
  priceTextStyle,
  flatListRef,
  numColumns = 1,
  horizontal = false,
  showsVerticalScrollIndicator = false,
  showsHorizontalScrollIndicator = false,
  initialNumToRender = 10,
  maxToRenderPerBatch = 10,
  windowSize = 10,
  removeClippedSubviews = true,
  updateCellsBatchingPeriod = 75,
  testID,
}) => {
  const defaultStyles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flex: 1,
          paddingHorizontal: wp('2.5%'),
          ...containerStyle,
        },
        listContent: {
          paddingBottom: hp('2%'),
          ...listContentStyle,
        },
      }),
    [containerStyle, listContentStyle],
  );

  // Optimized rendering components
  const renderItem = useCallback(
    ({item}: {item: Inventory_Filter}) => {
      if (customItemRenderer) {
        return customItemRenderer(item, onItemPress, onItemLongPress);
      }

      return (
        <DefaultInventoryItem
          item={item}
          onPress={onItemPress}
          onLongPress={onItemLongPress}
          itemCardStyle={itemCardStyle}
          itemTextStyle={itemTextStyle}
          priceTextStyle={priceTextStyle}
        />
      );
    },
    [
      onItemPress,
      onItemLongPress,
      customItemRenderer,
      itemCardStyle,
      itemTextStyle,
      priceTextStyle,
    ],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum?.toString() || '',
    [],
  );

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }),
    [itemHeight],
  );

  // Empty component
  const EmptyComponent = useCallback(() => {
    if (customEmptyComponent) {
      return customEmptyComponent(listData.searchQuery);
    }
    return <DefaultEmptyListComponent searchQuery={listData.searchQuery} />;
  }, [customEmptyComponent, listData.searchQuery]);

  // Footer component
  const FooterComponent = useCallback(() => {
    if (customFooterComponent) {
      return customFooterComponent(
        listData.loading && !listData.initialLoading,
      );
    }
    return (
      <DefaultListFooter
        loading={listData.loading && !listData.initialLoading}
      />
    );
  }, [customFooterComponent, listData.loading, listData.initialLoading]);

  // Loading component
  const LoadingComponent = useCallback(() => {
    if (customLoadingComponent) {
      return customLoadingComponent();
    }
    return <DefaultLoadingComponent />;
  }, [customLoadingComponent]);

  if (listData.initialLoading) {
    return <LoadingComponent />;
  }

  return (
    <View style={defaultStyles.container} testID={testID}>
      <FlatList
        ref={flatListRef}
        data={listData.displayData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={defaultStyles.listContent}
        onEndReached={listData.handleLoadMore}
        onEndReachedThreshold={0.5}
        refreshing={listData.refreshing}
        onRefresh={listData.handleRefresh}
        getItemLayout={getItemLayout}
        ListEmptyComponent={EmptyComponent}
        ListFooterComponent={FooterComponent}
        initialNumToRender={initialNumToRender}
        maxToRenderPerBatch={maxToRenderPerBatch}
        windowSize={windowSize}
        removeClippedSubviews={removeClippedSubviews}
        updateCellsBatchingPeriod={updateCellsBatchingPeriod}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        showsHorizontalScrollIndicator={showsHorizontalScrollIndicator}
        numColumns={numColumns}
        horizontal={horizontal}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
        }}
      />
    </View>
  );
};

export default InventoryList;
