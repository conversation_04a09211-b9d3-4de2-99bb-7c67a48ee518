import {View, Text, Alert} from 'react-native';
import React, {useCallback} from 'react';
import Header from '../../../components/Inventory/Header';
import AppButton from '../../../components/Inventory/AppButton';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {PurchaseOrderItems} from '../../../server/types';
import DataList from '../../../components/Inventory/AppList';
import PurchaseOrderItemCart from '../../../components/Inventory/PurchaseOrderItemCart';
import {useFocusEffect} from '@react-navigation/native';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import FAB from '../../../components/common/FAB';
import {useDirectPurchase} from '../../../hooks';
import {useThemeColors} from '../../../Theme/useThemeColors';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const DirectPurchase: React.FC<NavProps> = ({navigation}) => {
  const {
    purchaseItems,
    loading,
    vendorItems,
    vendorItemsFilter,
    removeListItem,
    ScanBarcode,
    VendorItemAdd,
    initializeData,
  } = useDirectPurchase();

  const colors = useThemeColors();

  const renderItem = ({item}: {item: PurchaseOrderItems}) => {
    return (
      <View>
        <PurchaseOrderItemCart
          Name={item?.ItemName}
          Ordered={item?.Quan_Ordered}
          RecivedNow={item?.Quan_Received}
          DamagedNow={item.Quan_Damaged}
          OnPress={() =>
            navigation.navigate('PurchaseItemView', {
              ItemData: item,
            })
          }
          Delete={() => removeListItem(item)}
        />
      </View>
    );
  };

  useFocusEffect(
    useCallback(() => {
      initializeData();
    }, [initializeData]),
  );

  return (
    <View
      style={{
        backgroundColor: colors.background,
        flex: 1,
        justifyContent: 'space-between',
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Header NavName="Direct Purchase" />
        <Text
          style={{
            paddingVertical: hp('2.5%'),
            fontFamily: Fonts.OnestBold,
            fontSize: hp('2.2%'),
            color: colors.primary,
          }}>
          {`Items Added: (${purchaseItems.length})`}
        </Text>

        {false && (
          <AppFocus
            PlaceHolder="Scan Book Number"
            Title="Scan Book Number"
            onChangeText={value => {
              ScanBarcode(value, navigation);
            }}
          />
        )}

        <DataList
          data={purchaseItems}
          loading={loading}
          renderItem={renderItem}
          Hight="70%"
        />
      </View>

      <View
        style={{
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: 0,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1%'),
          backgroundColor: colors.surface,
        }}>
        <View style={{marginVertical: 10}}>
          <AppButton
            Title="Add Items"
            OnPress={() =>
              navigation.navigate('DirectPurchaseVendorItemsScreen', {
                vendorItems: vendorItems,
                vendorItemsFilter: vendorItemsFilter,
                VendorItemAdd: VendorItemAdd,
              })
            }
          />
        </View>
        <View style={{marginVertical: 10}}>
          <FAB
            label="Save & Close"
            position="bottomRight"
            onPress={() => {
              if (purchaseItems.length > 0) {
                navigation.navigate('PurchaseOrderCreate', {
                  ItemData: purchaseItems,
                  IsDirect: true,
                });
              } else {
                Alert.alert('Cant Create PO, Items Empty!');
              }
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default DirectPurchase;
