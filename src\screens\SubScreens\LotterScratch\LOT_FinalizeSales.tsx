import {View, Text, ScrollView, StyleSheet} from 'react-native';
import React, {useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Game_Details,
  Shift_Details,
} from '../../../Types/Lottery/Lottery_Types';
import {
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import {RouteProp} from '@react-navigation/native';
import {MaterialColors} from '../../../constants/MaterialColors';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'FinalizePage'>;

const LOT_FinalizeSales: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [shiftGameData, setShiftGameData] = useState<Game_Details[]>([]);
  const [shiftData, setShiftData] = useState<Shift_Details>(
    route.params?.ItemData,
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [store, setStore] = useState<string>('');
  const [cashier, setCashier] = useState<string>('');
  const [empName, setEmpName] = useState<string>('');

  useEffect(() => {
    getShiftGameDetails();
  }, []);

  const getShiftGameDetails = async () => {
    const data = await GetItemsParamsNoFilter<Game_Details[]>(
      (await getLotteryPort()).toString(),
      '/GetAllShiftAllGames/:Shift_ID',
      setShiftGameData,
      {Shift_ID: shiftData.Shift_ID},
      false,
    );

    const getBarcode = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/loginEmployee/:Cashier_ID',
      {Cashier_ID: shiftData.Shift_Cashier_ID},
    );
    console.log('GET CASHIER', getBarcode);

    if (Array.isArray(getBarcode) && getBarcode.length > 0) {
      setEmpName(getBarcode[0].EmpName || 'N/A');
    } else {
      console.error('Employee not found');
    }
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const splitCashier = splitCashierId(ValidStore, shiftData.Shift_Cashier_ID);
    if (shiftData.Shift_Cashier_ID.includes(ValidStore)) {
      if (splitCashier) {
        setStore(splitCashier.storeId);
        setCashier(splitCashier.cashierNumber);
      } else {
        console.error('Invalid Cashier ID');
      }
    } else {
      setStore(ValidStore);
      setCashier(shiftData.Shift_Cashier_ID);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const formattedDate =
      `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()} ` +
      `${date.toLocaleTimeString('en-US')}`;
    return formattedDate;
  };

  function splitCashierId(storeId: string, cashierId: string) {
    const storeIdStr = storeId.toString();
    const cashierIdStr = cashierId.toString();

    if (cashierIdStr.startsWith(storeIdStr)) {
      const cashierNumber = cashierIdStr.slice(storeIdStr.length);
      return {
        storeId: storeIdStr,
        cashierNumber: cashierNumber,
      };
    } else {
      return null; // or throw an error if preferred
    }
  }

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('4%'),
    },
    shiftInfoContainer: {
      marginTop: hp('2%'),
      marginBottom: hp('2%'),
    },
    shiftInfoCard: {
      backgroundColor: colors.card,
      borderRadius: 12,
      padding: wp('4%'),
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    shiftInfoTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.primary,
      marginBottom: hp('1%'),
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: hp('0.8%'),
    },
    infoLabel: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
      width: wp('25%'),
    },
    infoValue: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.text,
      flex: 1,
    },
    sectionTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.text,
      marginVertical: hp('1.5%'),
    },
    scrollContainer: {
      flex: 1,
    },
    gameCard: {
      backgroundColor: colors.card,
      borderRadius: 12,
      marginBottom: hp('2%'),
      overflow: 'hidden',
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    gameHeaderContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: wp('4%'),
      backgroundColor: colors.surface,
    },
    gameHeaderItem: {
      alignItems: 'flex-start',
    },
    gameHeaderLabel: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
      color: colors.textSecondary,
      marginBottom: hp('0.2%'),
    },
    gameHeaderValue: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      width: '100%',
    },
    detailsSection: {
      paddingHorizontal: wp('4%'),
      paddingVertical: hp('1%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: hp('0.3%'),
    },
    detailLabel: {
      fontFamily: Fonts.OnestMedium,
      fontSize: FontSizes.medium,
      color: colors.textSecondary,
    },
    detailValue: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
    },
    missingTicketsContainer: {
      backgroundColor: isDark ? '#4A1F1F' : '#FFEBEE',
      paddingVertical: hp('1.2%'),
      alignItems: 'center',
    },
    missingTicketsText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.error,
    },
    noMissingContainer: {
      backgroundColor: isDark ? '#1F4A2F' : '#E8F5E8',
      paddingVertical: hp('1.2%'),
      alignItems: 'center',
    },
    noMissingText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.large,
      color: colors.success,
    },
  });

  return (
    <View style={styles.container}>
      <Header NavName="Finalize Sales" />

      <View style={styles.shiftInfoContainer}>
        <View style={styles.shiftInfoCard}>
          <Text style={styles.shiftInfoTitle}>Shift Information</Text>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Shift ID:</Text>
            <Text style={styles.infoValue}>{shiftData.Shift_ID}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Cashier ID:</Text>
            <Text style={styles.infoValue}>
              {store + '-' + cashier + '   ' + empName}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Shift Started:</Text>
            <Text style={styles.infoValue}>
              {formatDate(shiftData.Shift_Start_Time)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Shift End:</Text>
            <Text style={styles.infoValue}>
              {formatDate(shiftData.Shift_End_Time)}
            </Text>
          </View>
        </View>
      </View>

      <Text style={styles.sectionTitle}>Game Details</Text>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        {shiftGameData.map((shift, index) => (
          <View key={index} style={styles.gameCard}>
            <View style={styles.gameHeaderContainer}>
              <View style={styles.gameHeaderItem}>
                <Text style={styles.gameHeaderLabel}>Game ID</Text>
                <Text style={styles.gameHeaderValue}>
                  {shift.Game_ID || 'N/A'}
                </Text>
              </View>

              <View style={styles.gameHeaderItem}>
                <Text style={styles.gameHeaderLabel}>Location</Text>
                <Text style={styles.gameHeaderValue}>
                  {shift.Location || 'N/A'}
                </Text>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.detailsSection}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Open Book:</Text>
                <Text style={styles.detailValue}>
                  {shift.Open_Book || 'N/A'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Close Book:</Text>
                <Text style={styles.detailValue}>
                  {shift.Close_Book || 'N/A'}
                </Text>
              </View>
            </View>

            <View style={styles.detailsSection}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Open Stock:</Text>
                <Text style={styles.detailValue}>
                  {shift.Stock_Level_Open || 'N/A'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Close Stock:</Text>
                <Text style={styles.detailValue}>
                  {shift.Stock_Level_Close || 'N/A'}
                </Text>
              </View>
            </View>

            <View style={styles.detailsSection}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Open Serial:</Text>
                <Text style={styles.detailValue}>
                  {shift.Shift_Open_Serial || 'N/A'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Close Serial:</Text>
                <Text style={styles.detailValue}>
                  {shift.Shift_Close_Serial || 'N/A'}
                </Text>
              </View>
            </View>

            <View style={styles.detailsSection}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Open Skip Reason:</Text>
                <Text style={styles.detailValue}>
                  {shift.Shift_Start_Skip || 'N/A'}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Close Skip Reason:</Text>
                <Text style={styles.detailValue}>
                  {shift.Shift_End_Skip || 'N/A'}
                </Text>
              </View>
            </View>

            {shift.Missing_Ticket > 0 ? (
              <View style={styles.missingTicketsContainer}>
                <Text style={styles.missingTicketsText}>
                  Missing Tickets: {shift.Missing_Ticket}
                </Text>
              </View>
            ) : (
              <View style={styles.noMissingContainer}>
                <Text style={styles.noMissingText}>No Missing Tickets</Text>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default LOT_FinalizeSales;
