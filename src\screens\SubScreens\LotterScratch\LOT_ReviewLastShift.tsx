import {View, Text, TextInput, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ert} from 'react-native';
import React, {useState, useEffect} from 'react';
import {Backround} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppButton from '../../../components/Inventory/AppButton';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LotBarcode {
  Barcode: string;
}

const LOT_ReviewLastShift = () => {
  const [data, setData] = useState<LotBarcode[]>([]);
  const [value, setValue] = useState<string>('');
  const [differenceCount, setDifferenceCount] = useState<number | null>(null); // To store the count of differences

  const onClick = () => {
    if (value) {
      const newData = [...data, {Barcode: value}];
      setData(newData);
      setValue('');
    }
  };

  const renderItem = ({item}: {item: LotBarcode}) => {
    return (
      <View style={{padding: 10}}>
        <Text style={{fontSize: 18, fontWeight: 'bold', color: 'green'}}>
          {item.Barcode}
        </Text>
      </View>
    );
  };

  const endShift = async () => {
    try {
      const savedData = await AsyncStorage.getItem('lotData');

      if (savedData) {
        const parsedSavedData: LotBarcode[] = JSON.parse(savedData);

        // Extract the Barcode values from data to a simple array for easy comparison
        const dataBarcodes = data.map(item => item.Barcode);

        let missingCount = 0;

        // Loop through the saved data and check if each barcode is present in data
        for (const asyn of parsedSavedData) {
          if (!dataBarcodes.includes(asyn.Barcode)) {
            missingCount++;
          } else {
          }
        }

        // Output how many are missing
        await AsyncStorage.setItem('lotData', '');
        Alert.alert(`${missingCount} Barcode Miss Check Stock`);
      }
    } catch (error) {
      console.error('Failed to compare data', error);
    }
  };

  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
      <Header NavName="Shift End" />

      <View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
          }}>
          <TextInput
            placeholder="Scan barcode"
            onChangeText={value => setValue(value)}
            value={value}
            style={{
              borderColor: 'red',
              borderRadius: 10,
              borderWidth: 2,
              paddingHorizontal: 10,
              width: 250,
            }}
          />
          <View style={{width: 100, marginLeft: 20}}>
            <Button title="Add" onPress={onClick} />
          </View>
        </View>

        <View
          style={{
            backgroundColor: 'yellow',
            height: '80%',
            width: '100%',
            marginTop: 30,
          }}>
          <Text
            style={{
              textAlign: 'center',
              fontSize: 18,
              fontWeight: 'bold',
              color: 'red',
            }}>
            Enter Barcode On Your End Shift
          </Text>
          <FlatList
            data={data}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderItem}
          />
        </View>

        <View>
          <AppButton Title="End Shift" OnPress={endShift} />
        </View>

        {/* <View>
          <AppButton Title='DATA' OnPress={async() => await AsyncStorage.setItem('lotData','')} />
        </View> */}

        {/* Show the difference count message */}
        {differenceCount !== null && (
          <View style={{padding: 20, marginTop: 20}}>
            <Text style={{fontSize: 16, color: 'red'}}>
              {differenceCount > 0
                ? `There are ${differenceCount} differences between the current data and the saved data.`
                : 'The data is identical to the saved data.'}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

export default LOT_ReviewLastShift;
