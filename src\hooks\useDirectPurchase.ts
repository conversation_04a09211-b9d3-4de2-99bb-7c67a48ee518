import {useState, useCallback} from 'react';
import {Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GetAllItemsWithFilter, showAlert} from '../utils/PublicHelper';
import {PurchaseOrderItems, VendorItem} from '../server/types';
import {getInventoryPort} from '../server/InstanceTypes';

export const useDirectPurchase = () => {
  const [purchaseItems, setPurchaseItems] = useState<PurchaseOrderItems[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const removeListItem = async (DeletItems: PurchaseOrderItems) => {
    showAlert('Are you sure you want to delete?')
      .then(async result => {
        if (result) {
          const updatedArray = purchaseItems.filter(
            item => item.ItemNum !== DeletItems.ItemNum,
          );
          setPurchaseItems(updatedArray);
          await AsyncStorage.setItem('DIRECTPO', JSON.stringify(updatedArray));
        }
      })
      .catch(error => {
        console.error('Error showing alert', error);
      });
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    setPage(1);
    // Filter vendor items directly instead of using handleSearch
    if (text) {
      const filtered = vendorItems.filter(
        item =>
          item.ItemName?.toLowerCase().includes(text.toLowerCase()) ||
          item.ItemNum?.toLowerCase().includes(text.toLowerCase()),
      );
      setVendorItemsFilter(filtered);
    } else {
      setVendorItemsFilter(vendorItems);
    }
  };

  const getPurchaseItems = async () => {
    const getItems = await AsyncStorage.getItem('DIRECTPO');

    if (getItems === null || getItems === undefined) {
      setPurchaseItems([]); // If no data found, set an empty array
    } else {
      try {
        const parsedItems = JSON.parse(getItems);
        setPurchaseItems(parsedItems);
      } catch (error) {
        console.error('Error parsing purchase items from AsyncStorage', error);
        setPurchaseItems([]);
      }
    }
  };

  const vendorDetails = async () => {
    GetAllItemsWithFilter(
      (await getInventoryPort()).toString(),
      '/getAllvendoritems',
      setVendorItems,
      setVendorItemsFilter,
      setLoading,
      false,
    );
  };

  const handleLoadMore = () => {
    if (!loading) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const isItemNumExist = (itemNum: string) => {
    return purchaseItems.some(item => item.ItemNum === itemNum);
  };

  const VendorItemAdd = (item: VendorItem, navigation: any) => {
    const IsExists = isItemNumExist(item.ItemNum);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      navigation.navigate('PurchaseItemView', {
        ItemData: item,
        Direct: true,
      });
    }
  };

  const ScanBarcode = (Barcode: string, navigation: any) => {
    const IsExists = isItemNumExist(Barcode);
    if (IsExists) {
      Alert.alert('Item Already Exists');
    } else {
      const GetVendorItem = vendorItems.filter(
        item => item.ItemNum === Barcode,
      );

      navigation.navigate('PurchaseItemView', {
        ItemData: GetVendorItem,
        Direct: true,
      });
    }
  };

  const initializeData = useCallback(async () => {
    await vendorDetails();
    await getPurchaseItems();
  }, []);

  return {
    // State
    purchaseItems,
    loading,
    vendorItems,
    vendorItemsFilter,
    searchQuery,
    page,
    pageSize,

    // Functions
    removeListItem,
    onSearchChange,
    getPurchaseItems,
    vendorDetails,
    handleLoadMore,
    isItemNumExist,
    VendorItemAdd,
    ScanBarcode,
    initializeData,

    // Setters
    setPurchaseItems,
    setLoading,
    setVendorItems,
    setVendorItemsFilter,
    setSearchQuery,
    setPage,
    setPageSize,
  };
};
