// inventory.ts (or any module you prefer)
import AsyncStorage from '@react-native-async-storage/async-storage';

export async function getInventoryPort(): Promise<string> {
  const localIP = await AsyncStorage.getItem('LOCALIP');
  if (!localIP) {
    throw new Error('LOCALIP is not set in AsyncStorage');
  }
  return `http://${localIP}:8090/api`;
}

export async function getLotteryPort(): Promise<string> {
  const localIP = await AsyncStorage.getItem('LOCALIP');
  if (!localIP) {
    throw new Error('LOCALIP is not set in AsyncStorage');
  }
  return `http://${localIP}:9090/api`;
}