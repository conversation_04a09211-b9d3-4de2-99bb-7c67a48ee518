// src/api/types.ts

export interface InventoryData {
  ItemNum?: string | null;
  ItemName?: string | null;
  Store_ID?: string | null;
  Cost?: number | null;
  Price?: number | null;
  Retail_Price?: number | null;
  In_Stock?: number | null;
  Reorder_Level?: number | null;
  Reorder_Quantity?: number | null;
  Tax_1?: boolean | false; // default false
  Tax_2?: boolean | false; // default false
  Tax_3?: boolean | false; // default false
  Vendor_Number?: string | null;
  Dept_ID?: string | null;
  IsKit?: boolean; // default false
  IsModifier?: boolean; // default false
  Kit_Override?: number | null;
  Inv_Num_Barcode_Labels?: number | null;
  Use_Serial_Numbers?: boolean; // default false
  Num_Bonus_Points?: number | null;
  IsRental?: boolean; // default false
  Use_Bulk_Pricing?: boolean; // default false
  Print_Ticket?: boolean; // default false
  Print_Voucher?: boolean; // default false
  Num_Days_Valid?: number | null;
  IsMatrixItem?: boolean; // default false
  Vendor_Part_Num?: string | null;
  Location?: string | null;
  AutoWeigh?: boolean; // default false
  numBoxes?: number | null;
  Dirty?: boolean; // default false
  Tear?: number | null;
  NumPerCase?: number | null;
  FoodStampable?: boolean; // default false
  ReOrder_Cost?: number | null;
  Helper_ItemNum?: string | null;
  ItemName_Extra?: string | null;
  Exclude_Acct_Limit?: boolean; // default false
  Check_ID?: boolean; // default false
  Old_InStock?: number | null;
  Date_Created?: Date | string | null;
  ItemType?: number | null;
  Prompt_Price?: boolean; // default false
  Prompt_Quantity?: boolean; // default false
  Inactive?: number | null;
  Allow_BuyBack?: boolean; // default false
  Last_Sold?: Date | string | null;
  Unit_Type?: string | null;
  Unit_Size?: number | null;
  Fixed_Tax?: number | null;
  DOB?: number | null;
  Special_Permission?: boolean; // default false
  Prompt_Description?: boolean; // default false
  Check_ID2?: boolean; // default false
  Count_This_Item?: boolean; // default false
  Transfer_Cost_Markup?: number | null; // default false
  Print_On_Receipt?: boolean; // default false
  Transfer_Markup_Enabled?: boolean; // default false
  As_Is?: boolean; // default false
  InStock_Committed?: number | null;
  RequireCustomer?: boolean; // default false
  PromptCompletionDate?: boolean; // default false
  PromptInvoiceNotes?: boolean; // default false
  Prompt_DescriptionOverDollarAmt?: number | null;
  Exclude_From_Loyalty?: boolean; // default false
  BarTaxInclusive?: boolean; // default false
  ScaleSingleDeduct?: boolean; // default false
  GLNumber?: string | null;
  ModifierType?: number | null;
  Position?: number | null;
  numberOfFreeToppings?: number | null;
  ScaleItemType?: number | null;
  DiscountType?: number | null;
  AllowReturns?: boolean; // default false
  SuggestedDeposit?: number | null;
  Liability?: boolean; // default false
  IsDeleted?: boolean; // default false
  ItemLocale?: number | null;
  QuantityRequired?: number | null;
  AllowOnDepositInvoices?: boolean; // default false
  Import_Markup?: number | null;
  PricePerMeasure?: number | null;
  UnitMeasure?: number | null;
  ShipCompliantProductType?: string | null;
  AlcoholContent?: number | null;
  AvailableOnline?: boolean; // default false
  AllowOnFleetCard?: boolean; // default false
  DoughnutTax?: boolean; // default false
  DisplayTaxInPrice?: boolean; // default false
  NeverPrintInKitchen?: boolean; // default false
  RowID?: string | null;
  Tax_4?: boolean; // default false
  Tax_5?: boolean; // default false
  Tax_6?: boolean; // default false
  DisableInventoryUpload?: boolean; // default false
  InvoiceLimitQty?: number | null;
  ItemCategory?: number | null;
  IsRestrictedPerInvoice?: boolean; // default false
  TagStatus?: string | null;
}

export interface Department {
  Dept_ID: string;
  Description: string;
}

export interface Reason_Codes {
  Store_ID: string;
  Reason_Code: string;
  Reason_Type: number;
}

export interface Inventory_In {
  ItemNum: string | null;
  Store_ID: string | null;
  Quantity: string | number | null;
  CostPer: string | number | null;
  DateTime: string | null;
  Vendor_Number: string | null;
  Dirty: boolean | null;
  TransType: string | null;
  Destination: string | null;
  Description: string | null;
  Cashier_ID: string | null;
  PO_Number: number | null;
  Delivery_Number: string | null;
}

export interface Get_Max_ID {
  MaxValue: string | number | null;
}

export interface InventoryRefData {
  ID: string | number | null;
  ItemNum: string | number | null;
  Store_ID: string | number | null;
}

export interface SetupTsButtonData {
  Store_ID: string | '';
  Station_ID: string | '';
  Index: number | 0;
  Caption: string | '';
  Picture: string | '';
  Function: number | 0;
  Option1: string | '';
  BackColor: number | 0;
  ForeColor: number | null | 0;
  Visible: boolean | true;
  BtnType: number | 0;
  Ident: string | '';
  ScheduleIndex: number | 0;
  Option2: string | '';
  Option3: string | '';
  Option4: boolean | false;
  HideCaption: boolean | false;
}

export interface Kit_Index {
  Kit_ID: string | '';
  Store_ID: string | '';
  ItemNum: string | '';
  Discount: number | 0;
  Quantity: number | 0;
  Index: number | 0;
  Price: number | 0;
  Description: string | '';
  InvoiceMethodToUse: number | 0;
  ChoiceLockdown: number | 0;
}

export interface InventoryBumpbarData {
  Store_ID: string | '';
  ItemNum: string | '';
  Backcolor: number | 0;
  Forecolor: number | 0;
}

export interface AdditionalInfoData {
  Store_ID: string | '';
  ItemNum: string | '';
  ExtendedDescription: string | '';
  Keywords: string | '';
  Brand: string | '';
  Theme: string | '';
  SubCategory: string | '';
  LeadTime: string | '';
  ProductOnPromotionPreOrder: boolean | false;
  ProductOnSpecialOffer: boolean | false;
  NewProduct: boolean | false;
  Discountable: boolean | false;
  WebPrice: number | 0;
  ReleaseDate: string | null;
  Weight: number | 0;
  NoWebSales: boolean | false;
  IsPrimaryMatrixItem: boolean | false;
  Priority: number | 0;
  Rating: number | 0;
  CustomNumber1: number | 0;
  CustomNumber2: number | 0;
  CustomNumber3: number | 0;
  CustomNumber4: number | 0;
  CustomNumber5: number | 0;
  CustomText1: string | '';
  CustomText2: string | '';
  CustomText3: string | '';
  CustomText4: string | '';
  CustomText5: string | '';
  CustomExtendedText1: string | '';
  CustomExtendedText2: string | '';
  SubDescription1: string | '';
  SubDescription2: string | '';
  SubDescription3: string | '';
}

export interface AvailabilityInfo {
  org_id: string | null;
  available_days: string | null;
  available_time: string | null;
  report_send_time: string | null;
  is_ticket_ascending: boolean | null;
}

export interface Data {
  inventoryData: InventoryData;
  additionalInfoData: AdditionalInfoData;
  setupTsButtonData: SetupTsButtonData;
  InventoryRefData: InventoryRefData;
  InventoryBumpbarData: InventoryBumpbarData;
  availabilityInfo: AvailabilityInfo;
}
