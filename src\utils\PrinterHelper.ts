import { Alert } from "react-native";
import { getInventoryPort } from "../server/InstanceTypes";
import { Inventory } from "../server/types";
import { GetItemsParamsNoFilterNoReturn } from "./PublicHelper";
import { get } from "react-native/Libraries/TurboModule/TurboModuleRegistry";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { EPLlogo, EPLlogoTest } from "./EPLlogo";
import { getMargins, applyMargins } from "./marginHelper";
import { LabelMargins } from "../Types/PrinterTypes";

function getDiscountedPrice(originalPrice: number, discountPercent: number) {
  const discountAmount = (originalPrice * discountPercent) / 100;
  const discountedPrice = originalPrice - discountAmount;
  return discountedPrice.toFixed(2); // returns a string with 2 decimal places
}

/**
 * Apply margins to EPL coordinates and return formatted coordinate string
 */
const applyMarginsToCoordinates = (x: number, y: number, margins: LabelMargins): { x: number; y: number } => {
  return applyMargins(x, y, margins);
};



export const sendPrintLabelOne = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

    try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
        if (LabelDetails?.Vendor_Number) {
            VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );
        }
      // const VendorDetails = await GetItemsParamsNoFilterNoReturn(
      //   (await getInventoryPort()).toString(),
      //   '/getVendorDetails/:Vendor_Number',
      //   {Vendor_Number: LabelDetails?.Vendor_Number},
      // );
      
      const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : Number(LabelDetails?.Price);  
      const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
      let samplePrice =  LabelDetails?.Price?.toFixed(2) || "0.00";
      let price = LabelDetails?.Price != null ? Number(LabelDetails.Price).toFixed(2) : "0.00";
      let priceWithPadding = price.padStart(6, " ");   

      let dollarWithPad;

      let dollarX = 245;


      if (price.length === 6) {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 242;
      } else if (price.length === 5) {
        dollarWithPad = "$".padStart(2 , " ");
        dollarX = 270;
      } else if (price.length === 4) {
        dollarWithPad = "$".padStart(4, " ");
        dollarX = 297;
      } else {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 245;
      }
      
      const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
      if (getLoacalIp) {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq440
            \nQ250,24
            \nA${applyMarginsToCoordinates(0, 0, margins).x},${applyMarginsToCoordinates(0, 0, margins).y},0,3,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
            \nB${applyMarginsToCoordinates(0, 25, margins).x},${applyMarginsToCoordinates(0, 25, margins).y},0,1,2,10,35,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(0, 70, margins).x},${applyMarginsToCoordinates(0, 70, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(130, 70, margins).x},${applyMarginsToCoordinates(130, 70, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(0, 95, margins).x},${applyMarginsToCoordinates(0, 95, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(1, 95, margins).x},${applyMarginsToCoordinates(1, 95, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(0, 120, margins).x},${applyMarginsToCoordinates(0, 120, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName_Extra ? "" : LabelDetails?.ItemName_Extra}"
            \nA${applyMarginsToCoordinates(1, 120, margins).x},${applyMarginsToCoordinates(1, 120, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName_Extra ? "" : LabelDetails?.ItemName_Extra}"
            \nA${applyMarginsToCoordinates(5, 170, margins).x},${applyMarginsToCoordinates(5, 170, margins).y},0,4,1,2,N,"$"
            \nA${applyMarginsToCoordinates(25, 170, margins).x},${applyMarginsToCoordinates(25, 170, margins).y},0,4,1,2,N,"${!UnitPrice ? "" : "5.65"}"
            \nA${applyMarginsToCoordinates(105, 195, margins).x},${applyMarginsToCoordinates(105, 195, margins).y},0,1,1,1,N,"PER"
            \nA${applyMarginsToCoordinates(145, 195, margins).x},${applyMarginsToCoordinates(145, 195, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(0, 230, margins).x},${applyMarginsToCoordinates(0, 230, margins).y},0,1,1,1,N,"${!VendorDetails[0]?.Company ? "" : VendorDetails[0]?.Company}"
            \nA${applyMarginsToCoordinates(330, 150, margins).x},${applyMarginsToCoordinates(330, 150, margins).y},0,1,1,1,N,"Your Price"
            \nA${applyMarginsToCoordinates(330, 150, margins).x},${applyMarginsToCoordinates(330, 150, margins).y},0,1,1,1,N,"Your Price"
            \nA${applyMarginsToCoordinates(dollarX, 157, margins).x},${applyMarginsToCoordinates(dollarX, 157, margins).y},0,1,2,3,N,"$"
            \nA${applyMarginsToCoordinates(dollarX+1, 157, margins).x},${applyMarginsToCoordinates(dollarX+1, 157, margins).y},0,1,2,3,N,"$"
            \nA${applyMarginsToCoordinates(265, 170, margins).x},${applyMarginsToCoordinates(265, 170, margins).y},0,3,2,3,N,"${!priceWithPadding ? "" : priceWithPadding}"
            \nA${applyMarginsToCoordinates(263, 170, margins).x},${applyMarginsToCoordinates(263, 170, margins).y},0,3,2,3,N,"${!priceWithPadding ? "" : priceWithPadding}"
            \nA${applyMarginsToCoordinates(360, 230, margins).x},${applyMarginsToCoordinates(360, 230, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size}"
            \nA${applyMarginsToCoordinates(397, 230, margins).x},${applyMarginsToCoordinates(397, 230, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(430, 20, margins).x},${applyMarginsToCoordinates(430, 20, margins).y},1,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
  
  
        const result = await response.json();
        if (response.ok) {
          Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }
        

      
    } catch (error) {
      console.error('Print request error:', error);
      Alert.alert('Error', 'Failed to connect to print server');
    }
}

export const sendPrintLabelTwo = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

    try {
        // Get current margin settings
        const margins = await getMargins();

        let VendorDetails = [] as any[];
        if (LabelDetails?.Vendor_Number) {
            VendorDetails = await GetItemsParamsNoFilterNoReturn(
                (await getInventoryPort()).toString(),
                '/getVendorDetails/:Vendor_Number',
                {Vendor_Number: LabelDetails?.Vendor_Number},
              );

              console.log(VendorDetails, "Vendor Details print helper");

        }

      //const UnitPrice = Number(LabelDetails?.Price) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : Number(LabelDetails?.Price);  
      const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    //   let samplePrice =  LabelDetails?.Price?.toFixed(2) || "0.00";
    //   let samplePrice =  LabelDetails?.Pri;
    let price = LabelDetails?.Price != null ? Number(LabelDetails.Price).toFixed(2) : "0.00";
      let priceWithPadding = price.padStart(6, " ");   

      let dollarWithPad;

      let dollarX = 245;
      let priceX = 250;


      if (price.length === 6) {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 170;
        priceX = 200;
      } else if (price.length === 5) {
        dollarWithPad = "$".padStart(2 , " ");
        dollarX = 200;
        priceX = 230;
      } else if (price.length === 4) {
        dollarWithPad = "$".padStart(4, " ");
        dollarX = 235;
        priceX = 260;
      } else {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 245;
      }
      
      const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
      if (getLoacalIp) {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq400
            \nQ200,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(20, 110, margins).x},${applyMarginsToCoordinates(20, 110, margins).y},0,1,1,1,N,"${!VendorDetails[0]?.Company ? "" : VendorDetails[0]?.Company}"
            \nA${applyMarginsToCoordinates(21, 110, margins).x},${applyMarginsToCoordinates(21, 110, margins).y},0,1,1,1,N,"${!VendorDetails[0]?.Company ? "" : VendorDetails[0]?.Company}"
            \nB${applyMarginsToCoordinates(20, 130, margins).x},${applyMarginsToCoordinates(20, 130, margins).y},0,1,2,5,35,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 178, margins).x},${applyMarginsToCoordinates(20, 178, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(160, 178, margins).x},${applyMarginsToCoordinates(160, 178, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(295, 178, margins).x},${applyMarginsToCoordinates(295, 178, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(dollarX, 65, margins).x},${applyMarginsToCoordinates(dollarX, 65, margins).y},0,2,2,2,N,"${"$"}"
            \nA${applyMarginsToCoordinates(dollarX, 66, margins).x},${applyMarginsToCoordinates(dollarX, 66, margins).y},0,2,2,2,N,"${"$"}"
            \nA${applyMarginsToCoordinates(priceX, 70, margins).x},${applyMarginsToCoordinates(priceX, 70, margins).y},0,4,2,3,N,"${!price ? "" : price}"
            \nA${applyMarginsToCoordinates(priceX-3, 70, margins).x},${applyMarginsToCoordinates(priceX-3, 70, margins).y},0,4,2,3,N,"${!price ? "" : price}"
            \nA${applyMarginsToCoordinates(315, 140, margins).x},${applyMarginsToCoordinates(315, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size}"
            \nA${applyMarginsToCoordinates(350, 140, margins).x},${applyMarginsToCoordinates(350, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
  
  
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }
        
      
    } catch (error) {
      console.error('Print request error:', error);
      Alert.alert('Error', 'Failed to connect to print server');
    }
}

export const sendPrintLabelThree = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

    try {
        // Get current margin settings
        const margins = await getMargins();

        let VendorDetails = [] as any[];
        if (LabelDetails?.Vendor_Number) {
            VendorDetails = await GetItemsParamsNoFilterNoReturn(
                (await getInventoryPort()).toString(),
                '/getVendorDetails/:Vendor_Number',
                {Vendor_Number: LabelDetails?.Vendor_Number},
              );

        }
        
      const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
      const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
      
      const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
        const line1 = text.substring(0, maxLength);
        const line2 = text.substring(maxLength, maxLength * 2);
        return [line1, line2];
      };
      const [companyLine1, companyLine2] = splitTextForEPL(VendorDetails[0]?.Company);

      const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
      if (getLoacalIp) {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq460
            \nQ240,24
            \nA${applyMarginsToCoordinates(30, 10, margins).x},${applyMarginsToCoordinates(30, 10, margins).y},0,3,1,1,N,"${"UNIT PRICE"}"
            \nA${applyMarginsToCoordinates(32, 10, margins).x},${applyMarginsToCoordinates(32, 10, margins).y},0,3,1,1,N,"${"UNIT PRICE"}"
            \nA${applyMarginsToCoordinates(280, 10, margins).x},${applyMarginsToCoordinates(280, 10, margins).y},0,3,1,1,N,"${"YOU PAY"}"
            \nA${applyMarginsToCoordinates(282, 10, margins).x},${applyMarginsToCoordinates(282, 10, margins).y},0,3,1,1,N,"${"YOU PAY"}"
            \nA${applyMarginsToCoordinates(20, 53, margins).x},${applyMarginsToCoordinates(20, 53, margins).y},0,2,2,2,N,"${"$"}"
            \nA${applyMarginsToCoordinates(45, 55, margins).x},${applyMarginsToCoordinates(45, 55, margins).y},0,2,2,2,N,"${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(47, 55, margins).x},${applyMarginsToCoordinates(47, 55, margins).y},0,2,2,2,N,"${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(260, 40, margins).x},${applyMarginsToCoordinates(260, 40, margins).y},0,3,2,3,N,"${"$"}"
            \nA${applyMarginsToCoordinates(290, 45, margins).x},${applyMarginsToCoordinates(290, 45, margins).y},0,3,2,3,N,"${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(292, 45, margins).x},${applyMarginsToCoordinates(292, 45, margins).y},0,3,2,3,N,"${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(130, 92, margins).x},${applyMarginsToCoordinates(130, 92, margins).y},0,1,1,1,N,"${"PER"}"
            \nA${applyMarginsToCoordinates(170, 92, margins).x},${applyMarginsToCoordinates(170, 92, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(380, 98, margins).x},${applyMarginsToCoordinates(380, 98, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size}"
            \nA${applyMarginsToCoordinates(410, 98, margins).x},${applyMarginsToCoordinates(410, 98, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(10, 128, margins).x},${applyMarginsToCoordinates(10, 128, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(12, 128, margins).x},${applyMarginsToCoordinates(12, 128, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(10, 160, margins).x},${applyMarginsToCoordinates(10, 160, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(10, 215, margins).x},${applyMarginsToCoordinates(10, 215, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(100, 190, margins).x},${applyMarginsToCoordinates(100, 190, margins).y},0,1,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(150, 215, margins).x},${applyMarginsToCoordinates(150, 215, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nB${applyMarginsToCoordinates(260, 170, margins).x},${applyMarginsToCoordinates(260, 170, margins).y},0,1,2,5,35,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(310, 215, margins).x},${applyMarginsToCoordinates(310, 215, margins).y},0,1,1,1,N,"${!LabelDetails.Vendor_Number ? "" : LabelDetails?.Vendor_Number}"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
  
  
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }

     
    } catch (error) {
      console.error('Print request error:', error);
      Alert.alert('Error', 'Failed to connect to print server');
    }
}

export const sendPrintLabelFour = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

    try {
        // Get current margin settings
        const margins = await getMargins();

        let VendorDetails = [] as any[];
        if (LabelDetails?.Vendor_Number) {
            VendorDetails = await GetItemsParamsNoFilterNoReturn(
                (await getInventoryPort()).toString(),
                '/getVendorDetails/:Vendor_Number',
                {Vendor_Number: LabelDetails?.Vendor_Number},
              );

        }
        
      const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
      const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
      
      const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
        const line1 = text.substring(0, maxLength);
        const line2 = text.substring(maxLength, maxLength * 2);
        return [line1, line2];
      };
      const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName);
      const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

      let price = LabelDetails?.Price != null ? Number(LabelDetails.Price).toFixed(2) : "0.00";
      let priceWithPadding = price.padStart(6, " ");   

      let dollarWithPad;

      let dollarX = 190;
      let priceX = 210;


      if (price.length === 6) {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 210;
        priceX = 230;
      } else if (price.length === 5) {
        dollarWithPad = "$".padStart(2 , " ");
        dollarX = 230;
        priceX = 250;
      } else if (price.length === 4) {
        dollarWithPad = "$".padStart(4, " ");
        dollarX = 250;
        priceX = 270;
      } else {
        dollarWithPad = "$".padStart(0, " ");
        dollarX = 245;
      }
      
      
      const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
      if (getLoacalIp) {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq450
            \nQ200,24
            \nA${applyMarginsToCoordinates(40, 10, margins).x},${applyMarginsToCoordinates(40, 10, margins).y},0,1,1,1,N,"${"UNIT PRICE"}"
            \nA${applyMarginsToCoordinates(41, 10, margins).x},${applyMarginsToCoordinates(41, 10, margins).y},0,1,1,1,N,"${"UNIT PRICE"}"
            \nA${applyMarginsToCoordinates(280, 20, margins).x},${applyMarginsToCoordinates(280, 20, margins).y},0,3,1,1,N,"${"YOU PAY"}"
            \nA${applyMarginsToCoordinates(281, 20, margins).x},${applyMarginsToCoordinates(281, 20, margins).y},0,3,1,1,N,"${"YOU PAY"}"
            \nA${applyMarginsToCoordinates(dollarX, 50, margins).x},${applyMarginsToCoordinates(dollarX, 50, margins).y},0,1,2,3,N,"${"$"}"
            \nA${applyMarginsToCoordinates(priceX, 70, margins).x},${applyMarginsToCoordinates(priceX, 70, margins).y},0,4,2,3,N,"${!price ? "" : price}"
            \nA${applyMarginsToCoordinates(380, 150, margins).x},${applyMarginsToCoordinates(380, 150, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size}"
            \nA${applyMarginsToCoordinates(405, 150, margins).x},${applyMarginsToCoordinates(405, 150, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(25, 37, margins).x},${applyMarginsToCoordinates(25, 37, margins).y},0,1,2,2,N,"${"$"}"
            \nA${applyMarginsToCoordinates(26, 37, margins).x},${applyMarginsToCoordinates(26, 37, margins).y},0,1,2,2,N,"${"$"}"
            \nA${applyMarginsToCoordinates(50, 40, margins).x},${applyMarginsToCoordinates(50, 40, margins).y},0,1,2,2,N,"${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(51, 40, margins).x},${applyMarginsToCoordinates(51, 40, margins).y},0,1,2,2,N,"${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(445, 20, margins).x},${applyMarginsToCoordinates(445, 20, margins).y},1,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(110, 67, margins).x},${applyMarginsToCoordinates(110, 67, margins).y},0,1,1,1,N,"${"PER"}"
            \nA${applyMarginsToCoordinates(150, 67, margins).x},${applyMarginsToCoordinates(150, 67, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 160, margins).x},${applyMarginsToCoordinates(20, 160, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nA${applyMarginsToCoordinates(20, 161, margins).x},${applyMarginsToCoordinates(20, 161, margins).y},0,2,1,1,N,"${!LabelDetails?.ItemName ? "" : LabelDetails?.ItemName}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(60, 140, margins).x},${applyMarginsToCoordinates(60, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(260, 182, margins).x},${applyMarginsToCoordinates(260, 182, margins).y},0,1,1,1,N,"${vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 182, margins).x},${applyMarginsToCoordinates(370, 182, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
  
  
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }

      
    } catch (error) {
      console.error('Print request error:', error);
      Alert.alert('Error', 'Failed to connect to print server');
    }
}


export const sendPrintLabelFive = async(LabelDetails: Inventory, selectedPrinter: String,  Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }
      
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName);
    const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

    let price = LabelDetails?.Price != null ? Number(LabelDetails.Price).toFixed(2) : "0.00";
    let priceWithPadding = price.padStart(6, " ");   

    let dollarWithPad;

    let dollarX = 190;
    let priceX = 210;


    if (price.length === 6) {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 210;
      priceX = 230;
    } else if (price.length === 5) {
      dollarWithPad = "$".padStart(2 , " ");
      dollarX = 230;
      priceX = 250;
    } else if (price.length === 4) {
      dollarWithPad = "$".padStart(4, " ");
      dollarX = 250;
      priceX = 270;
    } else {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 245;
    }
    
    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {
      const response = await fetch('http://*************:8090/print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          labelData: `N
          \nq200
          \nQ220,24
          \nA${applyMarginsToCoordinates(10, 20, margins).x},${applyMarginsToCoordinates(10, 20, margins).y},0,2,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(11, 20, margins).x},${applyMarginsToCoordinates(11, 20, margins).y},0,2,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(30, 48, margins).x},${applyMarginsToCoordinates(30, 48, margins).y},0,1,2,3,N,"${"$"}"
          \nA${applyMarginsToCoordinates(50, 50, margins).x},${applyMarginsToCoordinates(50, 50, margins).y},0,1,2,3,N,"${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
          \nA${applyMarginsToCoordinates(51, 50, margins).x},${applyMarginsToCoordinates(51, 50, margins).y},0,1,2,3,N,"${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
          \nA${applyMarginsToCoordinates(20, 100, margins).x},${applyMarginsToCoordinates(20, 100, margins).y},0,2,1,1,N,"${companyLine1}"
          \nA${applyMarginsToCoordinates(20, 120, margins).x},${applyMarginsToCoordinates(20, 120, margins).y},0,2,1,1,N,"${companyLine2}"
          \nB${applyMarginsToCoordinates(40, 140, margins).x},${applyMarginsToCoordinates(40, 140, margins).y},0,1,1,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nA${applyMarginsToCoordinates(40, 190, margins).x},${applyMarginsToCoordinates(40, 190, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nP1\n`,
          ipAddress: getLoacalIp,
          printerName: selectedPrinter
        }),
      });
  
  
      const result = await response.json();
      if (response.ok) {
        console.log(LabelDetails?.ItemName, "Result Print Label Two");
        
        //Alert.alert('Success', result.message);
      } else {
        Alert.alert('Error', result.error || 'Failed to print label');
      }
    }

    
  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}

export const sendPrintLabelSix = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }
      
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 12);
    const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

    // T
    let price = LabelDetails?.Price != null ? Number(LabelDetails.Price).toFixed(2) : "0.00";

    let priceWithPadding = price.padStart(6, " ");   

    let dollarWithPad;

    let dollarX = 227;
    let priceX = 240;


    if (price.length === 6) {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 200;
      priceX = 212;
    } else if (price.length === 5) {
      dollarWithPad = "$".padStart(2 , " ");
      dollarX = 212;
      priceX = 225;
    } else if (price.length === 4) {
      dollarWithPad = "$".padStart(4, " ");
      dollarX = 227;
      priceX = 240;
    } else {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 245;
    }
    
    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {
      const response = await fetch('http://*************:8090/print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          labelData: `N
          \nq300
          \nQ100,24
          \nA${applyMarginsToCoordinates(10, 3, margins).x},${applyMarginsToCoordinates(10, 3, margins).y},0,2,1,1,N,"${companyLine1}"
          \nA${applyMarginsToCoordinates(11, 3, margins).x},${applyMarginsToCoordinates(11, 3, margins).y},0,2,1,1,N,"${companyLine1}"
          \nA${applyMarginsToCoordinates(10, 23, margins).x},${applyMarginsToCoordinates(10, 23, margins).y},0,2,1,1,N,"${companyLine2}"
          \nA${applyMarginsToCoordinates(11, 23, margins).x},${applyMarginsToCoordinates(11, 23, margins).y},0,2,1,1,N,"${companyLine2}"
          \nB${applyMarginsToCoordinates(10, 42, margins).x},${applyMarginsToCoordinates(10, 42, margins).y},0,1,1,2,28,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nA${applyMarginsToCoordinates(10, 75, margins).x},${applyMarginsToCoordinates(10, 75, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nA${applyMarginsToCoordinates(dollarX, 22, margins).x},${applyMarginsToCoordinates(dollarX, 22, margins).y},0,2,1,1,N,"${"$"}"
          \nA${applyMarginsToCoordinates(priceX, 30, margins).x},${applyMarginsToCoordinates(priceX, 30, margins).y},0,3,1,2,N,"${!price ? "" : price}"
          \nP1\n`,
          ipAddress: getLoacalIp,
          printerName: selectedPrinter
        }),
      });
  
  
      const result = await response.json();
      if (response.ok) {
        console.log(LabelDetails?.ItemName, "Result Print Label Two");
        
        //Alert.alert('Success', result.message);
      } else {
        Alert.alert('Error', result.error || 'Failed to print label');
      }
    }


  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}

export const sendPrintLabelBulkPricing = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }

      const getBulkInfo = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getBulkInfo/:ItemNum',
              {ItemNum: LabelDetails?.ItemNum},
      );
      
      
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 20);
    const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

    const getPercentage = (value: number) => {
      return `${value * 100}`;
    };
    const percentage = getPercentage(getBulkInfo[0]?.Bulk_Price);
   
    
    
    const getParts = (value: Number) => {
      const [intPart, decimalPart] = value.toString().split(".");
      const firstTwoDecimals = decimalPart ? decimalPart.slice(0, 2) : "00";
      return {
        intPart,
        firstTwoDecimals,
      };
    };
    const { intPart, firstTwoDecimals } = getParts(getBulkInfo[0]?.Bulk_Price);
    const getSavePrice =  Number(LabelDetails?.Price) * Number(getBulkInfo[0]?.Bulk_Quan);
    const savePrice = Number(getSavePrice) - Number(getBulkInfo[0]?.Bulk_Price);

    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {
      if (getBulkInfo[0]?.Price_Type === 1) {
        const finalPriceCal = Number(getBulkInfo[0]?.Bulk_Quan) * Number(LabelDetails?.Price);
        console.log("LOG PRINC", finalPriceCal);
        
        const finalPrice = getDiscountedPrice(finalPriceCal, Number(percentage));
  
    
        const finalSavePrice = Number(finalPriceCal) - Number(finalPrice);
  
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "0" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(50, 355, margins).x},${applyMarginsToCoordinates(50, 355, margins).y},0,4,1,1,N,"${!getBulkInfo[0]?.Description ? "" : getBulkInfo[0]?.Description}"
            \nA${applyMarginsToCoordinates(150, 400, margins).x},${applyMarginsToCoordinates(150, 400, margins).y},0,4,2,2,N,"BUY ${!getBulkInfo[0]?.Bulk_Quan ? "0" : Number(getBulkInfo[0]?.Bulk_Quan)}"
            \nA${applyMarginsToCoordinates(150, 470, margins).x},${applyMarginsToCoordinates(150, 470, margins).y},0,4,2,2,N,"${ "GET"}"
            \nA${applyMarginsToCoordinates(150, 530, margins).x},${applyMarginsToCoordinates(150, 530, margins).y},0,4,2,2,N,"${!percentage ? "" : percentage}% OFF"
            \nA${applyMarginsToCoordinates(40, 610, margins).x},${applyMarginsToCoordinates(40, 610, margins).y},0,3,2,2,N,"You Save $${!finalSavePrice ? "0.00" : finalSavePrice.toFixed(2)}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      } else {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "0" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(50, 365, margins).x},${applyMarginsToCoordinates(50, 365, margins).y},0,4,1,1,N,"${!getBulkInfo[0]?.Description ? "" : getBulkInfo[0]?.Description}"
            \nA${applyMarginsToCoordinates(60, 430, margins).x},${applyMarginsToCoordinates(60, 430, margins).y},0,4,4,4,N,"${!getBulkInfo[0]?.Bulk_Quan ? "0" : Number(getBulkInfo[0]?.Bulk_Quan)}"
            \nA${applyMarginsToCoordinates(120, 430, margins).x},${applyMarginsToCoordinates(120, 430, margins).y},0,4,4,4,N,"/"
            \nA${applyMarginsToCoordinates(180, 400, margins).x},${applyMarginsToCoordinates(180, 400, margins).y},0,2,3,3,N,"$"
            \nA${applyMarginsToCoordinates(210, 430, margins).x},${applyMarginsToCoordinates(210, 430, margins).y},0,4,4,4,N,"${!intPart ? "0" : intPart}."
            \nA${applyMarginsToCoordinates(360, 430, margins).x},${applyMarginsToCoordinates(360, 430, margins).y},0,2,2,3,N,"${!firstTwoDecimals ? "00" : firstTwoDecimals}"
            \nA${applyMarginsToCoordinates(30, 580, margins).x},${applyMarginsToCoordinates(30, 580, margins).y},0,3,2,2,N,"You Save $${!savePrice ? "0.00" : savePrice.toFixed(2)}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }
    }

    

  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}

export const sendPrintLabelSalePricing = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }

      const getSaleInfo = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getSaleInfo/:ItemNum',
        {ItemNum: LabelDetails?.ItemNum},
      );

      
    const finalPrice = getDiscountedPrice(LabelDetails?.Price, Number(getSaleInfo[0]?.Percent));
    console.log("SALE INFO", getSaleInfo, finalPrice);

      
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 20);
    const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);


    const getParts = (value: Number) => {
      const [intPart, decimalPart] = value.toString().split(".");
      const firstTwoDecimals = decimalPart ? decimalPart.slice(0, 2) : "00";
      return {
        intPart,
        firstTwoDecimals,
      };
    };

    const { intPart, firstTwoDecimals } = getParts(finalPrice);
    const getSavePrice =  Number(LabelDetails?.Price) - Number(finalPrice);
    
    const date = new Date(getSaleInfo[0]?.Sale_End);
    const formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
    
    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {
      if (getSaleInfo[0]?.SalePriceType === 1) {
        const { intPart, firstTwoDecimals } = getParts(Number(getSaleInfo[0]?.Price));
        const getSavePriceType =  Number(LabelDetails?.Price) - Number(getSaleInfo[0]?.Price);
  
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "0" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(50, 355, margins).x},${applyMarginsToCoordinates(50, 355, margins).y},0,4,1,1,N,"SALE PRICE"
            \nA${applyMarginsToCoordinates(180, 400, margins).x},${applyMarginsToCoordinates(180, 400, margins).y},0,2,3,3,N,"$"
            \nA${applyMarginsToCoordinates(210, 430, margins).x},${applyMarginsToCoordinates(210, 430, margins).y},0,4,4,4,N,"${!intPart ? "0" : intPart}."
            \nA${applyMarginsToCoordinates(360, 430, margins).x},${applyMarginsToCoordinates(360, 430, margins).y},0,2,2,3,N,"${!firstTwoDecimals ? "00" : firstTwoDecimals}"
            \nA${applyMarginsToCoordinates(30, 550, margins).x},${applyMarginsToCoordinates(30, 550, margins).y},0,3,2,2,N,"You Save $${!getSavePriceType ? "" : getSavePriceType.toFixed(2)}"
            \nA${applyMarginsToCoordinates(100, 630, margins).x},${applyMarginsToCoordinates(100, 630, margins).y},0,1,2,2,N,"GOOD THRU ${!formattedDate ? "" : formattedDate}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      } else {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : LabelDetails?.Price}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "0" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(200, 380, margins).x},${applyMarginsToCoordinates(200, 380, margins).y},0,2,2,2,N,"Buy 1"
            \nA${applyMarginsToCoordinates(201, 380, margins).x},${applyMarginsToCoordinates(201, 380, margins).y},0,2,2,2,N,"Buy 1"
            \nA${applyMarginsToCoordinates(200, 425, margins).x},${applyMarginsToCoordinates(200, 425, margins).y},0,2,2,2,N,"Get 1"
            \nA${applyMarginsToCoordinates(201, 425, margins).x},${applyMarginsToCoordinates(201, 425, margins).y},0,2,2,2,N,"Get 1"
            \nA${applyMarginsToCoordinates(190, 480, margins).x},${applyMarginsToCoordinates(190, 480, margins).y},0,4,4,4,N,"${!getSaleInfo[0]?.Percent ? "" : getSaleInfo[0]?.Percent}"
            \nA${applyMarginsToCoordinates(320, 480, margins).x},${applyMarginsToCoordinates(320, 480, margins).y},0,4,2,2,N,"%"
            \nA${applyMarginsToCoordinates(320, 540, margins).x},${applyMarginsToCoordinates(320, 540, margins).y},0,4,1,1,N,"off"
            \nA${applyMarginsToCoordinates(260, 580, margins).x},${applyMarginsToCoordinates(260, 580, margins).y},0,2,1,1,N,"Regular Retail"
            \nA${applyMarginsToCoordinates(100, 630, margins).x},${applyMarginsToCoordinates(100, 630, margins).y},0,1,2,2,N,"GOOD THRU ${!formattedDate ? "" : formattedDate}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }
    }

    
  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}

export const sendPrintLabelMixAndMatch = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }

      let mixLabelItemNames = [] as any[];
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };

    const splitTextForEPLFour = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      const line3 = text.substring(maxLength * 2, maxLength * 3);
      const line4 = text.substring(maxLength * 3, maxLength * 4);
      return [line1, line2, line3, line4];
    };
    
    
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 20);
    const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

    
    
    // const date = new Date(getSaleInfo[0]?.Sale_End);
    // const formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
    

   const getMixAndMatchDetails = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/GetKidIndexByItemNum/:ItemNum',
      {ItemNum: LabelDetails?.ItemNum},
    );

    let labelDetailsMix = [] as any[];
    let q1 = "";
    let q2 = "";
    let q3 = "";
    let q4 = "";

    if (getMixAndMatchDetails) {
      labelDetailsMix = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: getMixAndMatchDetails[0]?.Kit_ID},
      );

      mixLabelItemNames = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        'GetKidIndexItemName/:Kit_ID',
        {Kit_ID: labelDetailsMix[0]?.ItemNum},
      );
      const [qline1, qline2, qline3, qline4] = splitTextForEPLFour(mixLabelItemNames[0]?.ItemNames, 35);
      q1 = qline1;
      q2 = qline2;
      q3 = qline3;
      q4 = qline4;
    }

    const getPercentage = (value: number) => {
      return `${value * 100}`;
    };
    const percentage = getPercentage(labelDetailsMix[0]?.Price);

    let price =  labelDetailsMix[0]?.ItemType === 6 ? percentage.toString() || "486.25" : parseInt(labelDetailsMix[0]?.Price).toString() || "486.25";
    let priceWithPadding = price.padStart(6, " ");   

    let dollarWithPad;

    let dollarX =  160 ;
    let priceX = 180;


    if (price.length === 2) {
      dollarWithPad = "$".padStart(0, " ");
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 200;
        priceX = 180;
      }else{
        dollarX = 200;
        priceX = 100;
      }

    } else if (price.length === 1) {
      dollarWithPad = "$".padStart(2 , " ");
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 212;
        priceX = 235;
      }else{
        dollarX = 212;
        priceX = 160;
      }
      
    } else if (price.length === 3) {
      dollarWithPad = "$".padStart(4, " ");
      
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 227;
      priceX = 240;
      }else{
        dollarX = 227;
      priceX = 50;
      }

    } else {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 245;
    }
    
    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {
      if (labelDetailsMix[0]?.ItemType === 5) {  
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(80, 320, margins).x},${applyMarginsToCoordinates(80, 320, margins).y},0,2,1,1,N,"Mix N Match: ${!labelDetailsMix[0]?.ItemName ? "" : labelDetailsMix[0]?.ItemName}"
            \nA${applyMarginsToCoordinates(40, 385, margins).x},${applyMarginsToCoordinates(40, 385, margins).y},0,4,3,3,N,"${!labelDetailsMix[0]?.QuantityRequired ? "" : labelDetailsMix[0]?.QuantityRequired}"
            \nA${applyMarginsToCoordinates(100, 395, margins).x},${applyMarginsToCoordinates(100, 395, margins).y},0,1,3,3,N,"FOR"
            \nA${applyMarginsToCoordinates(210, 385, margins).x},${applyMarginsToCoordinates(210, 385, margins).y},0,3,3,3,N,"$${!labelDetailsMix[0]?.Price ? "" : Number(labelDetailsMix[0]?.Price).toFixed(2)}"
            \nA${applyMarginsToCoordinates(20, 490, margins).x},${applyMarginsToCoordinates(20, 490, margins).y},0,3,1,1,N,"Qualified Items:"
            \nA${applyMarginsToCoordinates(20, 515, margins).x},${applyMarginsToCoordinates(20, 515, margins).y},0,2,1,1,N,"${!q1 ? "" : q1}"
            \nA${applyMarginsToCoordinates(20, 545, margins).x},${applyMarginsToCoordinates(20, 545, margins).y},0,2,1,1,N,"${!q2 ? "" : q2}"
            \nA${applyMarginsToCoordinates(20, 575, margins).x},${applyMarginsToCoordinates(20, 575, margins).y},0,2,1,1,N,"${!q3 ? "" : q3}"
            \nA${applyMarginsToCoordinates(20, 605, margins).x},${applyMarginsToCoordinates(20, 605, margins).y},0,2,1,1,N,"${!q4 ? "" : q4}"
            \nA${applyMarginsToCoordinates(100, 630, margins).x},${applyMarginsToCoordinates(100, 630, margins).y},0,1,2,2,N,"GOOD THRU ${ "4/30"}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      } else if (labelDetailsMix[0]?.ItemType === 6) {
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(80, 320, margins).x},${applyMarginsToCoordinates(80, 320, margins).y},0,2,1,1,N,"Mix N Match: ${!labelDetailsMix[0]?.ItemName ? "" : labelDetailsMix[0]?.ItemName}"
            \nA${applyMarginsToCoordinates(120, 350, margins).x},${applyMarginsToCoordinates(120, 350, margins).y},0,1,2,2,N,"Buy ${!labelDetailsMix[0]?.QuantityRequired ? "" : labelDetailsMix[0]?.QuantityRequired} and Get"
            \nA${applyMarginsToCoordinates(priceX, 400, margins).x},${applyMarginsToCoordinates(priceX, 400, margins).y},0,4,4,4,N,"${!price ? "" : price}"
            \nA${applyMarginsToCoordinates(310, 390, margins).x},${applyMarginsToCoordinates(310, 390, margins).y},0,1,4,4,N,"%"
            \nA${applyMarginsToCoordinates(310, 460, margins).x},${applyMarginsToCoordinates(310, 460, margins).y},0,1,2,2,N,"OFF"
            \nA${applyMarginsToCoordinates(20, 520, margins).x},${applyMarginsToCoordinates(20, 520, margins).y},0,3,1,1,N,"Qualified Items:"
            \nA${applyMarginsToCoordinates(20, 545, margins).x},${applyMarginsToCoordinates(20, 545, margins).y},0,2,1,1,N,"${!q1 ? "" : q1}"
            \nA${applyMarginsToCoordinates(20, 565, margins).x},${applyMarginsToCoordinates(20, 565, margins).y},0,2,1,1,N,"${!q2 ? "" : q2}"
            \nA${applyMarginsToCoordinates(20, 585, margins).x},${applyMarginsToCoordinates(20, 585, margins).y},0,2,1,1,N,"${!q3 ? "" : q3}"
            \nA${applyMarginsToCoordinates(20, 600, margins).x},${applyMarginsToCoordinates(20, 600, margins).y},0,2,1,1,N,"${!q4 ? "" : q4}"
            \nA${applyMarginsToCoordinates(100, 630, margins).x},${applyMarginsToCoordinates(100, 630, margins).y},0,1,2,2,N,"GOOD THRU ${ "4/30"}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }else{
        const response = await fetch('http://*************:8090/print', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            labelData: `N
            \nq470
            \nQ800,24
            \nA${applyMarginsToCoordinates(20, 20, margins).x},${applyMarginsToCoordinates(20, 20, margins).y},0,4,1,1,N,"${companyLine1}"
            \nA${applyMarginsToCoordinates(20, 60, margins).x},${applyMarginsToCoordinates(20, 60, margins).y},0,4,1,1,N,"${companyLine2}"
            \nB${applyMarginsToCoordinates(20, 95, margins).x},${applyMarginsToCoordinates(20, 95, margins).y},0,1,2,2,40,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(20, 140, margins).x},${applyMarginsToCoordinates(20, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
            \nA${applyMarginsToCoordinates(210, 140, margins).x},${applyMarginsToCoordinates(210, 140, margins).y},0,1,1,1,N,"${!LabelDetails?.Vendor_Part_Num ? "" : LabelDetails?.Vendor_Part_Num}"
            \nA${applyMarginsToCoordinates(300, 115, margins).x},${applyMarginsToCoordinates(300, 115, margins).y},0,2,2,3,N,"$${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
            \nA${applyMarginsToCoordinates(370, 170, margins).x},${applyMarginsToCoordinates(370, 170, margins).y},0,1,1,1,N,"${!LabelDetails?.Unit_Size ? "" : LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(20, 170, margins).x},${applyMarginsToCoordinates(20, 170, margins).y},0,1,1,1,N,"UNIT PRICE"
            \nA${applyMarginsToCoordinates(20, 190, margins).x},${applyMarginsToCoordinates(20, 190, margins).y},0,1,2,2,N,"$${!UnitPrice ? "" : UnitPrice.toFixed(3)}"
            \nA${applyMarginsToCoordinates(140, 200, margins).x},${applyMarginsToCoordinates(140, 200, margins).y},0,1,1,1,N,"PER ${!LabelDetails?.Unit_Type ? "" : LabelDetails?.Unit_Type}"
            \nA${applyMarginsToCoordinates(240, 200, margins).x},${applyMarginsToCoordinates(240, 200, margins).y},0,1,1,1,N,"${!vendorLine1 ? "" : vendorLine1}"
            \nA${applyMarginsToCoordinates(370, 200, margins).x},${applyMarginsToCoordinates(370, 200, margins).y},0,1,1,1,N,"${!CreatedDate ? "" : CreatedDate}"
            \nA${applyMarginsToCoordinates(20, 270, margins).x},${applyMarginsToCoordinates(20, 270, margins).y},0,1,2,2,N,"${!companyLine1 ? "" : companyLine1}"
            \nA${applyMarginsToCoordinates(20, 300, margins).x},${applyMarginsToCoordinates(20, 300, margins).y},0,1,2,2,N,"${!companyLine2 ? "" : companyLine2}"
            \nA${applyMarginsToCoordinates(80, 320, margins).x},${applyMarginsToCoordinates(80, 320, margins).y},0,2,1,1,N,"Mix N Match: ${!labelDetailsMix[0]?.ItemName ? "" : labelDetailsMix[0]?.ItemName}"
            \nA${applyMarginsToCoordinates(20, 350, margins).x},${applyMarginsToCoordinates(20, 350, margins).y},0,1,2,2,N,"BUY ${!labelDetailsMix[0]?.QuantityRequired ? "" : labelDetailsMix[0]?.QuantityRequired} GET"
            \nA${applyMarginsToCoordinates(priceX, 400, margins).x},${applyMarginsToCoordinates(priceX, 400, margins).y},0,4,4,4,N,"$${!price ? "" : Number(price).toFixed(0)}"
            \nA${applyMarginsToCoordinates(310, 440, margins).x},${applyMarginsToCoordinates(310, 440, margins).y},0,2,3,3,N,"OFF"
            \nA${applyMarginsToCoordinates(20, 520, margins).x},${applyMarginsToCoordinates(20, 520, margins).y},0,3,1,1,N,"Qualified Items:"
            \nA${applyMarginsToCoordinates(20, 545, margins).x},${applyMarginsToCoordinates(20, 545, margins).y},0,2,1,1,N,"${!q1 ? "" : q1}"
            \nA${applyMarginsToCoordinates(20, 565, margins).x},${applyMarginsToCoordinates(20, 565, margins).y},0,2,1,1,N,"${!q2 ? "" : q2}"
            \nA${applyMarginsToCoordinates(20, 585, margins).x},${applyMarginsToCoordinates(20, 585, margins).y},0,2,1,1,N,"${!q3 ? "" : q3}"
            \nA${applyMarginsToCoordinates(20, 600, margins).x},${applyMarginsToCoordinates(20, 600, margins).y},0,2,1,1,N,"${!q4 ? "" : q4}"
            \nA${applyMarginsToCoordinates(100, 630, margins).x},${applyMarginsToCoordinates(100, 630, margins).y},0,1,2,2,N,"GOOD THRU ${ "4/30"}"
            \nA${applyMarginsToCoordinates(50, 700, margins).x},${applyMarginsToCoordinates(50, 700, margins).y},0,4,3,4,N,"SAVE BIG"
            \nP1\n`,
            ipAddress: getLoacalIp,
            printerName: selectedPrinter
          }),
        });
    
    
    
        const result = await response.json();
        if (response.ok) {
          console.log(LabelDetails?.ItemName, "Result Print Label Two");
          
          //Alert.alert('Success', result.message);
        } else {
          Alert.alert('Error', result.error || 'Failed to print label');
        }
      }
    }


      
    
  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}

// export const sendPrintLabelRound = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {
  
//   try {
//       let VendorDetails = [] as any[];
//       if (LabelDetails?.Vendor_Number) {
//           VendorDetails = await GetItemsParamsNoFilterNoReturn(
//               (await getInventoryPort()).toString(),
//               '/getVendorDetails/:Vendor_Number',
//               {Vendor_Number: LabelDetails?.Vendor_Number},
//             );
            
//       }

//       let getIncredients = [] as any[];
//     const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
//     const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
//     const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
//       const line1 = text.substring(0, maxLength);
//       const line2 = text.substring(maxLength, maxLength * 2);
//       return [line1, line2];
//     };

//     const splitTextForEPLFour = (text: string = '', maxLength: number = 15): string[] => {
//       const line1 = text.substring(0, maxLength);
//       const line2 = text.substring(maxLength, maxLength * 2);
//       const line3 = text.substring(maxLength * 2, maxLength * 3);
//       const line4 = text.substring(maxLength * 3, maxLength * 4);
//       return [line1, line2, line3, line4];
//     };
    
    
//     const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 20);
//     const [vendorLine1, vendorLine2] = splitTextForEPL(VendorDetails[0]?.Company, 10);

    
    
//     // const date = new Date(getSaleInfo[0]?.Sale_End);
//     // const formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
    

//    const getMixAndMatchDetails = await GetItemsParamsNoFilterNoReturn(
//       (await getInventoryPort()).toString(),
//       '/GetKidIndexByItemNum/:ItemNum',
//       {ItemNum: LabelDetails?.ItemNum},
//     );

//     let labelDetailsMix = [] as any[];
//     let q1 = "";
//     let q2 = "";
//     let q3 = "";
//     let q4 = "";

//     getIncredients = await GetItemsParamsNoFilterNoReturn(
//       (await getInventoryPort()).toString(),
//       '/getInvetoryNotes/:ItemNum',
//       {ItemNum: LabelDetails?.ItemNum},
//     );
//     const [qline1, qline2, qline3, qline4] = splitTextForEPLFour(getIncredients[0]?.Notes, 35);
//     q1 = qline1;
//     q2 = qline2;
//     q3 = qline3;
//     q4 = qline4;

//     // if (getMixAndMatchDetails) {
//     //   labelDetailsMix = await GetItemsParamsNoFilterNoReturn(
//     //     (await getInventoryPort()).toString(),
//     //     '/inventory/:ItemNum',
//     //     {ItemNum: getMixAndMatchDetails[0]?.Kit_ID},
//     //   );

      
//     // }

//     const getPercentage = (value: number) => {
//       return `${value * 100}`;
//     };
//     const percentage = getPercentage(labelDetailsMix[0]?.Price);

//     let price =  labelDetailsMix[0]?.ItemType === 6 ? percentage.toString() || "486.25" : parseInt(labelDetailsMix[0]?.Price).toString() || "486.25";
//     let priceWithPadding = price.padStart(6, " ");   

//     let dollarWithPad;

//     let dollarX =  160 ;
//     let priceX = 180;


//     if (price.length === 2) {
//       dollarWithPad = "$".padStart(0, " ");
//       if (labelDetailsMix[0]?.ItemType === 6) {
//         dollarX = 200;
//         priceX = 180;
//       }else{
//         dollarX = 200;
//         priceX = 100;
//       }

//     } else if (price.length === 1) {
//       dollarWithPad = "$".padStart(2 , " ");
//       if (labelDetailsMix[0]?.ItemType === 6) {
//         dollarX = 212;
//         priceX = 235;
//       }else{
//         dollarX = 212;
//         priceX = 160;
//       }
      
//     } else if (price.length === 3) {
//       dollarWithPad = "$".padStart(4, " ");
      
//       if (labelDetailsMix[0]?.ItemType === 6) {
//         dollarX = 227;
//       priceX = 240;
//       }else{
//         dollarX = 227;
//       priceX = 50;
//       }

//     } else {
//       dollarWithPad = "$".padStart(0, " ");
//       dollarX = 245;
//     }
    
    
//     const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
//     if (getLoacalIp) {

      
      
//       const response = await fetch('http://*************:8090/print', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           labelData: `N
//           \nq510
//           \nQ550,24
//           \nA130,90,0,4,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
//           \nA131,90,0,4,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
//           \nA120,140,0,1,2,2,N,"${companyLine1}"
//           \nA120,180,0,1,2,2,N,"${companyLine2}"
//           \nB50,220,0,1,1,5,50,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
//           \nA50,280,0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
//           \nA280,280,0,4,1,1,N,"PRICE:"
//           \nA370,280,0,4,1,1,N,"$${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
//           \nA0,320,0,1,1,1,N,"INGREDIENTS:"
//           \nLO 325,320,5,55
//           \nA340,320,0,2,1,1,R,"KEEP REFRIGERATED:"
//           \nA340,340,0,1,1,1,N,"BEST BEFROE"
//           \nA340,360,0,1,1,1,N,"10/01/2023"
//           \nA120,320,0,1,1,1,N,"${!q1 ? "" : q1}"
//           \nA0,340,0,1,1,1,N,"${!q2 ? "" : q3}"
//           \nA0,360,0,1,1,1,N,"${!q3 ? "" : q3}"
//           \nA170,400,0,2,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
//           \nA60,430,0,1,1,1,N,"${!Company[0]?.Company_Info_2 ? "" : Company[0]?.Company_Info_2}"
//           \nA170,450,0,2,1,1,N,"${!Company[0]?.Company_Info_4 ? "" : Company[0]?.Company_Info_4}"
//           \nA150,490,0,2,1,1,N,"NET WT. ${!LabelDetails?.Unit_Size ? "" :LabelDetails?.Unit_Size}  ${!LabelDetails?.Unit_Type ? "" :LabelDetails?.Unit_Type}  (850g)"
//           \nA151,490,0,2,1,1,N,"NET WT. ${!LabelDetails?.Unit_Size ? "" :LabelDetails?.Unit_Size}  ${!LabelDetails?.Unit_Type ? "" :LabelDetails?.Unit_Type}  (850g)"
//           \nP1\n`,
//           ipAddress: getLoacalIp,
//           printerName: selectedPrinter
//         }),

//         // const EPLlogoBase64 = EPLlogo..toString('base64');

//         // body: JSON.stringify({
//         //   labelData: `N
//         //   \nI8,1,001
//         //   \nq520
//         //   \nOD
//         //   \nJF
//         //   \nWN
//         //   \nZT
//         //   \nQ550,24
//         //   \nGW100,100,10,72,${encodedEPLlogo}
//         //   \nLO 220,100,5,75
//         //   \nP1\n`,
//         //   ipAddress: getLoacalIp,
//         //   printerName: selectedPrinter
//         // })
//       });
  
//       const result = await response.json();
//       if (response.ok) {
//         console.log(LabelDetails?.ItemName, "Result Print Label Two");
        
//         //Alert.alert('Success', result.message);
//       } else {
//         Alert.alert('Error', result.error || 'Failed to print label');
//       }
//     }
    
//   } catch (error) {
//     console.error('Print request error:', error);
//     Alert.alert('Error', 'Failed to connect to print server');
//   }
// }



const getFutureDate = (daysToAdd: number) => {
  const today = new Date(); // Today: May 10, 2025 (automatically taken)
  today.setDate(today.getDate() + daysToAdd);
  const month = today.getMonth() + 1; // Months are 0-based
  const day = today.getDate();
  const year = today.getFullYear();
  return `${month}/${day}/${year}`; // Format: MM.DD.YYYY
};
export const sendPrintLabelRound = async(LabelDetails: Inventory, selectedPrinter: String, Company: any) => {

  try {
      // Get current margin settings
      const margins = await getMargins();

      let VendorDetails = [] as any[];
      if (LabelDetails?.Vendor_Number) {
          VendorDetails = await GetItemsParamsNoFilterNoReturn(
              (await getInventoryPort()).toString(),
              '/getVendorDetails/:Vendor_Number',
              {Vendor_Number: LabelDetails?.Vendor_Number},
            );

      }

    

      let mixLabelItemNames = [] as any[];
    const UnitPrice = Number(LabelDetails?.Unit_Size) > 1 ? Number(LabelDetails?.Price) / Number(LabelDetails?.Unit_Size) : LabelDetails?.Price; 
    const CreatedDate = new Date(LabelDetails?.Date_Created).toISOString().split('T')[0]
    
    const splitTextForEPL = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2);
      return [line1, line2];
    };

    
    
    
    const [companyLine1, companyLine2] = splitTextForEPL(LabelDetails?.ItemName, 15);

    
    let labelDetailsMix = [] as any[];

   

    const splitTextForEPLFourInc = (text: string = '', maxLength: number = 15): string[] => {
      const line1 = text.substring(0, maxLength);
      const line2 = text.substring(maxLength, maxLength * 2.6);
      const line3 = text.substring(maxLength * 2.6, maxLength * 4.6);
      const line4 = text.substring(maxLength * 4.6, maxLength * 6);
      return [line1, line2, line3, line4];
    };
    

    
    let I1 = "";
    let I2 = "";
    let I3 = "";
    let I4 = "";

    
    
    const getBarcode = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            '/getInvetoryNotes/:ItemNum',
            {ItemNum: LabelDetails?.ItemNum},
          );
  

          if (getBarcode) {
            const [Iline1, Iline2, Iline3, Iline4] = splitTextForEPLFourInc(getBarcode[0]?.Notes, 18);
            I1 = Iline1 || "";
            I2 = Iline2 || "";
            I3 = Iline3 || "";
            I4 = Iline4 || "";
          }
          
   
     


    
    
    const getPercentage = (value: number) => {
      return `${value * 100}`;
    };
    const percentage = getPercentage(labelDetailsMix[0]?.Price);

    let price =  labelDetailsMix[0]?.ItemType === 6 ? percentage.toString() || "486.25" : parseInt(labelDetailsMix[0]?.Price).toString() || "486.25";
    let priceWithPadding = price.padStart(6, " ");   

    let dollarWithPad;

    let dollarX =  160 ;
    let priceX = 180;


    if (price.length === 2) {
      dollarWithPad = "$".padStart(0, " ");
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 200;
        priceX = 180;
      }else{
        dollarX = 200;
        priceX = 100;
      }

    } else if (price.length === 1) {
      dollarWithPad = "$".padStart(2 , " ");
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 212;
        priceX = 235;
      }else{
        dollarX = 212;
        priceX = 160;
      }
      
    } else if (price.length === 3) {
      dollarWithPad = "$".padStart(4, " ");
      
      if (labelDetailsMix[0]?.ItemType === 6) {
        dollarX = 227;
      priceX = 240;
      }else{
        dollarX = 227;
      priceX = 50;
      }

    } else {
      dollarWithPad = "$".padStart(0, " ");
      dollarX = 245;
    }
    
    
    const GetValidDates = getFutureDate(Number(LabelDetails?.Num_Days_Valid))
   
    
    
    const getLoacalIp = await AsyncStorage.getItem('LOCALIP');
    if (getLoacalIp) {

      
      
      const response = await fetch('http://*************:8090/print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          labelData: `N
          \nq510
          \nQ550,24
          \nA${applyMarginsToCoordinates(150, 90, margins).x},${applyMarginsToCoordinates(150, 90, margins).y},0,4,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(151, 90, margins).x},${applyMarginsToCoordinates(151, 90, margins).y},0,4,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(120, 140, margins).x},${applyMarginsToCoordinates(120, 140, margins).y},0,1,2,2,N,"${companyLine1}"
          \nA${applyMarginsToCoordinates(120, 180, margins).x},${applyMarginsToCoordinates(120, 180, margins).y},0,1,2,2,N,"${companyLine2}"
          \nB${applyMarginsToCoordinates(50, 220, margins).x},${applyMarginsToCoordinates(50, 220, margins).y},0,1,1,5,50,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nA${applyMarginsToCoordinates(50, 280, margins).x},${applyMarginsToCoordinates(50, 280, margins).y},0,1,1,1,N,"${!LabelDetails?.ItemNum ? "" : LabelDetails?.ItemNum}"
          \nA${applyMarginsToCoordinates(280, 280, margins).x},${applyMarginsToCoordinates(280, 280, margins).y},0,4,1,1,N,"PRICE:"
          \nA${applyMarginsToCoordinates(375, 280, margins).x},${applyMarginsToCoordinates(375, 280, margins).y},0,4,1,1,N,"$${!LabelDetails?.Price ? "" : Number(LabelDetails?.Price).toFixed(2)}"
          \nA${applyMarginsToCoordinates(0, 320, margins).x},${applyMarginsToCoordinates(0, 320, margins).y},0,1,1,1,N,"INGREDIENTS:"
          \nA${applyMarginsToCoordinates(1, 320, margins).x},${applyMarginsToCoordinates(1, 320, margins).y},0,1,1,1,N,"INGREDIENTS:"
          \nLO ${applyMarginsToCoordinates(330, 320, margins).x},${applyMarginsToCoordinates(330, 320, margins).y},5,55
          \nA${applyMarginsToCoordinates(340, 320, margins).x},${applyMarginsToCoordinates(340, 320, margins).y},0,2,1,1,R,"KEEP REFRIGERATED:"
          \nA${applyMarginsToCoordinates(340, 340, margins).x},${applyMarginsToCoordinates(340, 340, margins).y},0,1,1,1,N,"BEST BEFROE"
          \nA${applyMarginsToCoordinates(340, 360, margins).x},${applyMarginsToCoordinates(340, 360, margins).y},0,1,1,1,N,"${!GetValidDates ? "" : GetValidDates}"
          \nA${applyMarginsToCoordinates(120, 320, margins).x},${applyMarginsToCoordinates(120, 320, margins).y},0,1,1,1,N,"${!I1 ? "" : I1}"
          \nA${applyMarginsToCoordinates(5, 340, margins).x},${applyMarginsToCoordinates(5, 340, margins).y},0,1,1,1,N,"${!I2 ? "" : I2}"
          \nA${applyMarginsToCoordinates(10, 360, margins).x},${applyMarginsToCoordinates(10, 360, margins).y},0,1,1,1,N,"${!I3 ? "" : I3}"
          \nA${applyMarginsToCoordinates(20, 380, margins).x},${applyMarginsToCoordinates(20, 380, margins).y},0,1,1,1,N,"${!I4 ? "" : I4}"
          \nA${applyMarginsToCoordinates(170, 410, margins).x},${applyMarginsToCoordinates(170, 410, margins).y},0,2,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(171, 410, margins).x},${applyMarginsToCoordinates(171, 410, margins).y},0,2,1,1,N,"${!Company[0]?.Company_Info_1 ? "" : Company[0]?.Company_Info_1}"
          \nA${applyMarginsToCoordinates(60, 440, margins).x},${applyMarginsToCoordinates(60, 440, margins).y},0,1,1,1,N,"${!Company[0]?.Company_Info_2 ? "" : Company[0]?.Company_Info_2} ${!Company[0]?.Company_Info_3 ? "" : Company[0]?.Company_Info_3}"
          \nA${applyMarginsToCoordinates(170, 470, margins).x},${applyMarginsToCoordinates(170, 470, margins).y},0,2,1,1,N,"${!Company[0]?.Company_Info_4 ? "" : Company[0]?.Company_Info_4}"
          \nA${applyMarginsToCoordinates(190, 500, margins).x},${applyMarginsToCoordinates(190, 500, margins).y},0,2,1,1,N,"NET WT.${!LabelDetails?.Unit_Size ? "" :LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "":LabelDetails?.Unit_Type}"
          \nA${applyMarginsToCoordinates(191, 500, margins).x},${applyMarginsToCoordinates(191, 500, margins).y},0,2,1,1,N,"NET WT.${!LabelDetails?.Unit_Size ? "" :LabelDetails?.Unit_Size} ${!LabelDetails?.Unit_Type ? "" :LabelDetails?.Unit_Type}"
          \nP1\n`,
          ipAddress: getLoacalIp,
          printerName: selectedPrinter
        }),

        // const EPLlogoBase64 = EPLlogo..toString('base64');

        // body: JSON.stringify({
        //   labelData: `N
        //   \nI8,1,001
        //   \nq520
        //   \nOD
        //   \nJF
        //   \nWN
        //   \nZT
        //   \nQ550,24
        //   \nGW100,100,10,72,${encodedEPLlogo}
        //   \nLO 220,100,5,75
        //   \nP1\n`,
        //   ipAddress: getLoacalIp,
        //   printerName: selectedPrinter
        // })
      });
  
      const result = await response.json();
      if (response.ok) {
        console.log(LabelDetails?.ItemName, "Result Print Label Two");
        
        //Alert.alert('Success', result.message);
      } else {
        Alert.alert('Error', result.error || 'Failed to print label');
      }
    }
    
  } catch (error) {
    console.error('Print request error:', error);
    Alert.alert('Error', 'Failed to connect to print server');
  }
}
