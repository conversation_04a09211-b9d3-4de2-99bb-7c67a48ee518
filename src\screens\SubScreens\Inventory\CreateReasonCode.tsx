import {View, Text, Alert} from 'react-native';
import React, {useState} from 'react';
import {Setup_Reason_Codes} from '../../../server/types';
import {createItem} from '../../../server/service';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AppButton from '../../../components/Inventory/AppButton';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import Header from '../../../components/Inventory/Header';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {RouteProp} from '@react-navigation/native';
import {Backround} from '../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';

type AdjustStockScreenRouteProp = RouteProp<any, 'CreateReasonCode'>;

const CreateReasonCode: React.FC<{
  route: AdjustStockScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [touched, setTouched] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [SelectedReason, setSelectReason] = useState<string>(
    route.params?.ItemData || '',
  );

  const createReasonCode = async (reasonCode: any) => {
    if (!reasonCode.trim()) {
      setError('This field is required');
      setTouched(true);
      return;
    }
    setTouched(false);
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const ReasonCode: Setup_Reason_Codes = {
      Store_ID: ValidStore,
      Reason_Code: reasonCode,
      Reason_Type: Number(SelectedReason),
    };

    const result = await createItem(
      (await getInventoryPort()).toString(),
      '/createreasoncode',
      ReasonCode,
    );

    if (result) {
      navigation.goBack();
    }
  };

  const handleBlur = () => {
    setTouched(true);
    if (!inputValue.trim()) {
      setError('This field is required');
    } else {
      setError(''); // No error if validation passes
    }
  };

  const colors = useThemeColors();

  return (
    <View style={{paddingHorizontal: 10, backgroundColor: colors.background}}>
      {/* Rest of your JSX remains exactly the same */}
      <Header NavName="Create Reason Code" />
      <AppTextInput
        Value={inputValue}
        PlaceHolder="Enter Reason Code"
        Title="Reason Code"
        onChangeText={text => setInputValue(text)}
        onBlur={handleBlur}
        error={error}
        touched={touched}
      />
      <AppButton
        Title="Save & Close"
        OnPress={() => createReasonCode(inputValue)}
      />
    </View>
  );
};

export default CreateReasonCode;
