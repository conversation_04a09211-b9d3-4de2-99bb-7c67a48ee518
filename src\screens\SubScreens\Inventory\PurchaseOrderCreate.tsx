import {View, Text, Alert, StyleSheet} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  Get_Max_ID,
  Inventory,
  Inventory_In,
  PurchaseOrder,
  PurchaseOrderInsert,
  PurchaseOrderItems,
  Reason_Codes,
  Vendor,
} from '../../../server/types';
import {
  createData,
  GetAllItems,
  GetCommonLatestID,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetLatestID,
  updateData,
} from '../../../utils/PublicHelper';
import AppDatePicker from '../../../components/Inventory/AppDatePicker';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
  applyDefaultsPurchaseOrder,
  applyDefaultsPurchaseOrderItem,
} from '../../../Validator/Inventory/Barcode';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {MaterialColors} from '../../../constants/MaterialColors';
import {Fonts} from '../../../styles/fonts';
import FAB from '../../../components/common/FAB';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

const FontSizes = {
  small: 10,
  medium: 12,
  large: 14,
  xLarge: 16,
  xxLarge: 18,
};

const validationSchema = Yup.object().shape({
  vendor: Yup.string().required('Vendor is required'),
  refnumber: Yup.string(),
  date: Yup.string().required('Date is required'),
  shipvia: Yup.string().required('Ship Via is required'),
});

const validationSchemaReturn = Yup.object().shape({
  vendor: Yup.string().required('Vendor is required'),
  refnumber: Yup.string(),
  reasonCodes: Yup.string().required('Reason Code is required'),
  date: Yup.string().required('Date is required'),
  shipvia: Yup.string().required('Ship Via is required'),
});

type CountItemRouteProp = RouteProp<any, 'PurchaseOrderCreate'>;

const PurchaseOrderCreate: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [poType, setPoType] = useState<number>(route.params?.POType);
  const [directPurchaseItems, setDirectPurchaseItems] = useState<
    PurchaseOrderItems[]
  >(route.params?.ItemData);
  const [loading, setLoading] = useState<boolean>(false);
  const [isDirect, setIsDirect] = useState<boolean>(
    route.params?.IsDirect || false,
  );
  const [adjust, setAdjust] = useState<Inventory[]>();
  const [reasonCodes, setReasonCodes] = useState<Reason_Codes[]>([]);
  const [vendor, setVendor] = useState<Vendor[]>([]);

  useEffect(() => {}, []);

  useFocusEffect(
    useCallback(() => {
      getVendor();
      getReasonCode();
    }, []),
  );

  const getVendor = async () => {
    GetAllItems<Vendor[]>(
      (await getInventoryPort()).toString(),
      '/GetVendors',
      setVendors,
      setLoading,
    );
  };

  const getReasonCode = async () => {
    GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/reasoncodes/:Reason_Type',
      setReasonCodes,
      {Reason_Type: 8},
      false,
    );
  };
  const vendorOptions = vendors.map(vent => ({
    label: vent.Company,
    value: vent.Vendor_Number,
  }));

  const initialValues = {
    vendor: '',
    refnumber: '',
    date: getFormateDate(Date()) || '',
    shipvia: 'N/A',
  };

  const initialValuesReturn = {
    vendor: '',
    refnumber: '',
    reasonCodes: '',
    date: getFormateDate(Date()) || '',
    shipvia: 'N/A',
  };

  const createPurchaseOrder = async (Values: any) => {
    const getVendor = await GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getVendorDetails/:Vendor_Number',
      setVendor,
      {Vendor_Number: Values.vendor},
      false,
    );
    const providePoType: number =
      poType === 0 ? 0 : poType === 1 ? 1 : poType === 2 ? 2 : -1;
    const providePoStatus: string = poType === 0 ? 'O' : 'C';

    const POID = await GetCommonLatestID(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderID',
    );

    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const poItmes: Partial<PurchaseOrder> = {
      PO_Number: Number(POID),
      Store_ID: ValidStore,
      DateTime: Values.date,
      Reference: Values.refnumber,
      Vendor_Number: Values.vendor,
      Ship_Via: Values.shipvia,
      Status: providePoStatus,
      Cashier_ID: ValideCashier,
      Due_Date: Values.date,
      Last_Modified: Values.date,
      Cancel_Date: Values.date,
      POType: providePoType,
      Order_Reason: poType === 1 ? Values.reasonCodes : '',
      Print_Notes_On_PO: true,
      Dirty: true,
      ShipTo_1: getVendor[0]?.Company || '',
      ShipTo_2: getVendor[0]?.Address_1 || '',
      ShipTo_4: getVendor[0]?.City + ',' + getVendor[0]?.State || '',
    };

    // Apply default values to the invoice item
    const applyDefault = applyDefaultsPurchaseOrder(poItmes);

    const createResult = await createData<PurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/createpurchaseorder',
    });
    if (createResult) {
      if (poType === 1 || poType === 2) {
        navigation.navigate('ReturnToVendor', {
          ItemData: applyDefault,
          POTYPE: poType,
        });
      } else {
        navigation.navigate('PurchaseOrderItem', {ItemData: applyDefault});
      }
    }
  };

  const CreatePurchaseItems = async (Values: any) => {
    const POID = await GetCommonLatestID(
      (await getInventoryPort()).toString(),
      '/getpurchaseorderID',
    );

    let TotalCost = 0;
    for (const data of directPurchaseItems) {
      const totals = data.Quan_Ordered * data.CostPer;
      TotalCost += Number(totals); // Add to the existing total cost
    }

    const getVendor = await GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getVendorDetails/:Vendor_Number',
      setVendor,
      {Vendor_Number: Values.vendor},
      false,
    );

    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const poItmes: Partial<PurchaseOrder> = {
      PO_Number: Number(POID),
      Store_ID: ValidStore,
      DateTime: Values.date,
      Reference: Values.refnumber,
      Vendor_Number: Values.vendor,
      Ship_Via: Values.shipvia,
      Status: 'C',
      Cashier_ID: ValideCashier,
      Due_Date: Values.date,
      Last_Modified: Values.date,
      Cancel_Date: Values.date,
      POType: 2,
      Print_Notes_On_PO: true,
      Dirty: true,
      Terms: 'DSD',
      Total_Cost: TotalCost,
      Total_Cost_Received: TotalCost,
      ExpectedAmountToReceive: 0,
      ShipTo_1: getVendor[0]?.Company || '',
      ShipTo_2: getVendor[0]?.Address_1 || '',
      ShipTo_4: getVendor[0]?.City + ',' + getVendor[0]?.State || '',
    };

    const applyDefault = applyDefaultsPurchaseOrder(poItmes);

    const createResult = await createData<PurchaseOrder>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/createpurchaseorder',
    });

    if (createResult) {
      CreatePurchaseEach(Number(POID));
    }
  };

  const CreatePurchaseEach = async (PO_Number: number) => {
    for (const data of directPurchaseItems) {
      const poItmes: Partial<PurchaseOrderItems> = {
        PO_Number: PO_Number,
        ItemNum: data.ItemNum,
        Quan_Ordered: data.Quan_Ordered,
        Quan_Received: data.Quan_Ordered,
        CostPer: data.CostPer,
        Vendor_Part_Number: data.Vendor_Part_Number,
        CasePack: data.CasePack,
        Store_ID: data.Store_ID,
        destStore_ID: data.Store_ID,
        NumberPerCase: data.NumberPerCase,
      };

      const applyDefault = applyDefaultsPurchaseOrderItem(poItmes);
      const createResult = await createData<PurchaseOrderItems>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/createpodetails',
      });
      if (createResult) {
        AdjustInventory();
      }
    }
  };

  const AdjustInventory = async () => {
    for (const data of directPurchaseItems) {
      const getInventory = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: data.ItemNum},
      );

      const adjustStock =
        Number(data.Quan_Ordered) + Number(getInventory[0].In_Stock);
      const inventoryData: Partial<Inventory> = {
        ItemNum: getInventory[0]?.ItemNum,
        ItemName: getInventory[0]?.ItemName,
        Dept_ID: getInventory[0]?.Dept_ID,
        Cost: getInventory[0]?.Cost,
        Price: getInventory[0]?.Price,
        Retail_Price: getInventory[0]?.Retail_Price,
        In_Stock: adjustStock,
        Date_Created: getInventory[0]?.Date_Created,
        Last_Sold: getInventory[0]?.Last_Sold,
        Location: getInventory[0]?.Location,
        Vendor_Number: getInventory[0]?.Vendor_Number,
        Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
        Reorder_Level: getInventory[0]?.Reorder_Level,
        Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
        ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
        Unit_Size: getInventory[0]?.Unit_Size,
        Unit_Type: getInventory[0]?.Unit_Type,
        FoodStampable: getInventory[0]?.FoodStampable,
        Tax_1: getInventory[0]?.Tax_1[0],
        Tax_2: getInventory[0]?.Tax_2[0],
        Tax_3: getInventory[0]?.Tax_3[0],
        Tax_4: getInventory[0]?.Tax_4[0],
        Tax_5: getInventory[0]?.Tax_5[0],
        Tax_6: getInventory[0]?.Tax_6[0],
        Check_ID: getInventory[0]?.Check_ID,
        Check_ID2: getInventory[0]?.Check_ID2,
        Store_ID: getInventory[0]?.Store_ID,
        ItemName_Extra: getInventory[0]?.ItemName_Extra,
      };
      const applyDefault = applyDefaults(inventoryData);

      const result = await updateData<Inventory>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefault,
        endpoint: '/updatebarcode',
      });

      if (result) {
        const storeId = await AsyncStorage.getItem('STOREID');
        const ValidStore = storeId === null ? '1001' : storeId;
        const CashierID = await AsyncStorage.getItem('SWIPEID');
        const ValideCashier = CashierID === null ? '100101' : CashierID;

        const inventoryAdjustData: Partial<Inventory_In> = {
          ItemNum: getInventory[0].ItemNum,
          Store_ID: ValidStore,
          Quantity: adjustStock,
          DateTime: getFormateDate(Date()),
          Dirty: true,
          TransType: 'C',
          Description: 'ITEM CREATION',
          Cashier_ID: ValideCashier,
          CostPer: getInventory[0].Cost,
        };

        const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
        const invenIN = await createData<Inventory_In>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createinvetoryin',
        });
      }
    }
    Alert.alert('Direct Purchase Success');
    await AsyncStorage.removeItem('DIRECTPO');
    navigation.navigate('Home', {screen: 'More'});
  };

  const departmentOptions = reasonCodes.map(dept => ({
    label: dept.Reason_Code,
    value: dept.Reason_Code,
  }));

  const getVendorDetails = async (Vendor_Number: string) => {
    await GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/getVendorDetails/:Vendor_Number',
      setVendor,
      {Vendor_Number: Vendor_Number},
      false,
    );
  };
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    rootContainer: {
      backgroundColor: colors.background,
      justifyContent: 'space-between',
      flex: 1,
    },
    formContainer: {
      paddingHorizontal: wp('2.5%'),
      flex: 1,
    },
    formCard: {
      marginTop: hp('1.5%'),
      marginBottom: hp('2%'),
    },
    formTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
      color: colors.text,
      marginBottom: hp('1.5%'),
    },
    formFieldsContainer: {
      marginTop: hp('1%'),
    },
    formField: {
      marginBottom: hp('2%'),
    },
    datePickerContainer: {
      marginBottom: hp('2%'),
    },
    bottomButtonContainer: {
      backgroundColor: colors.surface,
      paddingVertical: hp('2%'),
      paddingHorizontal: wp('2.5%'),
      borderTopWidth: 1,
      borderTopColor: colors.border,
      elevation: 4,
      shadowColor: colors.shadow,
      shadowOffset: {width: 0, height: -1},
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 2,
    },
  });
  return (
    <Formik
      initialValues={poType === 1 ? initialValuesReturn : initialValues}
      enableReinitialize={true}
      validationSchema={
        poType === 1 ? validationSchemaReturn : validationSchema
      }
      onSubmit={values => {
        createPurchaseOrder(values);
      }}>
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        values,
        errors,
        touched,
      }) => (
        <View style={styles.rootContainer}>
          <View style={styles.formContainer}>
            <Header
              NavName={
                poType === 0
                  ? 'Create Purchase Order'
                  : poType === 1
                  ? 'Create Return'
                  : 'Create Direct Purchase'
              }
            />

            <View style={styles.formCard}>
              <View style={styles.formFieldsContainer}>
                <View style={styles.formField}>
                  <AppDropDown
                    label="Vendors"
                    options={vendorOptions}
                    selectedValue={values.vendor}
                    onSelect={value => {
                      setFieldValue('vendor', value);
                    }}
                    error={errors.vendor}
                    touched={touched.vendor}
                    isAdd={true}
                    onCreate={() => navigation.navigate('Vendors')}
                  />
                </View>

                <View style={styles.formField}>
                  <AppTextInput
                    PlaceHolder="Enter Reference Number"
                    Title="Reference Number"
                    Value={values.refnumber}
                    onChangeText={handleChange('refnumber')}
                    onBlur={handleBlur('refnumber')}
                    error={errors.refnumber}
                    touched={touched.refnumber}
                    maxLength={15}
                  />
                </View>

                {poType === 1 && (
                  <View style={styles.formField}>
                    <AppDropDown
                      label="Reason Codes"
                      options={departmentOptions}
                      selectedValue={values.reasonCodes}
                      onSelect={value => setFieldValue('reasonCodes', value)}
                      error={errors.reasonCodes}
                      touched={touched.reasonCodes}
                      isAdd={true}
                      onCreate={() => navigation.navigate('ReasonCode')}
                    />
                  </View>
                )}

                {(poType === 0 || poType === 1) && (
                  <View style={styles.datePickerContainer}>
                    <AppDatePicker
                      label="Select a Date"
                      value={selectedDate}
                      onDateChange={value => setFieldValue('date', value)}
                    />
                  </View>
                )}

                {(poType === 0 || poType === 1) && (
                  <View style={styles.formField}>
                    <AppTextInput
                      PlaceHolder="Enter Ship Via"
                      Title="Ship Via"
                      Value={values.shipvia}
                      onChangeText={handleChange('shipvia')}
                      onBlur={handleBlur('shipvia')}
                      error={errors.shipvia}
                      touched={touched.shipvia}
                    />
                  </View>
                )}
              </View>
            </View>
          </View>

          <View style={styles.bottomButtonContainer}>
            <FAB
              label={'Add Items'}
              position="bottomRight"
              onPress={handleSubmit}
            />
          </View>
        </View>
      )}
    </Formik>
  );
};

export default PurchaseOrderCreate;
