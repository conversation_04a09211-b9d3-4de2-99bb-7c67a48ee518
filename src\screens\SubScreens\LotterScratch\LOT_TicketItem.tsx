import React, {
  forwardRef,
  useState,
  useEffect,
  useImperativeHandle,
} from 'react';
import {ActivityIndicator, View} from 'react-native';
import TicketsDetails from '../../../components/LotteryScratch/TicketsDetails';
import {Inventory} from '../../../server/types';
import {
  GetCommonLatestID,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getLotteryPort} from '../../../server/InstanceTypes';

interface TicketItemProps {
  item: Inventory;
  index: number;
  isActive: boolean;
  isCompleted: boolean;
  isBadItem: boolean;
  isGoodItem: boolean;
  status: boolean;
  onPress: () => void;
  onSerialChange: () => void;
  onSkip: () => void;
  onResetStock: () => void;
  autoFocus?: boolean;
}

const TicketItem = forwardRef<any, TicketItemProps>((props, ref) => {
  const {
    item,
    index,
    isActive,
    isCompleted,
    isBadItem,
    isGoodItem,
    status,
    onPress,
    onSerialChange,
    onSkip,
    onResetStock,
    autoFocus = false,
  } = props;

  const [lastShiftMissing, setLastShiftMissing] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const inputRef = React.useRef<any>(null);

  const LastShiftMissing = async (item: Inventory) => {
    const Shift_ID = await GetCommonLatestID(
      (await getLotteryPort()).toString(),
      '/GetShiftID',
    );

    const getMissingTickets = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetGameDetails/:Shift_ID/:Location',
      {Shift_ID: Number(Shift_ID) - 1, Location: item.Location},
    );

    return getMissingTickets;
  };

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
  }));

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        const data = await LastShiftMissing(item);
        if (isMounted) {
          setLastShiftMissing(data);
          setIsLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [item]);

  useEffect(() => {
    if (isActive && autoFocus && inputRef.current && !isLoading) {
      // Small timeout to ensure component is fully rendered
      setTimeout(() => inputRef.current?.focus(), 50);
    }
  }, [isActive, autoFocus, isLoading]);

  if (isLoading) {
    return <ActivityIndicator style={{padding: 16}} />;
  }

  return (
    <TicketsDetails
      //   ref={ref}
      inputRef={inputRef}
      key={item.ItemNum}
      Item={item}
      Onpress={onPress}
      OnSerialChange={onSerialChange}
      Stock={item.In_Stock}
      Cost={Number(item?.Price)}
      IsActive={isActive}
      IsCompleted={isCompleted}
      isBadItem={isBadItem}
      isGoodItem={isGoodItem}
      status={status}
      IsLastMissings={
        lastShiftMissing?.[0]?.Missing_Ticket > 0 &&
        lastShiftMissing?.[0]?.Reset === 'Y'
      }
      MissingValue={
        lastShiftMissing?.[0]?.Missing_Ticket > 0 &&
        lastShiftMissing?.[0]?.Reset === 'Y'
          ? lastShiftMissing[0]?.Missing_Ticket
          : undefined
      }
      Skip={onSkip}
      ResetStock={onResetStock}
    />
  );
});

export default TicketItem;
