import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import AppButton from '../../../components/Inventory/AppButton'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'

type NavProps = {
    navigation: NativeStackNavigationProp<any>; 
  };
  
  const PurchaseOrderAddItem: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
      <Header NavName='Purchase order'/>

      <View style={{marginHorizontal: 10}}>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>Vender: test</Text>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>Refrence Number: 464556</Text>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>Due Date: 20/10/2024</Text>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>Ship Via: </Text>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>Total Item Ordered: 0</Text>
      </View>

      <View style={{marginHorizontal: 10, gap: 5, marginTop: 20}}>
        <AppButton Title='Scan Barcode'/>
        <AppButton Title='Lockup Item' OnPress={() => navigation.navigate("PickListItem")}/>
      </View>

      <View style={{marginHorizontal: 10, gap: 5, marginTop: 480}}>
      <AppButton Title='Create PO'  />
      </View>
    </View>
  )
}

export default PurchaseOrderAddItem