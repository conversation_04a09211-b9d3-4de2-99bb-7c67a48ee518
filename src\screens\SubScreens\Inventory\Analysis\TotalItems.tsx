import {View, Text} from 'react-native';
import React, {useCallback, useRef, useState} from 'react';
import {Backround, SecondaryHint} from '../../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Header from '../../../../components/Inventory/Header';
import DataList from '../../../../components/Inventory/AppList';
import {Inventory_Filter} from '../../../../server/types';
import ChoieItemCard from '../../../../components/Inventory/ChoieItemCard';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
} from '../../../../utils/PublicHelper';
import {getInventoryPort} from '../../../../server/InstanceTypes';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {Fonts} from '../../../../styles/fonts';
import Search from '../../../../components/Inventory/Search';
import {TextInput} from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeColors} from '../../../../Theme/useThemeColors';
import {useTheme} from '../../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'Home'>;

const TotalItems: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const colors = useThemeColors();
  const {isDark} = useTheme();
  useFocusEffect(
    useCallback(() => {
      invNoPage();
    }, []),
  );

  const invNoPage = async () => {
    if (route.params?.Type === 1) {
      GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/getInventoryFilter',
        setInventoryData,
        setFilteredData,
        setLoading,
        false,
      );
    }

    if (route.params?.Type === 2) {
      const data = await GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/inventorynopg',
        setInventoryData,
        setFilteredData,
        setLoading,
        false,
      );

      const filteredDataOutofStock = data.filter(item => item.In_Stock <= 0);
      setFilteredData(filteredDataOutofStock);
      setInventoryData(filteredDataOutofStock);
    }

    if (route.params?.Type === 3) {
      const data = await GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/inventorynopg',
        setInventoryData,
        setFilteredData,
        setLoading,
        false,
      );

      const filteredData = data.filter(
        item => item.In_Stock > 0 && item.In_Stock <= item.Reorder_Level,
      );

      setFilteredData(filteredData);
      setInventoryData(filteredData);
    }

    if (route.params?.Type === 4) {
      const data = await GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/inventorynopg',
        setInventoryData,
        setFilteredData,
        setLoading,
        false,
      );

      const filteredDataVendor = data.filter(
        item =>
          item.Vendor_Number === '' ||
          item.Vendor_Number === null ||
          item.Vendor_Number === undefined,
      );
      setFilteredData(filteredDataVendor);
      setInventoryData(filteredDataVendor);
    }
  };

  const totalPrice = filteredData.reduce((acc, item) => {
    if (item.Price !== undefined && item.In_Stock !== undefined) {
      return acc + item.Price * item.In_Stock;
    }
    return acc;
  }, 0);
  const renderItem = ({item}: {item: Inventory_Filter}) => (
    <ChoieItemCard
      Barcode={item.ItemNum}
      Price={item.Price}
      Title={item.ItemName}
      inStock={item.In_Stock}
      OnPress={() => route.params?.Type === 4 && EditVendor(item)}
    />
  );

  const EditVendor = async (Inventory: Inventory_Filter) => {
    const inventoryAdditional = await GetItemsParamsNoFilterNoReturn(
      (await getInventoryPort()).toString(),
      '/getInventoryAdditional/:ItemNum',
      {ItemNum: Inventory.ItemNum},
    );
    // navigation.navigate('Barcode', {
    //   ItemData: [Inventory],
    //   VENDORITEM: null,
    //   ADDITIONAL: inventoryAdditional,
    // });
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');

    if (lotteryDepartment) {
      if (lotteryDepartment === Inventory.Dept_ID) {
        navigation.navigate('Barcode', {
          ItemData: [Inventory],
          VENDORITEM: null,
          ADDITIONAL: inventoryAdditional,
          CANEDIT: false,
          ISCHOICE: true,
        });
      } else {
        navigation.navigate('Barcode', {
          ItemData: [Inventory],
          VENDORITEM: null,
          ADDITIONAL: inventoryAdditional,
          CANEDIT: true,
          ISCHOICE: true,
        });
      }
    } else {
      navigation.navigate('Barcode', {
        ItemData: [Inventory],
        VENDORITEM: null,
        ADDITIONAL: inventoryAdditional,
        CANEDIT: true,
        ISCHOICE: true,
      });
    }
  };

  const onSearchChange = (text: string) => {
    setSearchQuery(text);
    handleSearch(
      text,
      inventoryData,
      ['ItemNum', 'ItemName'],
      setFilteredData,
      setLoading,
    );
  };

  return (
    <View
      style={{
        backgroundColor: colors.background,
        height: hp('100%'),
        width: wp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header
        NavName={
          route.params?.Type === 1
            ? 'Total Active Items'
            : route.params?.Type === 2
            ? 'Total Out of Stocks'
            : route.params?.Type === 3
            ? 'Total Low Stock Items'
            : 'Total Missing Vendor Items'
        }
      />
      <View style={{paddingHorizontal: 8}}>
        <Search
          textInputRef={textInputRef}
          PlaceHolder="Search..."
          Value={searchQuery}
          onChange={onSearchChange}
          keyboardON={true}
        />

        {route.params?.Type === 1 && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 10,
              paddingVertical: hp('1%'),
            }}>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: hp('1.8%'),
                color: colors.textSecondary,
              }}>
              Total Items: {filteredData.length}
            </Text>

            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: hp('1.8%'),
                color: colors.textSecondary,
              }}>
              Total Values: ${totalPrice.toFixed(2)}
            </Text>
          </View>
        )}
        <View
          style={
            route.params?.Type === 1 ? {height: hp('79%')} : {height: hp('83%')}
          }>
          <View style={{height: hp('73%')}}>
            <DataList
              data={filteredData}
              renderItem={renderItem}
              loading={loading}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default TotalItems;
