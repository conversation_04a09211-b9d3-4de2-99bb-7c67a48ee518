import {
  View,
  Text,
  TextInput,
  Alert,
  TouchableOpacity,
  Modal,
} from 'react-native';
import React, {useState} from 'react';
import Header from '../../../components/Inventory/Header';
import {Inventory, Inventory_In} from '../../../server/types';
import {RouteProp} from '@react-navigation/native';
import AppButton from '../../../components/Inventory/AppButton';
import {
  createData,
  formatNumber,
  getFormateDate,
  GetItemsParamsNoFilter,
  updateData,
} from '../../../utils/PublicHelper';
import {Game_Details} from '../../../Types/Lottery/Lottery_Types';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {getLotteryPort, getInventoryPort} from '../../../server/InstanceTypes';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import AppFocus from '../../../components/Inventory/AppFocus';
import Feather from 'react-native-vector-icons/Feather';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import {Backround, Primary} from '../../../constants/Color';
import AsyncStorage from '@react-native-async-storage/async-storage';

type BarcodeScreenRouteProp = RouteProp<any, 'ResetShiftEach'>;
const LOT_Reset_Each_Shift: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [scratchGame, setScratchGame] = useState<Inventory>(
    route.params?.ItemData || [],
  );
  const [isConfirm, setIsConfirm] = useState<boolean>(true);
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [gameDetails, setGameDetails] = useState<Game_Details[]>([]);
  const [bookQty, setBookQty] = useState<number>(
    route.params?.ItemData?.In_Stock,
  );
  const [camera, setCamera] = useState<boolean>(false);

  const ResetShift = async () => {
    try {
      if (!currentBarcode || currentBarcode === '' || currentBarcode === null) {
        Alert.alert('Must Be Scan the Book Number');
      } else {
        const shiftBookDetails = await GetItemsParamsNoFilter(
          (await getLotteryPort()).toString(),
          '/GetShiftBookTickets/:ItemNum/:Location',
          setGameDetails,
          {ItemNum: scratchGame.ItemNum, Location: scratchGame.Location},
        );
        console.log('Log 1 ', shiftBookDetails);

        let endGameSerial = currentBarcode.split('-');

        console.log('Log 2', endGameSerial);

        const MainStockAdjustment =
          Number(shiftBookDetails[0]?.Book_Tickets) - Number(endGameSerial[2]);

        console.log('Log 3', MainStockAdjustment);

        const inventoryData: Partial<Inventory> = {
          ItemNum: scratchGame.ItemNum,
          ItemName: scratchGame.ItemName,
          Dept_ID: scratchGame.Dept_ID,
          Cost: scratchGame.Cost,
          Price: scratchGame.Price,
          Retail_Price: scratchGame.Retail_Price,
          In_Stock: Number(MainStockAdjustment),
          Date_Created: scratchGame.Date_Created,
          Last_Sold: scratchGame.Last_Sold,
          Location: scratchGame.Location,
          Vendor_Number: scratchGame.Vendor_Number,
          Vendor_Part_Num: scratchGame.Vendor_Part_Num,
          Reorder_Level: scratchGame.Reorder_Level,
          Reorder_Quantity: scratchGame.Reorder_Quantity,
          ReOrder_Cost: scratchGame.ReOrder_Cost,
          Unit_Size: scratchGame.Unit_Size,
          Unit_Type: scratchGame.Unit_Type,
          FoodStampable: scratchGame.FoodStampable,
          Tax_1: scratchGame.Tax_1[0],
          Tax_2: scratchGame.Tax_2[0],
          Tax_3: scratchGame.Tax_3[0],
          Tax_4: scratchGame.Tax_4[0],
          Tax_5: scratchGame.Tax_5[0],
          Tax_6: scratchGame.Tax_6[0],
          Check_ID: scratchGame.Check_ID,
          Check_ID2: scratchGame.Check_ID2,
          Store_ID: scratchGame.Store_ID,
          ItemName_Extra: scratchGame.ItemName_Extra,
        };

        const applyDefault = applyDefaults(inventoryData);
        console.log('Log 4 ', applyDefault);

        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });

        const inventoryin =
          Number(scratchGame.In_Stock) - Number(MainStockAdjustment);
        if (result) {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const CashierID = await AsyncStorage.getItem('SWIPEID');
          const ValideCashier = CashierID === null ? '100101' : CashierID;

          const inventoryAdjustData: Partial<Inventory_In> = {
            ItemNum: scratchGame.ItemNum,
            Store_ID: ValidStore,
            Quantity: '-' + inventoryin,
            DateTime: getFormateDate(Date()),
            Dirty: true,
            TransType: 'L',
            Description: 'LOTTERY SCRATCH',
            Cashier_ID: ValideCashier,
            CostPer: scratchGame.Cost,
          };
          const applyDefault =
            applyDefaultsInventoryAdjust(inventoryAdjustData);
          const result = await createData<Inventory_In>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createinvetoryin',
          });

          if (result) {
            setBookQty(Number(MainStockAdjustment));
            Alert.alert('Shift Reset Success!');
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        setCurrentBarcode(codes[0].value);
        setCamera(false);
      } else {
      }
    },
  });

  return (
    <View
      style={{
        backgroundColor: Backround,
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Adjust Shift" />
      <View>
        <View>
          <Text style={{fontSize: 18}}>
            Game Name #: {scratchGame?.ItemName}
          </Text>
          <Text style={{fontSize: 18}}>Current Stock #: {bookQty}</Text>
        </View>

        {false && (
          <AppFocus
            PlaceHolder="Scan Book Number"
            Title="Scan Book Number"
            onChangeText={value => {
              const formattedSerial = formatNumber(value);
              setCurrentBarcode(formattedSerial);
            }}
          />
        )}

        {/* <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            padding: 10,
            width: '100%',
          }}>
          <TextInput
            placeholder="Scan barcode"
            editable={isConfirm}
            onChangeText={value => setCurrentBarcode(value)}
            value={currentBarcode}
            style={{
              borderColor: 'red',
              borderRadius: 10,
              borderWidth: 2,
              fontSize: 18,
              width: 275,
            }}
          />

          <View style={{marginLeft: 10}}>
            <AppButton Title="Scan" OnPress={() => setCamera(true)} />
          </View>
        </View> */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingVertical: hp('5%'),
          }}>
          <View style={{width: wp('80%'), marginTop: 20}}>
            <AppFocus
              PlaceHolder="Scan Book Number"
              Title="Scan Book Number"
              onChangeText={value => {
                const formattedSerial = formatNumber(value);
                setCurrentBarcode(formattedSerial);
              }}
            />
          </View>
          <TouchableOpacity
            onPress={() => setCamera(!camera)}
            style={{
              width: wp('12%'),
              height: hp('5%'),
              marginTop: hp('1%'),
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Primary,
              borderRadius: 5,
            }}>
            <Feather name="camera" color={Backround} size={hp('3.5%')} />
          </TouchableOpacity>
        </View>
        {currentBarcode.length > 0 && (
          <View
            style={{
              backgroundColor: 'yellow',
              marginVertical: 20,
              justifyContent: 'space-between',
              flexDirection: 'row',
              padding: 15,
              alignItems: 'center',
            }}>
            <Text>{currentBarcode}</Text>
            <TouchableOpacity onPress={() => setCurrentBarcode('')}>
              <MaterialCommunityIcons name="delete" color={'#000'} size={30} />
            </TouchableOpacity>
          </View>
        )}
        <View style={{marginTop: 20}}>
          <AppButton
            Title={'Reset Shift'}
            OnPress={() => ResetShift()}
            //disabled={currentBarcode === '' ? true : false}
          />
        </View>
      </View>
      <Modal
        animationType="slide"
        transparent={true}
        visible={camera}
        onRequestClose={() => setCamera(!camera)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner codeScanner={codeScanner} />
        </View>
      </Modal>
    </View>
  );
};

export default LOT_Reset_Each_Shift;
