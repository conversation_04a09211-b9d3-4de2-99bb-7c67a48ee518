import React, {useCallback, useState} from 'react';
import {View, TouchableWithoutFeedback, Modal, StyleSheet} from 'react-native';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {useCodeScanner} from 'react-native-vision-camera';

// Components
import Header from '../../../components/Inventory/Header';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppScanner from '../../../components/Inventory/AppScanner';
import {
  CountItemList,
  CountItemModal,
  CountItemHeader,
  CountItemActions,
} from '../../../components/Inventory/CountItem';

// Hooks
import {
  useCountItemData,
  useCountItemFilters,
  useCountItemSearch,
  useCountItemModal,
} from '../../../hooks/inventory';

// Utils
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';

type CountItemRouteProp = RouteProp<any, 'AdjustStock'>;

const CountItem: React.FC<{
  route: CountItemRouteProp;
  navigation: any;
}> = ({navigation}) => {
  const colors = useThemeColors();
  const [appLoader, setAppLoader] = useState<boolean>(true);
  const [filter, setFilter] = useState<boolean>(false);

  // Data management hook
  const {
    masterData,
    filteredData,
    displayData,
    loading,
    initialLoading,
    refreshing,
    action,
    inputValues,
    actionRef,
    setFilteredData,
    setLoading,
    applyPagination,
    handleLoadMore,
    fetchInventoryData,
    handleRefresh,
    handleInputChange,
    updateStockInData,
    savePhysicalCount,
    clearCountData,
  } = useCountItemData();

  // Search functionality hook
  const {
    searchQuery,
    debouncedQuery,
    showLookup,
    camera,
    setCamera,
    textInputRef,
    onSearchChange,
    toggleLookup,
    handleDoneClickSub,
    handleOutsidePress,
    focusTextInput,
    clearSearchAndFocus,
  } = useCountItemSearch(
    masterData,
    filteredData,
    1, // page
    action,
    applyPagination,
    setFilteredData,
    setLoading,
    (itemNum: string) => openModalForItem(itemNum),
    navigation,
  );

  // Modal functionality hook
  const {
    modalVisible,
    modalStockInput,
    openModalForItem,
    handleModalInputChange,
    handleDoneClick,
    closeModal,
  } = useCountItemModal(
    actionRef,
    handleInputChange,
    updateStockInData,
    clearSearchAndFocus,
  );

  // Filters hook
  const {
    selectedDepartment,
    setSelectedDepartment,
    selectedVendor,
    setSelectedVendor,
    selectedBrand,
    setSelectedBrand,
    selectedSubCategory,
    setSelectedSubCategory,
    isEnableFilter,
    setIsEnableFilter,
  } = useCountItemFilters(
    masterData,
    inputValues,
    debouncedQuery,
    setFilteredData,
    applyPagination,
  );

  // Initialize data on screen focus
  useFocusEffect(
    useCallback(() => {
      fetchInventoryData();
      setFilter(false);
      focusTextInput();
    }, [fetchInventoryData, focusTextInput]),
  );

  // Handle item press
  const handleItemPress = useCallback(
    async (item: any) => {
      console.log('handleItemPress called with item:', item.ItemNum);
      await openModalForItem(item.ItemNum, item.Dept_ID);
    },
    [openModalForItem],
  );

  // Handle cancel action
  const handleCancel = useCallback(() => {
    clearCountData();
  }, [clearCountData]);

  // Code scanner for barcode scanning
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onSearchChange(codes[0].value || '');
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingHorizontal: wp('2.5%'),
      marginBottom: 5,
    },
  });

  return (
    <>
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View style={styles.container}>
          {/* Header Section */}
          <Header
            NavName="Count Items"
            isOption={true}
            Options={() => {
              clearSearchAndFocus();
              setCamera(!camera);
            }}
          />

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <AppSearchWIthFilter
              OnSearch={onSearchChange}
              SearchValue={searchQuery}
              OnSearchSet={() => setFilter(true)}
              isEnableFilter={isEnableFilter}
              Keyboardon={showLookup}
              textInputRef={textInputRef}
              onToggleLookup={toggleLookup}
              OnSubmitEditing={handleDoneClickSub}
            />
          </View>

          {/* Available Items Header */}
          <CountItemHeader itemCount={filteredData.length} />

          {/* List Container */}
          <CountItemList
            displayData={displayData}
            inputValues={inputValues}
            actionRef={actionRef}
            loading={loading}
            initialLoading={initialLoading}
            refreshing={refreshing}
            searchQuery={searchQuery}
            appLoader={appLoader}
            setAppLoader={setAppLoader}
            onLoadMore={handleLoadMore}
            onRefresh={handleRefresh}
            onItemPress={handleItemPress}
            onOutsidePress={handleOutsidePress}
          />

          {/* FAB and Action Buttons */}
          <CountItemActions
            action={action}
            onStartFinish={savePhysicalCount}
            onCancel={handleCancel}
          />

          {/* Filter Modal */}
          <AppFilter
            isVisible={filter}
            setIsVisble={setFilter}
            Department={setSelectedDepartment}
            Vedor={setSelectedVendor}
            Brand={setSelectedBrand}
            Category={setSelectedSubCategory}
            EnableFilter={setIsEnableFilter}
            selectedDepartment={selectedDepartment}
            selectedVendor={selectedVendor}
            selectedBrand={selectedBrand}
            selectedSubCategory={selectedSubCategory}
            isEnableFilter={isEnableFilter}
          />

          {/* Camera Scanner Modal */}
          <Modal
            animationType="fade"
            transparent={false}
            visible={camera}
            onRequestClose={() => setCamera(false)}>
            <AppScanner
              codeScanner={codeScanner}
              onClose={() => {
                setCamera(false);
                textInputRef?.current?.focus();
              }}
            />
          </Modal>
        </View>
      </TouchableWithoutFeedback>

      {/* Modal for Stock Input - Outside TouchableWithoutFeedback */}
      <CountItemModal
        visible={modalVisible}
        stockInput={modalStockInput}
        onInputChange={handleModalInputChange}
        onDone={handleDoneClick}
        onClose={closeModal}
      />
    </>
  );
};

export default CountItem;
