import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import AssignBookCart from './AssignBookCart';
import Search from '../Inventory/Search';
import AppTextInput from '../Inventory/AppTextInput';
import AppButton from '../Inventory/AppButton';
import SearchBar from './SearchBar';


const showAlert = (message: string, area: string) => {
  Alert.alert(
    area,
    message, // Optional message
    [
      {
        text: "No",
        onPress: () =>{
          console.log("Pressed");
          
           },
      },
      {
        text: "Yes",
        onPress: () => {
          console.log("pres")
          },
      },
    ],
    { cancelable: true } // Optional: Allows closing the alert by tapping outside
  );
};
// Define screen components
function HomeScreen() {
  return (
    <View style={styles.screen}>
      <AssignBookCart Onpress={() => showAlert("Are you sure you want to remove?", "Confirmaiton")}/>
      <AssignBookCart />
      <AssignBookCart />
      <AssignBookCart />
    </View>
  );
}

function ProfileScreen() {
  return (
    <View style={styles.screen}>
      <View>
        <SearchBar />
          <View style={{flexDirection: 'row', alignItems: 'center', width: '100%', }}> 
            <View style={{ width: '70%', paddingHorizontal: 10}}>
              <AppTextInput PlaceHolder='Search Game #'/>
            </View>


            <View style={{ width: '30%'}}>
            <AppButton Title='Clear'/>
            </View>
            
          </View>

          <AssignBookCart />
          <AssignBookCart />
          <AssignBookCart />
          <AssignBookCart />
      </View>
    </View>
  );
}


// Main component that handles the tab navigation
export default function TopTabNavigation() {
  const [selectedTab, setSelectedTab] = useState('Home');

  // Function to handle tab change
  const renderTabContent = () => {
    switch (selectedTab) {
      case 'Home':
        return <HomeScreen />;
      case 'Profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Custom Tab Bar */}
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabButton, selectedTab === 'Home' && styles.activeTab]}
          onPress={() => setSelectedTab('Home')}
        >
          <Text style={selectedTab === 'Home' ? styles.activeText : styles.inactiveText}>
            Manual
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, selectedTab === 'Profile' && styles.activeTab]}
          onPress={() => setSelectedTab('Profile')}
        >
          <Text style={selectedTab === 'Profile' ? styles.activeText : styles.inactiveText}>
            Scan
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content Area */}
      <View style={{ flex: 1 }}>
        {renderTabContent()}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  screen: {
    marginTop: 80,
    justifyContent: 'center',
    paddingHorizontal: 20,
    gap: 20
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    height: 60,
    borderBottomWidth: 1,
    borderColor: '#ccc',
    justifyContent: 'space-around',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    width: '100%',
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: 'tomato',
  },
  activeText: {
    color: 'tomato',
    fontWeight: 'bold',
  },
  inactiveText: {
    color: 'gray',
  },
});
