import {Alert} from 'react-native';
import {
  createItem,
  fetchAllItems,
  fetchMultiPlartform,
  fetchSingleItem,
  updateItem,
} from '../server/service';
import {
  AdditionalInfo,
  Bump_Bar_Settings,
  Department,
  Get_Max_ID,
  Get_Value,
  Inventory,
  Inventory_Filter,
  Inventory_In,
  Inventory_Reference,
  InventoryVendor,
  PurchaseOrder,
  Setup_TS_Buttons,
  VendorItem,
} from '../server/types';
import {
  applyDefaults,
  applyDefaultsAddition,
  applyDefaultsInventoryAdjust,
  applyDefaultsInventoryVendor,
  applyDefaultsSetupTSButtons,
} from '../Validator/Inventory/Barcode';
import {getInventoryPort} from '../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const RefreshStockDetails = async (
  baseURL: string,
  itemNum: string,
  setStockData: (data: Inventory[]) => void,
) => {
  try {
    // Call API to get the refreshed stock details
    const getRefreshedDetails = await fetchSingleItem<Inventory[]>(
      baseURL,
      '/inventory',
      itemNum,
    );

    if (!getRefreshedDetails || getRefreshedDetails.length === 0) {
      // If no data found, show an alert
    } else {
      // If data found, update the stock data
      setStockData(getRefreshedDetails);
    }
  } catch (error) {
    // Handle any errors during fetch
    console.error('Error reading barcode:', error);
    Alert.alert('Error reading barcode');
  }
};

export const GetItemsWithParams = async <T>(
  baseURL: string,
  endpoint: string,
  setData: (data: T) => void,
  setDataFilter: (data: T) => void,
  setLoading: (data: boolean) => void,
  params: object = {},
): Promise<T | undefined> => {
  try {
    setLoading(true);
    const result = await fetchMultiPlartform<T>(baseURL, endpoint, params);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      setData([] as T);
      setLoading(false);
      return [] as T;
    } else {
      setData(result);
      setDataFilter(result);
      setLoading(false);
      return result;
    }
  } catch (error) {
    setLoading(false);
    console.error('Error reading barcode:', error);
    return undefined;
  }
};

export const GetItemsParamsNoFilter = async <T>(
  baseURL: string,
  endpoint: string,
  setData: (data: T) => void,
  params: object = {},
  IsNotArray?: boolean,
): Promise<T | undefined> => {
  try {
    const result = await fetchMultiPlartform<T>(baseURL, endpoint, params);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      setData([] as T);
      return [] as T;
    } else {
      if (IsNotArray) {
        setData(result[0]);
        return result[0];
      } else {
        setData(result);
        return result;
      }
    }
  } catch (error) {
    console.error('Error reading barcode:', error);
    return undefined;
  }
};

export const GetItemsParamsNoFilterNoReturn = async <T>(
  baseURL: string,
  endpoint: string,
  params: object = {},
  IsNotArray?: boolean,
): Promise<T | undefined> => {
  try {
    const result = await fetchMultiPlartform<T>(baseURL, endpoint, params);
    if (!result || (Array.isArray(result) && result.length === 0)) {
      return [] as T;
    } else {
      if (IsNotArray) {
        return result[0];
      } else {
        return result;
      }
    }
  } catch (error) {
    console.error('Error reading barcode:', error);
    return undefined;
  }
};

export const GetItemsVerification = async <T>(
  baseURL: string,
  endpoint: string,
  params: object = {},
): Promise<boolean> => {
  try {
    const result = await fetchMultiPlartform<T>(baseURL, endpoint, params);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      return false;
    } else {
      return true;
    }
  } catch (error) {
    console.error('Error reading barcode:', error);
    return false;
  }
};

export const GetAllItemsWithFilter = async <T>(
  baseURL: string,
  endpoint: string,
  setData: (data: T) => void,
  setDataFilter: (data: T) => void,
  setLoading: (data: boolean) => void,
  IsPagination?: boolean,
): Promise<T | undefined> => {
  try {
    setLoading(true);
    const result = await fetchAllItems<T>(baseURL, endpoint);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      setLoading(false);
      setData([] as T);
      setDataFilter([] as T);
      return [] as T;
    } else {
      if (IsPagination) {
        setLoading(false);
        setData(result?.data);
        setDataFilter(result?.data);
        return result?.data;
      } else {
        setLoading(false);
        setData(result);
        setDataFilter(result);
        return result;
      }
    }
  } catch (error) {
    setLoading(false);
    console.error('Error fetching items:', error);
    return undefined;
  }
};

export const GetAllItems = async <T>(
  baseURL: string,
  endpoint: string,
  setData: (data: T) => void,
  setLoading: (data: boolean) => void,
  IsPagination?: boolean,
): Promise<T | undefined> => {
  try {
    setLoading(true);
    const result = await fetchAllItems<T>(baseURL, endpoint);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      setLoading(false);
      setData([] as T);
      return [] as T;
    } else {
      if (IsPagination) {
        setData(result?.data);
      } else {
        setData(result);
      }
      setLoading(false);
    }
  } catch (error) {
    setLoading(false);
    console.error('Error fetching items:', error);
    return undefined;
  }
};

export const GetAllItemsNoProps = async <T>(
  baseURL: string,
  endpoint: string,
): Promise<T | undefined> => {
  try {
    const result = await fetchAllItems<T>(baseURL, endpoint);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      return undefined;
    } else {
      return result;
    }
  } catch (error) {
    console.error('Error fetching items:', error);
    //Alert.alert(`'Network Error!', ${error}`);
  }
};
export const GetLatestID = async (
  baseURL: string,
  setData: (data: Get_Max_ID[]) => void,
  Url: string,
): Promise<number | undefined> => {
  // Specify that the return type can be a number or undefined

  try {
    // Call API to get the refreshed stock details
    const getLatestID = await fetchAllItems<Get_Max_ID[]>(baseURL, Url);

    if (!getLatestID || getLatestID === null || getLatestID === undefined) {
      // If no data found, show an alert

      return undefined; // If data is not found, return undefined
    } else {
      // If data found, update the stock data and return the MaxValue
      const maxValue = getLatestID[0]?.MaxValue;
      setData(getLatestID);
      return maxValue; // Return the MaxValue as a number
    }
  } catch (error) {
    // Handle any errors during fetch
    console.error('Error reading barcode:', error);
    Alert.alert('Error reading barcode');
    return undefined; // Return undefined if an error occurs
  }
};

export const GetCommonLatestID = async (
  baseURL: string,
  Url: string,
): Promise<number | undefined> => {
  try {
    const getLatestID = await fetchAllItems<Get_Max_ID[]>(baseURL, Url);

    if (!getLatestID || getLatestID === null || getLatestID === undefined) {
      return undefined;
    } else {
      const maxValue = getLatestID[0]?.MaxValue;

      return maxValue;
    }
  } catch (error) {
    console.error('Error reading barcode:', error);
    Alert.alert('Error reading barcode');
    return undefined;
  }
};

// Get next LineNum for an invoice
export const getNextLineNum = async (
  baseURL: string,
  invoiceNumber: number,
): Promise<number> => {
  try {
    const result = await GetItemsParamsNoFilterNoReturn<{NextLineNum: number}>(
      baseURL,
      '/getNextLineNum/:Invoice_Number',
      {Invoice_Number: invoiceNumber},
      true,
    );

    if (result && result.NextLineNum) {
      return result.NextLineNum;
    } else {
      // If no existing lines, start with 1
      return 1;
    }
  } catch (error) {
    console.error('Error getting next LineNum:', error);
    // Fallback to 1 if there's an error
    return 1;
  }
};

export const GetCount = async (
  baseURL: string,
  Url: string,
): Promise<number | undefined> => {
  try {
    const getLatestID = await fetchAllItems<Get_Value[]>(baseURL, Url);

    if (!getLatestID || getLatestID === null || getLatestID === undefined) {
      return undefined;
    } else {
      const maxValue = getLatestID[0]?.TotalDamaged;

      return maxValue;
    }
  } catch (error) {
    console.error('Error reading barcode:', error);
    Alert.alert('Error reading barcode');
    return undefined;
  }
};
export const createData = async <T>({
  baseURL,
  data,
  endpoint,
}: {
  baseURL: string;
  data: Partial<T>;
  endpoint: string;
}): Promise<boolean> => {
  try {
    const result = await createItem(baseURL, endpoint, data);
    if (!result || result === 0 || result.length === 0) {
      return false;
    } else {
      return true;
    }
  } catch (error) {
    console.log(error);
    Alert.alert('Error', 'There was an issue creating the barcode.');
    return false;
  }
};

export const updateData = async <T>({
  baseURL,
  data,
  endpoint,
}: {
  baseURL: string;
  data: Partial<T>;
  endpoint: string;
}) => {
  try {
    const result = await updateItem(baseURL, endpoint, data);

    if (result?.message) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

type SearchableItem = {
  [key: string]: any;
};

export const handleSearch = (
  text: string,
  dataset: SearchableItem[],
  searchKeys: string[],
  setFilteredData: React.Dispatch<React.SetStateAction<SearchableItem[]>>,
  setLoading: (data: boolean) => void,
) => {
  setLoading(true);
  if (text === '') {
    setFilteredData(dataset);
    setLoading(false);
  } else {
    const filteredItems = dataset.filter(item =>
      searchKeys.some(key =>
        item[key]?.toString().toLowerCase().includes(text.toLowerCase()),
      ),
    );
    setFilteredData(filteredItems);
    setLoading(false);
  }
};

export const getCurrentDateTime = (): Date => {
  const now = new Date();

  // Pad single digits with leading zeros if necessary
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const milliseconds = now.getMilliseconds().toString().padStart(3, '0');
  const current = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  return new Date(current);
};

export const showAlert = (
  message: string,
  Confirmation?: string,
  isProvide?: boolean,
  NoMessage?: string,
  YesMessage?: string,
) => {
  return new Promise((resolve, reject) => {
    Alert.alert(
      Confirmation ? Confirmation : 'Confirmation!',
      message,
      [
        {
          text: isProvide ? NoMessage : 'No',
          onPress: () => {
            resolve(false);
          },
        },
        {
          text: isProvide ? YesMessage : 'Yes',
          onPress: () => {
            resolve(true);
          },
        },
      ],
      {cancelable: false},
    );
  });
};

export const showAlertOK = (
  message: string,
  AlertConfirmation?: string,
  OkayMessage?: string,
  onOkPress?: () => void,
) => {
  return new Promise((resolve, reject) => {
    Alert.alert(
      AlertConfirmation || 'Confirmation!',
      message,
      [
        {
          text: OkayMessage || 'OK',
          onPress: () => {
            if (onOkPress) {
              onOkPress(); // Call the provided function when OK is pressed
            }
            resolve(true);
          },
        },
      ],
      {cancelable: false},
    );
  });
};

export const showAlertMulti = (
  confirmation: string,
  message: string,
  option: string,
  YesMessage?: string,
  NoMessage?: string,
) => {
  return new Promise<string>((resolve, reject) => {
    Alert.alert(
      confirmation,
      message,
      [
        {
          text: YesMessage || 'Yes',
          onPress: () => {
            console.log('Yes Pressed');
            resolve('Yes');
          },
        },

        {
          text: NoMessage || 'No',
          onPress: () => {
            console.log('No Pressed');
            resolve('No');
          },
        },

        {
          text: option,
          onPress: () => {
            console.log('Cancel Pressed');
            resolve('cancel');
          },
          style: 'cancel',
        },
      ],
      {cancelable: false}, // This prevents dismissing the alert by tapping outside of it
    );
  });
};

export const removeData = async <T>(
  endpoint: string,
  params: object,
  listData: any[], // The current list of items
  setListData: (data: any[]) => void, // Function to update the list
  showAlert: (message: string) => Promise<boolean>, // Function to show alert
  deleteItem: (endpoint: string, params: object) => Promise<any>, // API call to delete item
): Promise<boolean> => {
  try {
    // Show the confirmation alert
    const result = await showAlert('Are you sure you want to delete?');

    if (result) {
      // Make the API call to delete the item
      const deleteResponse = await deleteItem(endpoint, params);

      if (deleteResponse.success) {
        // If the delete was successful, update the list by removing the item
        const updatedList = listData.filter(
          item => item.ItemNum !== params.ItemNum,
        );
        setListData(updatedList);

        return true; // Return success
      } else {
        console.error('Failed to delete item');
        return false; // Return failure if the deletion was not successful
      }
    } else {
      return false; // Return false if the user cancels the action
    }
  } catch (error) {
    // Handle errors
    console.error('Error deleting item', error);
    return false; // Return failure if an error occurs
  }
};

export const getFormateDate = (date: string): string => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  const milliseconds = String(d.getMilliseconds()).padStart(3, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

//barcode creation processdsfsdf

export const createInventoryRef = async (baseURL: string, ItemNum: string) => {
  const invRefID = await GetCommonLatestID(baseURL, '/getinventoryrefid');
  const InventoryRef: Inventory_Reference = {
    ID: Number(invRefID),
    ItemNum: ItemNum,
    Store_ID: '1001',
  };
  const result = await createItem(baseURL, '/createinventoryref', InventoryRef);
};

export const createAdditionalInfo = async (
  baseURL: string,
  ItemNum: string,
) => {
  const AdditionalInfo: Partial<AdditionalInfo> = {
    Store_ID: '1001',
    ItemNum: ItemNum,
  };
  const inventoryWithDefaults = applyDefaultsAddition(AdditionalInfo);
  const result = await createItem(
    baseURL,
    '/createinventoryaddit',
    inventoryWithDefaults,
  );
};

export const truncateNumber = (number: number): string => {
  let numberString = number.toString();
  if (numberString.length > 6) {
    return numberString.slice(0, 6) + '...';
  }
  return numberString;
};
export const createSetupTSButton = async (
  baseURL: string,
  ItemNum: string,
  ItemName: string,
) => {
  try {
    const setupTSID = await GetCommonLatestID(baseURL, '/getinventorysetupid');
    const SetupTSButton: Setup_TS_Buttons = {
      Store_ID: '1001',
      Index: setupTSID,
      Caption: ItemName,
      Option1: '',
      BackColor: 12632256,
      Ident: ItemNum,
    };

    const inventoryWithDefaults = applyDefaultsSetupTSButtons(SetupTSButton);
    const result = await createItem(
      baseURL,
      '/createsetupts',
      inventoryWithDefaults,
    );
  } catch (error) {
    console.log(error);
  }
};

export const createInventoryVendors = async (
  baseURL: string,
  ItemNum: string,
  VendorNumber: string,
  Cost: number,
) => {
  try {
    const InventoryVendor: Partial<InventoryVendor> = {
      ItemNum: ItemNum,
      Store_ID: '1001',
      Vendor_Number: VendorNumber,
      CostPer: Cost,
    };

    const inventoryWithDefaults = applyDefaultsInventoryVendor(InventoryVendor);
    const result = await createItem(
      baseURL,
      '/createinvedors',
      inventoryWithDefaults,
    );
  } catch (error) {
    console.log(error);
  }
};

export const createBumpBarSettings = async (
  baseURL: string,
  ItemNum: string,
) => {
  const BumpBarSettings: Bump_Bar_Settings = {
    Store_ID: '1001',
    ItemNum: ItemNum,
    Backcolor: 12632256,
    Forecolor: 0,
  };

  const result = await createItem(baseURL, '/createbumbar', BumpBarSettings);
};

export function formatNumber(input: string): string {
  const part1 = input.slice(0, 3);
  const part2 = input.slice(4, 10);
  const part3 = input.slice(10, 13);

  // Combine parts with hyphens
  const formattedNumber = `${part1}-${part2}-${part3}`;

  return formattedNumber;
}
export const calculatePriceWithVAT1 = (
  priceWithoutVAT: number,
  Vat: number,
) => {
  const priceWithVAT = priceWithoutVAT * (1 + Vat);
  return priceWithVAT.toFixed(2);
};

export const calculateVAT1 = (priceWithoutVAT: number) => {
  const vatRate = 0.07;
  const priceWithVAT = priceWithoutVAT * (1 + vatRate);
  return priceWithVAT.toFixed(2);
};

export const calculateVATAmount = (price: number, vatRate: number) => {
  const vatAmount = price * vatRate;
  return vatAmount.toFixed(2);
};

export const findArrayDifference = (
  arr1: Inventory[],
  arr2: Inventory[],
): Inventory[] => {
  return arr1.filter(item => !arr2.includes(item));
};

export const findDifference = <T extends {ItemNum: string}>(
  arr1: T[],
  arr2: T[],
): T[] => {
  return arr1.filter(
    item1 => !arr2.some(item2 => item2.ItemNum === item1.ItemNum),
  );
};

export const findDifferenceCommon = <T extends {AltSKU: string}>(
  arr1: T[],
  arr2: T[],
): T[] => {
  return arr1.filter(
    item1 => !arr2.some(item2 => item2.AltSKU === item1.AltSKU),
  );
};

export const onSearchChange_Common = async (
  text: string,
  inventoryData: Inventory_Filter[], // You can refine the type if needed
  setFilteredData: (data: Inventory_Filter[]) => void,
  setLoading: (loading: boolean) => void,
  setSearchQuery: (query: string) => void,
  navigation: any,
  showLookup?: boolean,
  IsLottery?: boolean,
) => {
  if (text) {
    setSearchQuery(text);
    if (!showLookup) {
      const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: text},
      );

      const vendorItems = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getvendoritemsbyItemNum/:Vendor_Number/:ItemNum',
        {
          Vendor_Number: getBarcode[0]?.Vendor_Number,
          ItemNum: getBarcode[0]?.ItemNum,
        },
      );
      const inventoryAdditional = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getInventoryAdditional/:ItemNum',
        {ItemNum: getBarcode[0]?.ItemNum},
      );

      if (Array.isArray(getBarcode) && getBarcode.length === 0) {
        const userConfirmed = await showAlert(
          'Item not found. Do you want to create a new item?',
        );
        if (userConfirmed) {
          navigation.navigate('ItemType', {ItemData: text});
        } else {
          return false;
        }
      } else {
        if (getBarcode[0]?.ItemType === 3) {
          const getBarcode = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            '/inventory/:ItemNum',
            {ItemNum: getBarcode[0]?.ItemNum},
          );
          navigation.navigate('ChoiceItem', {
            ItemData: getBarcode,
            IsPickList: true,
            ISCREATE: false,
          });
        } else {
          if (IsLottery) {
            if (getBarcode[0]?.IsDeleted) {
              showAlert(
                `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                'Confirmation',
                true,
                'NO',
                'YES',
              )
                .then(async result => {
                  if (result) {
                    navigation.navigate('Barcode', {
                      ItemData: [getBarcode[0]],
                      VENDORITEM: vendorItems,
                      ADDITIONAL: inventoryAdditional,
                      isFromLottery: true,
                      CANEDIT: true,
                      ISCHOICE: true,
                    });
                  } else {
                    return false;
                  }
                })
                .catch(error => {
                  console.error('Error showing alert', error);
                });
            } else {
              navigation.navigate('Barcode', {
                ItemData: [getBarcode[0]],
                VENDORITEM: vendorItems,
                ADDITIONAL: inventoryAdditional,
                isFromLottery: true,
                CANEDIT: true,
                ISCHOICE: true,
              });
            }
          } else {
            if (
              !getBarcode[0]?.Vendor_Number ||
              getBarcode[0]?.Vendor_Number === undefined
            ) {
              const lotteryDepartment = await AsyncStorage.getItem(
                'LOTTERY_DEP_ID',
              );

              if (lotteryDepartment) {
                if (lotteryDepartment === getBarcode[0]?.Dept_ID) {
                  if (getBarcode[0]?.IsDeleted) {
                    showAlert(
                      `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                      'Confirmation',
                      true,
                      'NO',
                      'YES',
                    )
                      .then(async result => {
                        if (result) {
                          navigation.navigate('Barcode', {
                            ItemData: [getBarcode[0]],
                            VENDORITEM: vendorItems,
                            ADDITIONAL: inventoryAdditional,
                            CANEDIT: false,
                            ISCHOICE: true,
                          });
                        } else {
                          return false;
                        }
                      })
                      .catch(error => {
                        console.error('Error showing alert', error);
                      });
                  } else {
                    navigation.navigate('Barcode', {
                      ItemData: [getBarcode[0]],
                      VENDORITEM: vendorItems,
                      ADDITIONAL: inventoryAdditional,
                      CANEDIT: false,
                      ISCHOICE: true,
                    });
                  }
                } else {
                  if (getBarcode[0]?.IsDeleted) {
                    showAlert(
                      `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                      'Confirmation',
                      true,
                      'NO',
                      'YES',
                    )
                      .then(async result => {
                        if (result) {
                          navigation.navigate('Barcode', {
                            ItemData: [getBarcode[0]],
                            VENDORITEM: vendorItems,
                            ADDITIONAL: inventoryAdditional,
                            CANEDIT: true,
                            ISCHOICE: true,
                          });
                        } else {
                          return false;
                        }
                      })
                      .catch(error => {
                        console.error('Error showing alert', error);
                      });
                  } else {
                    navigation.navigate('Barcode', {
                      ItemData: [getBarcode[0]],
                      VENDORITEM: vendorItems,
                      ADDITIONAL: inventoryAdditional,
                      CANEDIT: true,
                      ISCHOICE: true,
                    });
                  }
                }
              } else {
                if (getBarcode[0]?.IsDeleted) {
                  showAlert(
                    `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                    'Confirmation',
                    true,
                    'NO',
                    'YES',
                  )
                    .then(async result => {
                      if (result) {
                        navigation.navigate('Barcode', {
                          ItemData: [getBarcode[0]],
                          VENDORITEM: vendorItems,
                          ADDITIONAL: inventoryAdditional,
                          CANEDIT: true,
                          ISCHOICE: true,
                        });
                      } else {
                        return false;
                      }
                    })
                    .catch(error => {
                      console.error('Error showing alert', error);
                    });
                } else {
                  navigation.navigate('Barcode', {
                    ItemData: [getBarcode[0]],
                    VENDORITEM: vendorItems,
                    ADDITIONAL: inventoryAdditional,
                    CANEDIT: true,
                    ISCHOICE: true,
                  });
                }
              }
            } else {
              const lotteryDepartment = await AsyncStorage.getItem(
                'LOTTERY_DEP_ID',
              );

              if (lotteryDepartment) {
                if (lotteryDepartment === getBarcode[0]?.Dept_ID) {
                  if (getBarcode[0]?.IsDeleted) {
                    showAlert(
                      `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                      'Confirmation',
                      true,
                      'NO',
                      'YES',
                    )
                      .then(async result => {
                        if (result) {
                          navigation.navigate('Barcode', {
                            ItemData: [getBarcode[0]],
                            VENDORITEM: vendorItems,
                            ADDITIONAL: inventoryAdditional,
                            CANEDIT: false,
                            ISCHOICE: true,
                          });
                        } else {
                          return false;
                        }
                      })
                      .catch(error => {
                        console.error('Error showing alert', error);
                      });
                  } else {
                    navigation.navigate('Barcode', {
                      ItemData: [getBarcode[0]],
                      VENDORITEM: vendorItems,
                      ADDITIONAL: inventoryAdditional,
                      CANEDIT: false,
                      ISCHOICE: true,
                    });
                  }
                } else {
                  if (getBarcode[0]?.IsDeleted) {
                    showAlert(
                      `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                      'Confirmation',
                      true,
                      'NO',
                      'YES',
                    )
                      .then(async result => {
                        if (result) {
                          navigation.navigate('Barcode', {
                            ItemData: [getBarcode[0]],
                            VENDORITEM: vendorItems,
                            ADDITIONAL: inventoryAdditional,
                            CANEDIT: true,
                            ISCHOICE: true,
                          });
                        } else {
                          return false;
                        }
                      })
                      .catch(error => {
                        console.error('Error showing alert', error);
                      });
                  } else {
                    navigation.navigate('Barcode', {
                      ItemData: [getBarcode[0]],
                      VENDORITEM: vendorItems,
                      ADDITIONAL: inventoryAdditional,
                      CANEDIT: true,
                      ISCHOICE: true,
                    });
                  }
                }
              } else {
                if (getBarcode[0]?.IsDeleted) {
                  showAlert(
                    `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName} Would You Like to Restore it?`,
                    'Confirmation',
                    true,
                    'NO',
                    'YES',
                  )
                    .then(async result => {
                      if (result) {
                        navigation.navigate('Barcode', {
                          ItemData: [getBarcode[0]],
                          VENDORITEM: vendorItems,
                          ADDITIONAL: inventoryAdditional,
                          CANEDIT: true,
                          ISCHOICE: true,
                        });
                      } else {
                        return false;
                      }
                    })
                    .catch(error => {
                      console.error('Error showing alert', error);
                    });
                } else {
                  navigation.navigate('Barcode', {
                    ItemData: [getBarcode[0]],
                    VENDORITEM: vendorItems,
                    ADDITIONAL: inventoryAdditional,
                    CANEDIT: true,
                    ISCHOICE: true,
                  });
                }
              }
            }
          }
        }
      }
    } else {
      handleSearch(
        text,
        inventoryData,
        ['ItemName', 'ItemNum'],
        setFilteredData,
        setLoading,
      );
    }
  } else {
    setFilteredData(inventoryData);
  }
};

export const onSearchChange_CommonVendor = async (
  text: string,
  inventoryData: VendorItem[], // You can refine the type if neededd
  setFilteredData: (data: VendorItem[]) => void,
  setLoading: (loading: boolean) => void,
  setSearchQuery: (query: string) => void,
  navigation: any,
  showLookup?: boolean,
  MainPurchase?: PurchaseOrder,
  IsDsd?: boolean,
  Vendor_Number?: string,
  PoType?: number,
) => {
  const checkAlt = inventoryData.some(item => item.ItemNum === text);
  if (checkAlt) {
    handleSearch(
      text,
      inventoryData,
      ['ItemName', 'ItemNum'],
      setFilteredData,
      setLoading,
    );
  } else {
    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          inventoryData,
          ['ItemName', 'ItemNum'],
          setFilteredData,
          setLoading,
        );
      } else {
        setSearchQuery(text);
        const getBarcode = await GetItemsParamsNoFilterNoReturn<VendorItem>(
          (await getInventoryPort()).toString(),
          '/inventory/:ItemNum',
          {ItemNum: text},
        );

        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('ItemType', {
              ItemData: text,
              ISPO: true,
              MAINPO: MainPurchase,
              VENDOR: Vendor_Number,
              ISDSD: IsDsd ? true : false,
              POTYPE: PoType,
            });
          } else {
            return false;
          }
        } else {
          handleSearch(
            getBarcode[0]?.ItemNum,
            inventoryData,
            ['ItemName', 'ItemNum'],
            setFilteredData,
            setLoading,
          );
        }
      }
    } else {
      setFilteredData(inventoryData);
    }
  }
};

export const AdjustInventory = async (ItemNum: string, Quantity: number) => {
  const getInventory = await GetItemsParamsNoFilterNoReturn(
    (await getInventoryPort()).toString(),
    '/inventory/:ItemNum',
    {ItemNum: ItemNum},
  );

  const adjustStock = Number(Quantity) + Number(getInventory[0].In_Stock);
  const inventoryData: Partial<Inventory> = {
    ItemNum: getInventory[0]?.ItemNum,
    ItemName: getInventory[0]?.ItemName,
    Dept_ID: getInventory[0]?.Dept_ID,
    Cost: getInventory[0]?.Cost,
    Price: getInventory[0]?.Price,
    Retail_Price: getInventory[0]?.Retail_Price,
    In_Stock: Number(adjustStock),
    Date_Created: getInventory[0]?.Date_Created,
    Last_Sold: getInventory[0]?.Last_Sold,
    Location: getInventory[0]?.Location,
    Vendor_Number: getInventory[0]?.Vendor_Number,
    Vendor_Part_Num: getInventory[0]?.Vendor_Part_Num,
    Reorder_Level: getInventory[0]?.Reorder_Level,
    Reorder_Quantity: getInventory[0]?.Reorder_Quantity,
    ReOrder_Cost: getInventory[0]?.ReOrder_Cost,
    Unit_Size: getInventory[0]?.Unit_Size,
    Unit_Type: getInventory[0]?.Unit_Type,
    FoodStampable: getInventory[0]?.FoodStampable,
    Tax_1: getInventory[0]?.Tax_1[0],
    Tax_2: getInventory[0]?.Tax_2[0],
    Tax_3: getInventory[0]?.Tax_3[0],
    Tax_4: getInventory[0]?.Tax_4[0],
    Tax_5: getInventory[0]?.Tax_5[0],
    Tax_6: getInventory[0]?.Tax_6[0],
    Check_ID: getInventory[0]?.Check_ID,
    Check_ID2: getInventory[0]?.Check_ID2,
    Store_ID: getInventory[0]?.Store_ID,
    ItemName_Extra: getInventory[0]?.ItemName_Extra,
  };
  const applyDefault = applyDefaults(inventoryData);

  const result = await updateData<Inventory>({
    baseURL: (await getInventoryPort()).toString(),
    data: applyDefault,
    endpoint: '/updatebarcode',
  });

  if (result) {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const CashierID = await AsyncStorage.getItem('SWIPEID');
    const ValideCashier = CashierID === null ? '100101' : CashierID;

    const inventoryAdjustData: Partial<Inventory_In> = {
      ItemNum: getInventory[0].ItemNum,
      Store_ID: ValidStore,
      Quantity: Number(adjustStock),
      DateTime: getFormateDate(Date()),
      Dirty: true,
      TransType: 'C',
      Description: 'ITEM CREATION',
      Cashier_ID: ValideCashier,
      CostPer: getInventory[0].Cost,
    };

    const applyDefault = applyDefaultsInventoryAdjust(inventoryAdjustData);
    const invenIN = await createData<Inventory_In>({
      baseURL: (await getInventoryPort()).toString(),
      data: applyDefault,
      endpoint: '/createinvetoryin',
    });
  }
};

// Serial Number Order Utility Functions
export const getSerialNumberOrder = async (): Promise<
  'ascending' | 'descending'
> => {
  try {
    // First try to get from API
    const {getOrgId, getAvailabilityInfo} = await import(
      './NotificationHelper'
    );
    const orgId = await getOrgId();

    if (orgId) {
      const availabilityData = await getAvailabilityInfo(orgId);
      if (availabilityData && availabilityData.is_ticket_ascending !== null) {
        return availabilityData.is_ticket_ascending
          ? 'ascending'
          : 'descending';
      }
    }

    // Fallback to AsyncStorage if API data not available
    const order = await AsyncStorage.getItem('SERIAL_NUMBER_ORDER');
    return (order as 'ascending' | 'descending') || 'ascending';
  } catch (error) {
    console.error('Error getting serial number order:', error);
    // Final fallback to AsyncStorage
    const order = await AsyncStorage.getItem('SERIAL_NUMBER_ORDER');
    return (order as 'ascending' | 'descending') || 'ascending';
  }
};

export const calculateSerialDifference = (
  endSerial: number,
  startSerial: number,
  order: 'ascending' | 'descending',
): number => {
  if (order === 'ascending') {
    return endSerial - startSerial;
  } else {
    return startSerial - endSerial;
  }
};

export const calculateTicketsSold = (
  totalTickets: number,
  serialDifference: number,
  order: 'ascending' | 'descending',
): number => {
  if (order === 'ascending') {
    return serialDifference;
  } else {
    return serialDifference;
  }
};

export const calculateRemainingStock = (
  totalTickets: number,
  ticketsSold: number,
): number => {
  return totalTickets - ticketsSold;
};
