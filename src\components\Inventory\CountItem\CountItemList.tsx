import React, {memo, useCallback, useRef} from 'react';
import {
  FlatList,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
} from 'react-native';
import {Inventory_Filter} from '../../../server/types';
import {CountItemInput} from '../../../hooks/inventory/useCountItemData';
import {useCountItemPerformance} from '../../../hooks/inventory/useCountItemPerformance';
import CountInventoryItem from './CountInventoryItem';
import CountItemEmptyList from './CountItemEmptyList';
import CountItemListFooter from './CountItemListFooter';
import AppLoader from '../AppLoader';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useThemeColors} from '../../../Theme/useThemeColors';

interface CountItemListProps {
  displayData: Inventory_Filter[];
  inputValues: CountItemInput[];
  actionRef: React.MutableRefObject<boolean>;
  loading: boolean;
  initialLoading: boolean;
  refreshing: boolean;
  searchQuery: string;
  appLoader: boolean;
  setAppLoader: (value: boolean) => void;
  onLoadMore: () => void;
  onRefresh: () => void;
  onItemPress: (item: Inventory_Filter) => void;
  onOutsidePress: () => void;
}

const CountItemList: React.FC<CountItemListProps> = ({
  displayData,
  inputValues,
  actionRef,
  loading,
  initialLoading,
  refreshing,
  searchQuery,
  appLoader,
  setAppLoader,
  onLoadMore,
  onRefresh,
  onItemPress,
  onOutsidePress,
}) => {
  const colors = useThemeColors();
  const flatListRef = useRef<FlatList>(null);

  // Use performance optimization hook
  const {
    keyExtractor,
    getItemLayout,
    getInputValue,
    hasInputValue,
    flatListProps,
  } = useCountItemPerformance(displayData, inputValues, actionRef);

  // Optimized renderItem function
  const renderItem = useCallback(
    ({item}: {item: Inventory_Filter}) => {
      const inputValue = getInputValue(item.ItemNum);
      const hasInput = hasInputValue(item.ItemNum);

      return (
        <CountInventoryItem
          item={item}
          onPress={() => onItemPress(item)}
          inputValue={inputValue}
          hasInputValue={hasInput}
        />
      );
    },
    [getInputValue, hasInputValue, onItemPress],
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: wp('2.5%'),
    },
    listContent: {
      paddingBottom: hp('10%'), // Add space for FAB
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
  });

  if (initialLoading) {
    return (
      <View style={styles.centered}>
        <AppLoader
          modalVisible={appLoader}
          setModalVisible={setAppLoader}
          isLookup={true}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={displayData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        refreshing={refreshing}
        onRefresh={onRefresh}
        getItemLayout={getItemLayout}
        ListEmptyComponent={<CountItemEmptyList searchQuery={searchQuery} />}
        ListFooterComponent={
          <CountItemListFooter loading={loading && !initialLoading} />
        }
        {...flatListProps}
      />
    </View>
  );
};

export default memo(CountItemList);
