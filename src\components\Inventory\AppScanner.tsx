import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Linking,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
} from 'react-native-vision-camera';
import {useFocusEffect} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';

type NavProps = {
  codeScanner?: any;
  onClose?: () => void;
};

const AppScanner: React.FC<NavProps> = ({codeScanner, onClose}) => {
  const device = useCameraDevice('back');
  const {hasPermission, requestPermission} = useCameraPermission();
  const [cameraActive, setCameraActive] = useState(true);
  const [loading, setLoading] = useState(false);

  // Request camera permission for Android
  const requestAndroidPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'We need access to your camera to scan QR codes.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        } else {
        }
      } catch (err) {
        console.warn(err);
      }
    }
  };

  // Handle camera permission
  const handlePermissionRequest = async () => {
    if (hasPermission === null) {
      // Request permission if not granted or yet to be granted
      const permissionStatus = await requestPermission();
      if (permissionStatus === 'denied') {
        Alert.alert(
          'Camera Permission Denied',
          'Please allow camera access from settings to use the scanner.',
          [
            {
              text: 'Cancel',
              onPress: () => console.log('Permission denied'),
              style: 'cancel',
            },
            {
              text: 'Open Settings',
              onPress: () => {
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings://');
                } else if (Platform.OS === 'android') {
                  // Replace with your app's package name
                  Linking.openURL('package:com.nurpimz');
                }
              },
            },
          ],
        );
      } else if (permissionStatus === 'granted') {
      }
    } else if (hasPermission === false) {
      Alert.alert(
        'Camera Permission Denied',
        'Please allow camera access from settings to use the scanner.',
        [
          {
            text: 'Cancel',
            onPress: () => console.log('Permission denied'),
            style: 'cancel',
          },
          {
            text: 'Open Settings',
            onPress: () => {
              if (Platform.OS === 'ios') {
                Linking.openURL('app-settings://');
              } else if (Platform.OS === 'android') {
                // Replace with your app's package name
                Linking.openURL('package:com.nurpimz');
              }
            },
          },
        ],
      );
    }
  };

  useEffect(() => {
    if (Platform.OS === 'android') {
      requestAndroidPermission(); // Request permission on Android
    } else {
      handlePermissionRequest(); // iOS should automatically request
    }
  }, [hasPermission]);

  useFocusEffect(() => {
    setCameraActive(true);
  });

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  if (device == null) {
    return (
      <View style={styles.container}>
        <Text>No Camera Device Found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
        <Ionicons name="close-circle" size={40} color="white" />
      </TouchableOpacity>

      {device != null && (
        <Camera
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={cameraActive}
          codeScanner={codeScanner}
          frameProcessorFps={1}
        />
      )}

      {loading && (
        <ActivityIndicator
          style={styles.loading}
          size="large"
          color="#ffffff"
        />
      )}

      <View style={styles.scanningArea} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningArea: {
    position: 'absolute',
    top: '30%',
    left: '10%',
    width: '80%',
    height: '40%',
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 10,
  },
  loading: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: -25}, {translateY: -25}],
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 10,
  },
});

export default AppScanner;
