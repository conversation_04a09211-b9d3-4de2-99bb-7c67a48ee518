import React, {useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
} from 'react-native';

interface CustomCheckboxProps {
  isChecked: boolean;
  onChange: (value: boolean) => void;
  size?: number;
  borderRadius?: number;
  activeColor?: string;
  inactiveColor?: string;
  checkColor?: string;
  rippleEffect?: boolean;
  disabled?: boolean;
}

const CustomCheckbox = ({
  isChecked,
  onChange,
  size = 22,
  borderRadius = 6,
  activeColor = '#6366F1', // Indigo color (more modern than blue)
  inactiveColor = '#E5E7EB', // Light gray for inactive
  checkColor = '#FFFFFF',
  rippleEffect = true,
  disabled = false,
}: CustomCheckboxProps) => {
  // Animation values
  const checkScale = new Animated.Value(isChecked ? 1 : 0);
  const boxColorAnimation = new Animated.Value(isChecked ? 1 : 0);

  // Animation effect when state changes
  useEffect(() => {
    Animated.parallel([
      Animated.timing(checkScale, {
        toValue: isChecked ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.16, 1, 0.3, 1), // Custom Bezier curve for a more natural feel
        useNativeDriver: true,
      }),
      Animated.timing(boxColorAnimation, {
        toValue: isChecked ? 1 : 0,
        duration: 200,
        easing: Easing.bezier(0.16, 1, 0.3, 1),
        useNativeDriver: false,
      }),
    ]).start();
  }, [isChecked]);

  // Interpolate background color based on state
  const backgroundColor = boxColorAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [inactiveColor, activeColor],
  });

  // Ripple animation value
  const rippleAnim = new Animated.Value(0);

  const handlePress = () => {
    if (disabled) return;

    // Trigger ripple effect
    if (rippleEffect) {
      rippleAnim.setValue(0);
      Animated.timing(rippleAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    }

    onChange(!isChecked);
  };

  // Scale interpolation for ripple effect
  const rippleScale = rippleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 1.3],
  });

  // Opacity interpolation for ripple effect
  const rippleOpacity = rippleAnim.interpolate({
    inputRange: [0, 0.7, 1],
    outputRange: [0.2, 0.1, 0],
  });

  return (
    <TouchableOpacity
      style={[styles.container, {opacity: disabled ? 0.6 : 1}]}
      onPress={handlePress}
      activeOpacity={0.8}
      disabled={disabled}>
      {/* Ripple effect */}
      {rippleEffect && (
        <Animated.View
          style={[
            styles.ripple,
            {
              width: size * 2.5,
              height: size * 2.5,
              borderRadius: size * 1.25,
              backgroundColor: activeColor,
              transform: [{scale: rippleScale}],
              opacity: rippleOpacity,
              position: 'absolute',
              top: -size * 0.75,
              left: -size * 0.75,
            },
          ]}
        />
      )}

      {/* Checkbox */}
      <Animated.View
        style={[
          styles.checkbox,
          {
            width: size,
            height: size,
            borderRadius: borderRadius,
            backgroundColor,
            borderWidth: isChecked ? 0 : 2,
            borderColor: disabled ? '#CBD5E1' : '#D1D5DB',
          },
        ]}>
        {/* Checkmark */}
        <Animated.View
          style={[
            styles.checkmarkContainer,
            {
              transform: [{scale: checkScale}],
              opacity: checkScale,
            },
          ]}>
          <View style={styles.checkmark}>
            <View
              style={[
                styles.checkmarkLine,
                styles.checkmarkLineShort,
                {backgroundColor: checkColor},
              ]}
            />
            <View
              style={[
                styles.checkmarkLine,
                styles.checkmarkLineLong,
                {backgroundColor: checkColor},
              ]}
            />
          </View>
        </Animated.View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  checkbox: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  checkmarkContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    width: '60%',
    height: '60%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkLine: {
    position: 'absolute',
    backgroundColor: '#FFFFFF',
  },
  checkmarkLineShort: {
    width: '30%',
    height: 2,
    bottom: '45%',
    left: '20%',
    transform: [{rotate: '45deg'}],
  },
  checkmarkLineLong: {
    width: '60%',
    height: 2,
    bottom: '40%',
    right: '15%',
    transform: [{rotate: '-45deg'}],
  },
  ripple: {
    position: 'absolute',
    zIndex: -1,
  },
});

export default CustomCheckbox;
