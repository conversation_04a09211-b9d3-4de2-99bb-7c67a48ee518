import {useState, useCallback, useRef, useEffect} from 'react';
import {TextInput, Keyboard, Platform, Alert} from 'react-native';
import {VendorItem, PurchaseOrder, PurchaseOrderItems} from '../server/types';
import {
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  handleSearch,
  showAlert,
  showAlertOK,
} from '../utils/PublicHelper';
import {getInventoryPort} from '../server/InstanceTypes';

export const useReturnVendor = (
  mainPO: PurchaseOrder,
  navigation: any,
  route: any,
  itemPO: PurchaseOrderItems[],
  isReturnOrDsd: boolean,
  poType: number,
) => {
  const [vendorItems, setVendorItems] = useState<VendorItem[]>([]);
  const [vendorItemsFilter, setVendorItemsFilter] = useState<VendorItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [camera, setCamera] = useState(false);

  // Debug effect to track vendorItemsFilter changes
  useEffect(() => {
    console.log(
      'vendorItemsFilter updated:',
      vendorItemsFilter.length,
      'items',
    );
  }, [vendorItemsFilter]);

  const textInputRef = useRef<TextInput>(null);

  // Initial loading of vendor items
  useEffect(() => {
    const loadInitialVendorItems = async () => {
      console.log('Loading initial vendor items...');
      console.log('POTYPE:', poType);
      console.log(
        'Vendor Number:',
        route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
      );

      setLoading(true);
      try {
        if (poType === 1) {
          console.log('Loading vendor-specific items...');
          GetItemsParamsNoFilter(
            (await getInventoryPort()).toString(),
            '/getvendoritems/:Vendor_Number',
            setVendorItems,
            {
              Vendor_Number:
                route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
            },
          );

          GetItemsParamsNoFilter(
            (await getInventoryPort()).toString(),
            '/getvendoritems/:Vendor_Number',
            setVendorItemsFilter,
            {
              Vendor_Number:
                route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
            },
          );
        } else {
          console.log('Loading all vendor items...');
          GetAllItemsWithFilter(
            (await getInventoryPort()).toString(),
            '/getAllvendoritems',
            setVendorItems,
            setVendorItemsFilter,
            setLoading,
            false,
          );
        }
      } catch (error) {
        console.error('Error loading vendor items:', error);
      } finally {
        if (poType === 1) {
          setLoading(false);
        }
        // For GetAllItemsWithFilter, loading is handled internally
      }
    };

    loadInitialVendorItems();
  }, [poType, route?.params?.ItemData?.Vendor_Number, mainPO.Vendor_Number]);

  const isItemNumExist = useCallback(
    (itemNum: string) => {
      return itemPO.some(item => item.ItemNum === itemNum);
    },
    [itemPO],
  );

  const VendorItemAdd = useCallback(
    (item: VendorItem) => {
      const IsExists = isItemNumExist(item.ItemNum);
      if (IsExists) {
        Alert.alert('Item Already Exists');
      } else {
        setshowLookup(false);
        setSearchQuery('');
        if (poType === 1) {
          navigation.navigate('AddVendorItemQuanity', {
            ItemData: item,
            Main: mainPO,
          });
        } else {
          navigation.navigate('PurchaseOrderQuanity', {
            ItemData: item,
            Main: mainPO,
            IsDirect: true,
          });
        }
      }
    },
    [isItemNumExist, poType, navigation, mainPO],
  );

  const handleLoadMore = useCallback(() => {
    if (!loading) {
      // Add pagination logic if needed
    }
  }, [loading]);

  const ScanBarcode = useCallback(
    (Barcode: string) => {
      const IsExists = isItemNumExist(Barcode);
      if (IsExists) {
        Alert.alert('Item Already Exists');
      } else {
        const GetVendorItem = vendorItems.filter(
          item => item.ItemNum === Barcode,
        );
        VendorItemAdd(GetVendorItem[0]);
      }
    },
    [isItemNumExist, vendorItems, VendorItemAdd],
  );

  const handleSearchChange = useCallback(
    async (text: string) => {
      setCamera(false);
      const getBarcode = await GetItemsParamsNoFilterNoReturn<VendorItem[]>(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: text},
      );
      setSearchQuery((getBarcode && getBarcode[0]?.ItemNum) || text);
      if (text) {
        if (showLookup) {
          handleSearch(
            text,
            vendorItemsFilter as any,
            ['ItemName', 'ItemNum'],
            setVendorItemsFilter as any,
            setLoading,
          );
        } else {
          if (Array.isArray(getBarcode) && getBarcode.length === 0) {
            const userConfirmed = await showAlert(
              'Item not found. Do you want to create a new item?',
            );
            if (userConfirmed) {
              navigation.navigate('ItemType', {
                ItemData: text,
                ISPO: true,
                MAINPO: mainPO,
                VENDOR: mainPO.Vendor_Number,
                ISDSD: isReturnOrDsd ? true : false,
                POTYPE: poType,
              });
            } else {
              if (textInputRef.current) {
                textInputRef.current.clear();
                textInputRef.current.blur();
              }

              setSearchQuery('');
              setshowLookup(false);
              Keyboard.dismiss();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 200);
            }
          } else {
            const FiterExist = isItemNumExist(
              Array.isArray(getBarcode) && getBarcode.length === 0
                ? text
                : (getBarcode && getBarcode[0]?.ItemNum) || text,
            );
            if (FiterExist) {
              showAlertOK(
                'This Item is Already Exists. Please Try Again',
                'Already Exists',
                'OK',
                () => {
                  if (textInputRef.current) {
                    textInputRef.current.clear();
                    textInputRef.current.blur();
                  }

                  setSearchQuery('');
                  setshowLookup(false);
                  Keyboard.dismiss();
                  setTimeout(() => {
                    if (textInputRef.current) {
                      textInputRef.current.focus();
                    }
                  }, 200);
                },
              );
            } else {
              const filterVendor = vendorItems.filter(
                item => item.ItemNum === (getBarcode && getBarcode[0]?.ItemNum),
              );
              setshowLookup(false);
              setSearchQuery('');
              if (poType === 1) {
                navigation.navigate('AddVendorItemQuanity', {
                  ItemData: filterVendor[0],
                  Main: mainPO,
                });
              } else {
                navigation.navigate('PurchaseOrderQuanity', {
                  ItemData: filterVendor[0],
                  Main: mainPO,
                  IsDirect: true,
                });
              }
            }
          }
        }
      } else {
        setVendorItemsFilter(vendorItems);
      }
    },
    [
      showLookup,
      vendorItemsFilter,
      vendorItems,
      isItemNumExist,
      navigation,
      mainPO,
      isReturnOrDsd,
      poType,
    ],
  );

  const toggleLookup = useCallback(
    async (value: boolean) => {
      setSearchQuery('');
      if (poType === 1) {
        GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/getvendoritems/:Vendor_Number',
          setVendorItems,
          {
            Vendor_Number:
              route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
          },
        );

        GetItemsParamsNoFilter(
          (await getInventoryPort()).toString(),
          '/getvendoritems/:Vendor_Number',
          setVendorItemsFilter,
          {
            Vendor_Number:
              route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
          },
        );
      } else {
        GetAllItemsWithFilter(
          (await getInventoryPort()).toString(),
          '/getAllvendoritems',
          setVendorItems,
          setVendorItemsFilter,
          setLoading,
          false,
        );
      }
      setshowLookup(value);

      if (Platform.OS === 'android') {
        if (value) {
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        } else {
          Keyboard.dismiss();
          setTimeout(() => {
            if (textInputRef.current) {
              textInputRef.current.blur();
              setTimeout(() => {
                if (textInputRef.current) {
                  textInputRef.current.focus();
                }
              }, 50);
            }
          }, 50);
        }
        return;
      }

      // iOS handling
      if (value) {
        setTimeout(() => {
          textInputRef.current?.focus();
        }, 100);
      } else {
        Keyboard.dismiss();
      }
    },
    [poType, route?.params?.ItemData?.Vendor_Number, mainPO.Vendor_Number],
  );

  const handleDoneClick = useCallback(async () => {
    if (poType === 1) {
      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItems,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );

      GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/getvendoritems/:Vendor_Number',
        setVendorItemsFilter,
        {
          Vendor_Number:
            route?.params?.ItemData?.Vendor_Number || mainPO.Vendor_Number,
        },
      );
    } else {
      GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/getAllvendoritems',
        setVendorItems,
        setVendorItemsFilter,
        setLoading,
        false,
      );
    }
    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  }, [poType, route?.params?.ItemData?.Vendor_Number, mainPO.Vendor_Number]);

  const keyExtractor = useCallback(
    (item: VendorItem) => item.ItemNum.toString(),
    [],
  );

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: 70,
      offset: 70 * index,
      index,
    }),
    [],
  );

  return {
    vendorItems,
    vendorItemsFilter,
    setVendorItems,
    setVendorItemsFilter,
    loading,
    setLoading,
    searchQuery,
    setSearchQuery,
    showLookup,
    setshowLookup,
    camera,
    setCamera,
    textInputRef,
    poType,
    isItemNumExist,
    VendorItemAdd,
    handleLoadMore,
    ScanBarcode,
    handleSearchChange,
    toggleLookup,
    handleDoneClick,
    keyExtractor,
    getItemLayout,
  };
};
