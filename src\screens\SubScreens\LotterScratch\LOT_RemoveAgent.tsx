import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import AssignBookCart from '../../../components/LotteryScratch/AssignBookCart'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'

type NavProps = {
  navigation: NativeStackNavigationProp<any>; 
};

const LOT_RemoveAgent: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
     <Header NavName='Remove Agent'/>

     <View style={{paddingHorizontal: 10, gap:30}}>
      <AssignBookCart Onpress={() => navigation.navigate("RemoveAgentConfirm")}/>
      <AssignBookCart />
      <AssignBookCart />
      <AssignBookCart />
      <AssignBookCart />
      <AssignBookCart />
      <AssignBookCart />
     </View>
    </View>
  )
}

export default LOT_RemoveAgent