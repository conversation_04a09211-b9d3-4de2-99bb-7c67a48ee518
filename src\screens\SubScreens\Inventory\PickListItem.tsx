import {View, Text, Alert} from 'react-native';
import React, {useState} from 'react';
import {Backround} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {RouteProp} from '@react-navigation/native';
import {
  Inventory,
  Invoice_Itemized,
  Invoice_Totals,
} from '../../../server/types';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  applyDefaultsInvoiceItemized,
  applyDefaultsInvoiceTotals,
} from '../../../Validator/Inventory/Barcode';
import {
  calculatePriceWithVAT1,
  createData,
  updateData,
} from '../../../utils/PublicHelper';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {getInventoryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';

const validationSchema = Yup.object().shape({
  quanity: Yup.string().required('Quanity is required'),
  action: Yup.string().required('action is required'),
});

type BarcodeScreenRouteProp = RouteProp<any, 'totals'>;
const PickListItem: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [totals, setTotals] = useState<Invoice_Totals>(route.params?.Total);
  const [itemized, setItemized] = useState<Invoice_Itemized>(
    route.params?.ItemData,
  );
  const [qty, setQty] = useState<number>(route.params?.ItemData?.Quantity);
  const [itemizedIn, setItemizedIn] = useState<Inventory>(
    route.params?.ItemData[0],
  );
  const [choice, setChoice] = useState<Inventory>(route.params?.ItemData);

  const Action = [
    {label: 'Increase', value: 'Increase'},
    {label: 'Decrease', value: 'Decrease'},
  ];

  const initialValues = {
    quanity: '',
    action: '',
  };

  const updateItemizeQty = async (Values: any) => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    try {
      const invoiceItemized: Partial<Invoice_Itemized> = {
        Invoice_Number: totals?.Invoice_Number,
        ItemNum: itemized?.ItemNum || itemizedIn?.ItemNum || choice?.ItemNum,
        Quantity:
          Values.action === 'Increase'
            ? Number(itemized.Quantity || 0) + Number(Values?.quanity || 0)
            : Number(itemized.Quantity || 0) - Number(Values?.quanity || 0),
        CostPer: itemized?.CostPer || itemizedIn?.Cost || choice?.Cost,
        PricePer: itemized?.PricePer || itemizedIn?.Price || choice?.Price,
        Tax1Per: 1,
        DiffItemName:
          itemized?.DiffItemName || itemizedIn?.ItemName || choice?.ItemName,
        Store_ID: ValidStore,
        origPricePer:
          itemized?.origPricePer || itemizedIn?.Price || choice?.Price,
        Allow_Discounts: true,
        KitchenQuantityPrinted: 1,
        PricePerBeforeDiscount:
          itemized?.PricePerBeforeDiscount ||
          itemizedIn?.Price ||
          choice?.Price,
        SentToKitchen: true,
        Tare: -1,
      };

      const applyDefault = applyDefaultsInvoiceItemized(invoiceItemized); //long pressed to pasete okay

      // if (!itemized.Invoice_Number || itemized.Invoice_Number === null) {
      if (
        itemized.Quantity === 0 ||
        itemized.Quantity === null ||
        itemized.Quantity === undefined
      ) {
        const unpdate = await createData<Invoice_Itemized>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createitemized',
        });

        if (unpdate) {
          Alert.alert('Quanity Added success!');
          setQty(Values.quanity);
        } else {
          Alert.alert('updated Failed!');
        }
      } else {
        const unpdate = await updateData<Invoice_Itemized>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updateinvoiecitemized',
        });
        if (unpdate) {
          if (Values.action === 'Increase') {
            const totalCost = Values.quanity * itemized.CostPer;
            const totalPrice = Values.quanity * itemized.PricePer;
            const totalVat =
              Number(calculatePriceWithVAT1(Number(totalPrice))) -
              Number(totalPrice);
            const grandTotals = Number(totalPrice) + Number(totalVat);

            const dataCost = Number(totals.Total_Cost) + Number(totalCost);
            const dataPrice = Number(totals.Total_Price) + Number(totalPrice);
            const dataVat = Number(totals.Total_Tax1) + Number(totalVat);
            const dataGrand = Number(totals.Grand_Total) + Number(grandTotals);
            updateInvoiceTotals(
              dataPrice,
              dataCost,
              dataVat,
              dataGrand,
              dataPrice,
              dataPrice,
            );
          } else {
            const totalCost = Values.quanity * itemized.CostPer;
            const totalPrice = Values.quanity * itemized.PricePer;
            const totalVat =
              Number(calculatePriceWithVAT1(Number(totalPrice))) -
              Number(totalPrice);
            const grandTotals = Number(totalPrice) + Number(totalVat);

            const dataCost = Number(totals.Total_Cost) - Number(totalCost);
            const dataPrice = Number(totals.Total_Price) - Number(totalPrice);
            const dataVat = Number(totals.Total_Tax1) - Number(totalVat);
            const dataGrand = Number(totals.Grand_Total) - Number(grandTotals);
            updateInvoiceTotals(
              dataPrice,
              dataCost,
              dataVat,
              dataGrand,
              dataPrice,
              dataPrice,
            );
          }
          const qtytotal =
            Values.action === 'Increase'
              ? Number(itemized.Quantity) + Number(Values.quanity)
              : Number(itemized.Quantity) - Number(Values.quanity);
          setQty(qtytotal);
          Alert.alert('Quanity updated success!');
        } else {
          Alert.alert('updated Failed!');
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updateInvoiceTotals = async (
    Total_Price?: number,
    Total_Cost?: number,
    Total_Tax1?: number,
    Grand_Total?: number,
    Taxed_Sales?: number,
    Total_UndiscountedSale?: number,
  ) => {
    try {
      const pickListData: Partial<Invoice_Totals> = {
        Invoice_Number: totals.Invoice_Number,
        ReferenceInvoiceNumber: totals.Invoice_Number.toString(),
        Orig_OnHoldID: totals.Orig_OnHoldID,
        Store_ID: totals.Store_ID,
        Cashier_ID: totals.Cashier_ID,
        CustNum: totals.CustNum,
        DateTime: totals.DateTime,
        Total_Price: Total_Price,
        Total_Cost: Total_Cost,
        Total_Tax1: Total_Tax1,
        Grand_Total: Grand_Total,
        Station_ID: totals.Station_ID,
        Payment_Method: totals.Payment_Method,
        Status: totals.Status,
        Taxed_1: totals.Taxed_1,
        Taxed_Sales: Taxed_Sales,
        Dirty: totals.Dirty,
        CourseOrderingProgress: totals.CourseOrderingProgress,
        Total_UndiscountedSale: Total_UndiscountedSale,
      };

      const applyDefult = applyDefaultsInvoiceTotals(pickListData);

      const result = await updateData<Invoice_Totals>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefult,
        endpoint: '/updateinvoice',
      });
      if (result) {
        // setSuccess(false)
      }
    } catch (error) {}
  };

  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
      <Header NavName={itemized.DiffItemName || itemized[0]?.ItemName} />
      <Formik
        initialValues={initialValues}
        enableReinitialize={true} // Enable reinitialization
        validationSchema={validationSchema}
        onSubmit={values => {
          updateItemizeQty(values);
        }}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
          values,
          errors,
          touched,
        }) => (
          <View style={{marginHorizontal: 10, marginTop: 20}}>
            <Text style={{fontSize: 16, fontWeight: 'bold'}}>
              Refrence Number: {totals.Orig_OnHoldID}
            </Text>
            {/* <Text style={{fontSize: 16, fontWeight: 'bold'}}>Create Date: {new Date(totals.DateTime).toISOString().split('T')[0]}</Text> */}
            <Text style={{fontSize: 16, fontWeight: 'bold'}}>
              Total Item Added: {qty}
            </Text>

            <View style={{marginTop: 30}}>
              <AppDropDown
                label="Action"
                options={Action}
                selectedValue={values.action}
                onSelect={value => setFieldValue('action', value)}
                error={errors.action}
                touched={touched.action}
              />

              <AppTextInput
                PlaceHolder="Enter Quanity"
                Title="Quanity"
                Value={values.quanity}
                onChangeText={handleChange('quanity')}
                onBlur={handleBlur('quanity')}
                error={errors.quanity}
                touched={touched.quanity}
              />
            </View>

            <AppButton
              Title={
                itemized.Quantity === 0 ||
                itemized.Quantity === null ||
                itemized.Quantity === undefined
                  ? 'Add Item'
                  : 'Updted Item'
              }
              OnPress={handleSubmit}
            />
          </View>
        )}
      </Formik>
    </View>
  );
};

export default PickListItem;
