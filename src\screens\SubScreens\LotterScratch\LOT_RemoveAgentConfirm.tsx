import React, {useState} from 'react';
import {
  View,
  Image,
  Button,
  Text,
  StyleSheet,
  Platform,
  PermissionsAndroid,
  Alert,
  Linking,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {
  launchImageLibrary,
  ImagePickerResponse,
} from 'react-native-image-picker';
import * as base64 from 'base64-js';
import RNFS from 'react-native-fs';

/**
 * Main component for image conversion
 */
const MonochromeConverter: React.FC = () => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [base64Data, setBase64Data] = useState<string | null>(null); // Store base64 data
  const [processing, setProcessing] = useState<boolean>(false);
  const [result, setResult] = useState<string | null>(null);
  const [rawBinaryOutput, setRawBinaryOutput] = useState<string | null>(null);

  /**
   * Request necessary permissions for Android
   */
  const requestPermissions = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') return true;

    try {
      // For Android 13+ (API level 33+)
      if (parseInt(Platform.Version as string, 10) >= 33) {
        const photoPermission = await PermissionsAndroid.request(
          'android.permission.READ_MEDIA_IMAGES' as any, // Using string as it might not be defined in older RN versions
          {
            title: 'Photos Permission',
            message: 'App needs access to your photos to select images',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return photoPermission === PermissionsAndroid.RESULTS.GRANTED;
      }
      // For Android 12 and below
      else {
        const storagePermission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission',
            message: 'App needs access to your storage to select images',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return storagePermission === PermissionsAndroid.RESULTS.GRANTED;
      }
    } catch (err) {
      console.error('Permission error:', err);
      return false;
    }
  };

  /**
   * Pick an image from the device's gallery
   */
  const pickImage = async (): Promise<void> => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          'Permission Denied',
          'Storage permission is required to select images. Please enable it in app settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Open Settings',
              onPress: () => {
                // Open app settings if available
                if (Platform.OS === 'ios') {
                  Linking.openURL('app-settings:');
                } else {
                  Linking.openSettings();
                }
              },
            },
          ],
        );
        return;
      }

      const options = {
        mediaType: 'photo' as const,
        includeBase64: true,
        maxWidth: 500,
        maxHeight: 500,
      };

      const response = await new Promise<ImagePickerResponse>(resolve => {
        launchImageLibrary(options, resolve);
      });

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorCode) {
        console.log('ImagePicker Error: ', response.errorMessage);
        Alert.alert('Error', response.errorMessage || 'Failed to pick image');
      } else if (response.assets && response.assets[0].uri) {
        setImageUri(response.assets[0].uri);
        // If we have base64 data directly from the picker, save it for later use
        if (response.assets[0].base64) {
          console.log('Got base64 data directly from picker');
          setBase64Data(response.assets[0].base64);
        } else {
          setBase64Data(null);
        }
        setResult(null);
        setRawBinaryOutput(null);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image: ' + error);
    }
  };

  /**
   * Convert the selected image to 1-bit monochrome raw binary data
   */
  const convertImage = async (): Promise<void> => {
    if (!imageUri) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setProcessing(true);
    try {
      // Get the image dimensions
      const {width, height} = await new Promise<{
        width: number;
        height: number;
      }>((resolve, reject) => {
        Image.getSize(
          imageUri,
          (w, h) => resolve({width: w, height: h}),
          error => reject(error),
        );
      });

      console.log(`Image dimensions: ${width}x${height}`);

      // Get image data as base64
      let imageBase64: string | null;

      // Use cached base64 data if available
      if (base64Data) {
        imageBase64 = base64Data;
        console.log('Using cached base64 data');
      } else {
        imageBase64 = await getImageBase64(imageUri);
      }

      if (!imageBase64) {
        throw new Error('Failed to get image data');
      }

      console.log(`Got base64 data, length: ${imageBase64.length}`);

      // Convert base64 to binary data
      const imageData = base64.toByteArray(imageBase64);
      console.log(`Converted to byte array, length: ${imageData.length}`);

      // Calculate bytes per pixel
      const bytesPerPixel = imageData.length / (width * height);
      console.log(`Calculated ${bytesPerPixel.toFixed(2)} bytes per pixel`);

      // Convert to 1-bit monochrome
      const binaryData = convertToMonochrome(imageData, width, height);
      console.log(`Converted to monochrome, size: ${binaryData.length} bytes`);

      // Convert binary data to raw string where each byte is represented as a character
      let rawString = '';
      for (let i = 0; i < binaryData.length; i++) {
        rawString += String.fromCharCode(binaryData[i]);
      }

      console.log(`Created raw binary string, length: ${rawString.length}`);
      setRawBinaryOutput(rawString);

      // Save the binary data to a file as raw binary
      const filePath = `${RNFS.CachesDirectoryPath}/monochrome_image.bin`;
      await RNFS.writeFile(filePath, rawString, 'utf8');
      console.log(`File saved at: ${filePath}`);

      // For visualization, generate a preview of the first few bytes
      let hexPreview = '';
      const previewLength = Math.min(binaryData.length, 32);
      for (let i = 0; i < previewLength; i++) {
        hexPreview += binaryData[i].toString(16).padStart(2, '0') + ' ';
        if ((i + 1) % 8 === 0) hexPreview += '\n';
      }

      setResult(`Converted to 1-bit monochrome (${width}x${height})
Raw binary file saved at: ${filePath}
Size: ${binaryData.length} bytes

First ${previewLength} bytes (hex):
${hexPreview}`);
    } catch (error) {
      console.error('Conversion error:', error);
      Alert.alert('Conversion Error', `Failed to convert image: ${error}`);
    } finally {
      setProcessing(false);
    }
  };

  /**
   * Get base64 representation of an image
   */
  const getImageBase64 = async (uri: string): Promise<string | null> => {
    try {
      // Check if the uri already contains base64 data
      if (uri.includes('data:image')) {
        return uri.split(',')[1];
      }

      // For local file URIs (file://)
      if (uri.startsWith('file://')) {
        console.log('Reading file with RNFS...');
        const base64Data = await RNFS.readFile(uri, 'base64');
        return base64Data;
      }

      // For content:// URIs on Android
      if (Platform.OS === 'android' && uri.startsWith('content://')) {
        console.log('Converting content URI using RNFS...');
        try {
          // First try direct read
          const base64Data = await RNFS.readFile(uri, 'base64');
          return base64Data;
        } catch (error) {
          console.log('Direct read failed, trying to get real path');
          // Try to get the real path
          try {
            const realPath = await RNFS.stat(uri);
            if (realPath && realPath.path) {
              console.log(`Real path: ${realPath.path}`);
              const base64Data = await RNFS.readFile(realPath.path, 'base64');
              return base64Data;
            }
          } catch (pathError) {
            console.error('Failed to get real path:', pathError);
          }

          // If we reached here, rethrow the original error
          throw error;
        }
      }

      // Fallback to direct read
      console.log('Using direct read fallback');
      const base64Data = await RNFS.readFile(uri, 'base64');
      return base64Data;
    } catch (error) {
      console.error('Error getting image base64:', error);

      // Last resort - try to use Image.getSize with a data URL approach
      try {
        console.log('Attempting alternative image loading approach...');
        return new Promise((resolve, reject) => {
          // This is a simplified placeholder. In a real implementation,
          // you would need to use other methods to get pixel data.
          // This won't actually work in React Native without native modules.
          reject(new Error('No image loading method available'));
        });
      } catch (altError) {
        console.error('Alternative loading failed:', altError);
        return null;
      }
    }
  };

  /**
   * Convert RGBA image data to 1-bit monochrome
   */
  const convertToMonochrome = (
    imageData: Uint8Array,
    width: number,
    height: number,
  ): Uint8Array => {
    // Calculate bytes per pixel (most likely 4 for RGBA or 3 for RGB)
    // If we can't determine exactly, make a smart guess
    let bytesPerPixel = Math.round(imageData.length / (width * height));
    if (bytesPerPixel < 3) bytesPerPixel = 3; // Minimum of 3 for RGB
    if (bytesPerPixel > 4) bytesPerPixel = 4; // Maximum of 4 for RGBA

    console.log(`Using ${bytesPerPixel} bytes per pixel for conversion`);

    // Calculate output size (1 bit per pixel, packed into bytes)
    const outputSize = Math.ceil((width * height) / 8);
    const output = new Uint8Array(outputSize);

    // Initialize the output array with zeros
    for (let i = 0; i < outputSize; i++) {
      output[i] = 0;
    }

    console.log(
      `Processing ${width}x${height} image (${width * height} pixels)`,
    );

    // Loop through all pixels
    let pixelCount = 0;
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        // Calculate pixel position in the array
        const pos = (y * width + x) * bytesPerPixel;

        // Check bounds
        if (pos + 2 >= imageData.length) {
          continue;
        }

        // Get RGB values (skip alpha if present)
        const r = imageData[pos] || 0;
        const g = imageData[pos + 1] || 0;
        const b = imageData[pos + 2] || 0;

        // Convert to grayscale using luminosity method
        const grayscale = Math.round(0.299 * r + 0.587 * g + 0.114 * b);

        // Threshold to 1-bit (0 or 1)
        const bit = grayscale > 127 ? 1 : 0;

        // Calculate position in output buffer
        const bytePos = Math.floor((y * width + x) / 8);
        const bitPos = 7 - ((y * width + x) % 8); // MSB first

        // Make sure we're not out of bounds
        if (bytePos < output.length) {
          // Set the bit in the output byte
          if (bit) {
            output[bytePos] |= 1 << bitPos;
          }
        }

        pixelCount++;
      }
    }

    console.log(`Processed ${pixelCount} pixels`);
    return output;
  };

  /**
   * Copy raw binary output to clipboard
   */
  const copyRawBinaryToClipboard = async () => {
    if (rawBinaryOutput) {
      try {
        await Clipboard.setString(rawBinaryOutput);
        console.log(rawBinaryOutput);

        Alert.alert('Success', 'Raw binary data copied to clipboard');
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        Alert.alert('Error', 'Failed to copy data to clipboard');
      }
    }
  };

  /**
   * Generate a test image (black and white pattern)
   */
  const generateTestImage = () => {
    // Create a simple 8x8 test pattern (checker pattern)
    const width = 8;
    const height = 8;
    const outputSize = Math.ceil((width * height) / 8);
    const output = new Uint8Array(outputSize);

    // Set alternating bits for checker pattern
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const bit = (x + y) % 2; // Alternating 1s and 0s

        // Calculate position in output buffer
        const bytePos = Math.floor((y * width + x) / 8);
        const bitPos = 7 - ((y * width + x) % 8); // MSB first

        // Set the bit in the output byte
        if (bit) {
          output[bytePos] |= 1 << bitPos;
        }
      }
    }

    // Convert binary data to raw string
    let rawString = '';
    for (let i = 0; i < output.length; i++) {
      rawString += String.fromCharCode(output[i]);
    }
    setRawBinaryOutput(rawString);

    // Convert to hex for display
    let hexString = '';
    for (let i = 0; i < output.length; i++) {
      hexString += output[i].toString(16).padStart(2, '0') + ' ';
    }

    setResult(`Generated 8x8 test pattern (checker board):
Hex: ${hexString}
Binary representation:
${visualizeBinary(output, width, height)}`);
  };

  /**
   * Visualize binary data as text (for testing)
   */
  const visualizeBinary = (
    data: Uint8Array,
    width: number,
    height: number,
  ): string => {
    let result = '';
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const bytePos = Math.floor((y * width + x) / 8);
        const bitPos = 7 - ((y * width + x) % 8);
        const bit = data[bytePos] & (1 << bitPos) ? 1 : 0;
        result += bit ? '■' : '□';
      }
      result += '\n';
    }
    return result;
  };

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>Image to 1-bit Monochrome Converter</Text>

        <View style={styles.buttonContainer}>
          <Button title="Pick an Image" onPress={pickImage} />
          <View style={styles.buttonSpacer} />
          <Button title="Generate Test Pattern" onPress={generateTestImage} />
        </View>

        {imageUri && (
          <View style={styles.imageContainer}>
            <Image source={{uri: imageUri}} style={styles.image} />
            <Button
              title={processing ? 'Processing...' : 'Convert to Monochrome'}
              onPress={convertImage}
              disabled={processing}
            />
          </View>
        )}

        {processing && (
          <View style={styles.processingContainer}>
            <ActivityIndicator size="large" color="#0000ff" />
            <Text style={styles.processingText}>Processing image...</Text>
          </View>
        )}

        {result && (
          <View style={styles.resultContainer}>
            <Text style={styles.resultText}>{result}</Text>
          </View>
        )}

        {rawBinaryOutput && (
          <View style={styles.rawBinaryContainer}>
            <Text style={styles.sectionTitle}>Raw Binary Output:</Text>
            <Text style={styles.infoText}>
              (This is the binary data represented as characters, which may
              appear garbled)
            </Text>
            <View style={styles.rawPreviewContainer}>
              <Text style={styles.rawBinaryText}>
                {rawBinaryOutput.slice(0, 100)}
                {rawBinaryOutput.length > 100 ? '...' : ''}
              </Text>
            </View>
            <Button
              title="Copy Raw Binary Data"
              onPress={copyRawBinaryToClipboard}
            />
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  buttonSpacer: {
    width: 10,
  },
  imageContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  image: {
    width: 200,
    height: 200,
    marginBottom: 10,
    resizeMode: 'contain',
    backgroundColor: '#f0f0f0',
  },
  processingContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  processingText: {
    marginTop: 10,
    color: '#0000ff',
  },
  resultContainer: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 5,
    marginTop: 20,
    width: '100%',
  },
  resultText: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  rawBinaryContainer: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 5,
    marginTop: 20,
    width: '100%',
    alignItems: 'center',
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 12,
    fontStyle: 'italic',
    marginBottom: 10,
    textAlign: 'center',
  },
  rawPreviewContainer: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 3,
    width: '100%',
    marginBottom: 15,
  },
  rawBinaryText: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 12,
  },
});

/**
 * Import Clipboard if not already imported
 */
import {Clipboard} from 'react-native';

/**
 * Standalone utility function to convert an image to 1-bit monochrome binary data
 * @param imageUri URI of the image to convert
 * @returns Promise resolving to binary data
 */
export const convertImageToMonochromeBinary = async (
  imageUri: string,
): Promise<{binaryData: Uint8Array; rawBinaryString: string}> => {
  return new Promise((resolve, reject) => {
    Image.getSize(
      imageUri,
      async (width, height) => {
        try {
          // Get image data as base64
          let base64Data: string | null;

          if (imageUri.startsWith('data:image')) {
            base64Data = imageUri.split(',')[1];
          } else {
            base64Data = await RNFS.readFile(imageUri, 'base64');
          }

          if (!base64Data) {
            throw new Error('Could not read image data');
          }

          // Decode base64 to binary
          const imageData = base64.toByteArray(base64Data);

          // Calculate bytes per pixel
          let bytesPerPixel = Math.round(imageData.length / (width * height));
          if (bytesPerPixel < 3) bytesPerPixel = 3; // Minimum of 3 for RGB
          if (bytesPerPixel > 4) bytesPerPixel = 4; // Maximum of 4 for RGBA

          // Calculate output size (1 bit per pixel)
          const outputSize = Math.ceil((width * height) / 8);
          const output = new Uint8Array(outputSize);

          // Convert to 1-bit monochrome
          for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
              // Calculate pixel position
              const pos = (y * width + x) * bytesPerPixel;

              if (pos + 2 < imageData.length) {
                // Get RGB values
                const r = imageData[pos] || 0;
                const g = imageData[pos + 1] || 0;
                const b = imageData[pos + 2] || 0;

                // Calculate grayscale
                const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b);

                // Threshold to 1-bit
                const bit = gray > 127 ? 1 : 0;

                // Set the bit in output
                const bytePos = Math.floor((y * width + x) / 8);
                const bitPos = 7 - ((y * width + x) % 8); // MSB first

                if (bit && bytePos < output.length) {
                  output[bytePos] |= 1 << bitPos;
                }
              }
            }
          }

          // Convert to raw binary string
          let rawString = '';
          for (let i = 0; i < output.length; i++) {
            rawString += String.fromCharCode(output[i]);
          }

          resolve({binaryData: output, rawBinaryString: rawString});
        } catch (error) {
          reject(error);
        }
      },
      error => {
        reject(error);
      },
    );
  });
};

export default MonochromeConverter;
