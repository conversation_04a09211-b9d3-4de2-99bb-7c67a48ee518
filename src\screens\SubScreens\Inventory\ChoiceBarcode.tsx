import {
  View,
  Text,
  Modal,
  StyleSheet,
  FlatList,
  Alert,
  TouchableOpacity,
  TextInput,
  Keyboard,
  KeyboardAvoidingView,
  SafeAreaView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TouchableWithoutFeedback,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState, memo} from 'react';
import {
  Backround,
  MainText,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Header from '../../../components/Inventory/Header';
import {Formik} from 'formik';
import * as Yup from 'yup';
import {
  AdditionalInfo,
  Bump_Bar_Settings,
  Department,
  Get_Max_ID,
  Inventory,
  Inventory_Filter,
  Inventory_Reference,
  Kit_Index,
  Setup_TS_Buttons,
} from '../../../server/types';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  createData,
  findArrayDifference,
  GetAllItems,
  GetAllItemsWithFilter,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  GetItemsWithParams,
  GetLatestID,
  handleSearch,
  onSearchChange_Common,
  showAlert,
  showAlertOK,
  updateData,
} from '../../../utils/PublicHelper';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import AppButton from '../../../components/Inventory/AppButton';
import {
  createItem,
  deleteItem,
  fetchAllItems,
  fetchSingleItem,
} from '../../../server/service';
import {
  applyDefaults,
  applyDefaultsAddition,
  applyDefaultsKitIndex,
  applyDefaultsSetupTSButtons,
} from '../../../Validator/Inventory/Barcode';
import Search from '../../../components/Inventory/Search';
import CustomCheckbox from '../../../components/Inventory/CustomCheckbox';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import DataList from '../../../components/Inventory/AppList';
import {getInventoryPort} from '../../../server/InstanceTypes';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppFocus from '../../../components/Inventory/AppFocus';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import AppFilter from '../../../components/Inventory/AppFilter';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import LottieView from 'lottie-react-native';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

// Validation schema using Yup
const validationSchema = Yup.object().shape({
  barcode: Yup.string().required('Item Number is required'),
  description: Yup.string().required('Description is required'),
  prompt: Yup.string(),
  department: Yup.string().required('Department is required'),
});

type BarcodeScreenRouteProp = RouteProp<any, 'Barcode'>;

const ChoiceBarcode: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [itemData, setItemData] = useState<Inventory[]>(
    route.params?.ItemData || [],
  );
  const [departments, setDepartments] = useState<Department[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [invRefID, setInvRefID] = useState<Get_Max_ID[]>();
  const [setupTSID, setSetupTSID] = useState<Get_Max_ID[]>();
  const [inventoryData, setInventoryData] = useState<Inventory_Filter[]>([]);
  const [filteredData, setFilteredData] = useState<Inventory_Filter[]>([]);
  const [displayData, setDisplayData] = useState<Inventory_Filter[]>([]);
  const [checkedState, setCheckedState] = useState({});
  const [selectedItems, setSelectedItems] = useState<Inventory[]>(
    route.params?.ListData || [],
  );
  const [success, setSuccess] = useState<string>(
    route.params?.ItemData[0]?.ItemName || '',
  );
  const [initial, setInitial] = useState<Inventory[]>(
    route.params?.ListData || [],
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [isLookup, setIsLookup] = useState<boolean>(route.params?.IsLookup);
  // Pagination state
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>('');
  const [showLookup, setshowLookup] = useState<boolean>(false);
  const [filter, setFilter] = useState<boolean>(false);

  const [itemNumber, setItemNumber] = useState<string>(
    itemData[0]?.ItemNum || route.params?.ItemData || '',
  );
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);

  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');

  useEffect(() => {
    if (route.params?.ItemData) {
      setItemNumber(route.params?.ItemData || '');
    }
  }, []);

  const textInputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    initialDetails();
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const initialDetails = async () => {
    GetLatestID(
      (await getInventoryPort()).toString(),
      setInvRefID,
      '/getinventoryrefid',
    );
    GetLatestID(
      (await getInventoryPort()).toString(),
      setSetupTSID,
      '/getinventorysetupid',
    );
    GetAllItems<Department[]>(
      (await getInventoryPort()).toString(),
      '/GetDepartments',
      setDepartments,
      setLoading,
      false,
    );
  };

  useEffect(() => {
    if (selectedItems.length > 0) {
      selectedItems.forEach(item => {
        handleCheckboxChange(item, true);
      });
    }
  }, [selectedItems]);

  useFocusEffect(
    useCallback(() => {
      invNoPagination();
      setModalVisible(false);
    }, []),
  );

  useEffect(() => {
    if (
      selectedBrand ||
      selectedDepartment ||
      selectedVendor ||
      selectedSubCategory
    ) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply search debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery]);

  // Apply filters and search
  useEffect(() => {
    // Skip if inventoryData isn't loaded yet
    if (inventoryData.length === 0) return;

    // Reset pagination when filters change
    setPage(1);

    // Filter by department, vendor, brand, and subcategory
    const filtered = inventoryData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Also apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    setFilteredData(filtered);
    applyPagination(filtered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    inventoryData,
  ]);

  // Pagination function
  const applyPagination = (data: Inventory_Filter[], currentPage: number) => {
    const endIndex = currentPage * pageSize;
    const paginatedItems = data.slice(0, endIndex);
    setDisplayData(paginatedItems);
  };

  // Load more data when reaching the end of the list
  const handleLoadMore = useCallback(() => {
    if (page * pageSize < filteredData.length) {
      const nextPage = page + 1;
      setPage(nextPage);
      applyPagination(filteredData, nextPage);
    }
  }, [page, filteredData, pageSize]);

  // Apply pagination when page changes
  useEffect(() => {
    if (filteredData.length > 0 && page > 1) {
      applyPagination(filteredData, page);
    }
  }, [page, filteredData]);

  // Pull to refresh implementation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    setPage(1);

    try {
      await invNoPagination();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }

    setRefreshing(false);
  }, []);

  const invNoPagination = async () => {
    setInitialLoading(true);
    try {
      GetAllItemsWithFilter(
        (await getInventoryPort()).toString(),
        '/inventorynopg',
        data => {
          setInventoryData(data);
          setFilteredData(data);
          applyPagination(data, 1);
          setInitialLoading(false);
          setLoading(false);
        },
        () => {},
        setLoading,
        false,
      );
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setInitialLoading(false);
      setLoading(false);
    }
  };

  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));

  const initialValues = {
    barcode: itemData[0]?.ItemNum || itemNumber || '',
    description: itemData[0]?.ItemName || '',
    prompt: itemData[0]?.ItemName_Extra || '',
    department: itemData[0]?.Dept_ID || '',
  };

  const createBarcode = async (Values: any): Promise<boolean> => {
    const storeId = await AsyncStorage.getItem('STOREID');
    const ValidStore = storeId === null ? '1001' : storeId;
    const inventoryData: Partial<Inventory> = {
      ItemNum: Values?.barcode,
      ItemName: Values?.description,
      Dept_ID: Values?.department,
      ItemType: 3,
      ItemName_Extra: Values.prompt,
      Store_ID: ValidStore,
    };

    const applyDefault = applyDefaults(inventoryData);

    try {
      if (success === undefined || success === null || success === '') {
        await createData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/createbarcode',
        });
      } else {
        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });
      }
      return true;
    } catch (error) {
      console.error('Error creating barcode:', error);
      return false;
    }
  };

  const createInventoryRef = async (Values: any) => {
    const InventoryRef: Inventory_Reference = {
      ID: Number(invRefID),
      ItemNum: Values?.barcode,
      Store_ID: '1001',
    };

    const result = await createItem(
      (await getInventoryPort()).toString(),
      '/createinventoryref',
      InventoryRef,
    );
  };

  const createSetupTSButton = async (Values: any) => {
    const SetupTSButton: Setup_TS_Buttons = {
      Store_ID: '1001',
      Index: setupTSID,
      Caption: Values?.description,
      Option1: '',
      BackColor: 12632256,
      Ident: Values?.barcode,
    };

    const inventoryWithDefaults = applyDefaultsSetupTSButtons(SetupTSButton);

    const result = await createItem(
      (await getInventoryPort()).toString(),
      '/createsetupts',
      inventoryWithDefaults,
    );
  };

  const createKitIndex = async ({
    values,
    updateItem,
  }: {
    values: any;
    updateItem: Inventory;
  }) => {
    if (updateItem === undefined) {
      console.warn('No items selected to create Kit Index.');
      return;
    }
    try {
      const KitIndex: Partial<Kit_Index> = {
        Kit_ID: values?.barcode,
        Store_ID: '1001',
        ItemNum: updateItem.ItemNum || '',
        Index: 1,
        Description: updateItem.ItemName || '',
      };
      const inventoryWithDefaults = applyDefaultsKitIndex(KitIndex);
      const result = await createItem(
        (await getInventoryPort()).toString(),
        '/createkitindex',
        inventoryWithDefaults,
      );
      return result;
    } catch (error) {
      console.error('Error creating Kit Indexes:', error);
      throw error;
    }
  };

  const createAdditionalInfo = async (Values: any) => {
    const AdditionalInfo: Partial<AdditionalInfo> = {
      Store_ID: '1001',
      ItemNum: Values?.barcode,
    };

    const inventoryWithDefaults = applyDefaultsAddition(AdditionalInfo);

    const result = await createItem(
      (await getInventoryPort()).toString(),
      '/createinventoryaddit',
      inventoryWithDefaults,
    );
  };

  const createBumpBarSettings = async (Values: any) => {
    const BumpBarSettings: Bump_Bar_Settings = {
      Store_ID: '1001',
      ItemNum: Values?.barcode,
      Backcolor: 12632256,
      Forecolor: 0,
    };

    const result = await createItem(
      (await getInventoryPort()).toString(),
      '/createbumbar',
      BumpBarSettings,
    );
  };

  const createChoiceItem = async (values: any) => {
    try {
      if (!success) {
        await createBarcode(values);
        await createInventoryRef(values);
        //await createSetupTSButton(values);
        if (selectedItems.length > 0) {
          selectedItems.map(async (updateItem: Inventory) => {
            await createKitIndex({values, updateItem});
          });
        }
        //await createBumpBarSettings(values);
        await createAdditionalInfo(values);
      } else {
        await createBarcode(values);
        const results = findArrayDifference(selectedItems, initial);

        await Promise.all(
          results.map(async (updateItem: Inventory) => {
            await createKitIndex({values, updateItem});
            initial.push(updateItem);
          }),
        );
      }

      if (!success) {
        Alert.alert('Success', 'All items created successfully.');
        setSuccess(values.description);
        GetItemsParamsNoFilter<Inventory[]>(
          (await getInventoryPort()).toString(),
          '/Get_Inventory_KidIndex/:Kit_ID',
          setSelectedItems,
          {Kit_ID: itemData[0]?.ItemNum || values?.barcode},
        );
      } else {
        Alert.alert('Success', 'All items Updated successfully.');
        setSuccess(values.description);
        GetItemsParamsNoFilter<Inventory[]>(
          (await getInventoryPort()).toString(),
          '/Get_Inventory_KidIndex/:Kit_ID',
          setSelectedItems,
          {Kit_ID: itemData[0]?.ItemNum || values?.barcode},
        );
      }
      navigation.navigate('Lookup');
    } catch (error) {
      console.error('Error while creating choice item:', error);
      Alert.alert('Error', 'Failed to create some items. Please try again.');
    }
  };

  const handleCheckboxChange = (
    item: Inventory,
    isChecked: boolean,
    isCheckBox: boolean = false,
  ) => {
    if (isChecked) {
      // Update checked state
      setCheckedState(prevState => ({
        ...prevState,
        [String(item?.ItemNum)]: isChecked,
      }));
    } else {
      setCheckedState((prevState: any) => {
        const newState = {...prevState};
        delete newState[String(item?.ItemNum)];
        return newState;
      });
    }

    if (isCheckBox) {
      setSelectedItems((prevItems: Inventory[]) => {
        if (isChecked) {
          return [...prevItems, item];
        } else {
          return prevItems.filter(
            selected => selected?.ItemNum !== item?.ItemNum,
          );
        }
      });
    }
  };

  const removeListItem = (KidID: string, Barcode: string, isNew: string) => {
    if (itemData[0]?.Dept_ID === undefined) {
      const updatedArraySel = selectedItems.filter(
        item => item.ItemNum !== Barcode,
      );
      setSelectedItems(updatedArraySel);
      selectedItems.forEach(item => {
        handleCheckboxChange(item, false);
      });
    } else {
      showAlert('Are you sure you want to delete?')
        .then(async result => {
          if (result) {
            initial.map(async (exist: Inventory) => {
              if (exist.ItemNum !== Barcode) {
                const updatedArraySel = selectedItems.filter(
                  item => item.ItemNum !== Barcode,
                );
                setSelectedItems(updatedArraySel);
                selectedItems.forEach(item => {
                  handleCheckboxChange(item, false);
                });
              } else {
                const result = await deleteItem(
                  (await getInventoryPort()).toString(),
                  '/DeleteKitIndex/:Kit_ID/:ItemNum',
                  {Kit_ID: itemData[0].ItemNum || KidID, ItemNum: Barcode},
                );
                if (result) {
                  const updatedArraySel = selectedItems.filter(
                    item => item.ItemNum !== Barcode,
                  );
                  setSelectedItems(updatedArraySel);
                  selectedItems.forEach(item => {
                    handleCheckboxChange(item, false);
                  });
                }
              }
            });
          } else {
            console.log('Item will not be deleted');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const renderItem = ({item}: {item: Inventory}) => (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 10,
        paddingHorizontal: 20,
        marginTop: 10,
        marginHorizontal: 10,
        backgroundColor: colors.surface,
        borderRadius: 15,
        borderWidth: 1,
        borderColor: colors.border,
      }}>
      <Text style={{color: colors.text}}>{item.ItemName}</Text>
      <TouchableOpacity
        onPress={() =>
          removeListItem(
            itemData[0]?.ItemNum,
            item.ItemNum,
            itemData[0]?.Dept_ID,
          )
        }>
        <MaterialCommunityIcons name="delete" color={colors.error} size={30} />
      </TouchableOpacity>
    </View>
  );

  const handleSearchChange = async (text: string) => {
    setCamera(false);
    const getBarcode = await GetItemsParamsNoFilterNoReturn<Inventory_Filter>(
      (await getInventoryPort()).toString(),
      '/inventory/:ItemNum',
      {ItemNum: text},
    );
    setSearchQuery(getBarcode[0]?.ItemNum || text);
    if (text) {
      if (showLookup) {
        handleSearch(
          text,
          inventoryData,
          ['ItemName', 'ItemNum'],
          setFilteredData,
          setLoading,
        );
      } else {
        if (Array.isArray(getBarcode) && getBarcode.length === 0) {
          const userConfirmed = await showAlert(
            'Item not found. Do you want to create a new item?',
          );
          if (userConfirmed) {
            navigation.navigate('Barcode', {
              BARCODENEW: getBarcode[0]?.ItemNum || text,
              ISCHOICE: true,
            });
          } else {
            if (textInputRef.current) {
              textInputRef.current.clear();
              textInputRef.current.blur();
            }

            setSearchQuery('');
            Keyboard.dismiss();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 200);
          }
        } else {
          handleSearch(
            getBarcode[0]?.ItemNum || text,
            inventoryData,
            ['ItemName', 'ItemNum'],
            setFilteredData,
            setLoading,
          );
        }
      }
    } else {
      setFilteredData(inventoryData);
    }
  };
  const handleBack = () => {
    setModalVisible(!modalVisible);
    setSelectedBrand('');
    setSelectedDepartment('');
    setSelectedSubCategory('');
    setSelectedVendor('');
    setIsEnableFilter(false);
    setSearchQuery('');
    setPage(1);
    setshowLookup(false);
  };

  const checkItemExist = async (text: string) => {
    if (text) {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: text},
      );

      if (getBarcode) {
        showAlertOK(
          `This Item Number is Already in Use. By an Item With a Discription: ${getBarcode[0]?.ItemName}`,
          'Already in Use',
        );
      }
    }
  };

  const toggleLookup = useCallback((value: boolean) => {
    applyPagination(filteredData, page);
    setshowLookup(value);
    setSearchQuery('');

    if (Platform.OS === 'android') {
      if (value) {
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      } else {
        Keyboard.dismiss();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.blur();
            setTimeout(() => {
              if (textInputRef.current) {
                textInputRef.current.focus();
              }
            }, 50);
          }
        }, 50);
      }
      return;
    }

    // iOS handling
    if (value) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    } else {
      setSearchQuery('');
      Keyboard.dismiss();
    }
  }, []);

  // GetItemLayout for optimized rendering
  const getItemLayout = useCallback(
    (_, index) => ({
      length: 88, // approximate height of each item
      offset: 88 * index,
      index,
    }),
    [],
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: Inventory_Filter) => item.ItemNum.toString(),
    [],
  );

  const handleOutsidePress = () => {
    if (showLookup) {
      setshowLookup(false);
      Keyboard.dismiss();
    }
  };

  const [keyboardVisible, setKeyboardVisible] = useState<boolean>(false);

  // Memoize render item function for modal items
  const memoizedRenderModalItem = useCallback(
    ({item}) => (
      <InventoryItem
        item={item}
        onPress={(item, isChecked) =>
          handleCheckboxChange(item, isChecked, true)
        }
        checkedState={checkedState}
      />
    ),
    [checkedState],
  );

  const handleDoneClick = () => {
    applyPagination(filteredData, page);
    setSearchQuery('');
    setshowLookup(false);
    Keyboard.dismiss();
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.blur();
        setTimeout(() => {
          if (textInputRef.current) {
            textInputRef.current.focus();
          }
        }, 50);
      }
    }, 50);
  };

  const [camera, setCamera] = useState(false);
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        handleSearchChange(codes[0].value);
      }
    },
  });

  const colors = useThemeColors();
  const {isDark} = useTheme();

  // Memoized Item component for optimal rendering performance
  const InventoryItem = memo(
    ({
      item,
      onPress,
      checkedState,
    }: {
      item: Inventory_Filter;
      onPress: (item: Inventory_Filter, isChecked: boolean) => void;
      checkedState: any;
    }) => {
      const isChecked = checkedState[item.ItemNum] || false;

      return (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderRadius: 12,
            paddingVertical: hp('1.8%'),
            paddingHorizontal: wp('4%'),
            marginBottom: hp('1.2%'),
            backgroundColor: colors.surface,
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <CustomCheckbox
              isChecked={isChecked}
              onChange={checked => onPress(item, checked)}
            />
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                color: colors.text,
                fontSize: FontSizes.small,
                paddingHorizontal: 10,
              }}>
              {item.ItemName}
            </Text>
          </View>
          <Text
            style={{
              fontFamily: Fonts.OnestBold,
              color: colors.primary,
              fontSize: FontSizes.medium,
            }}>
            ${item.Price}
          </Text>
        </View>
      );
    },
    (prevProps, nextProps) => {
      // Only re-render if the item ID changes or checked state changes
      return (
        prevProps.item.ItemNum === nextProps.item.ItemNum &&
        prevProps.checkedState[prevProps.item.ItemNum] ===
          nextProps.checkedState[nextProps.item.ItemNum]
      );
    },
  );

  // Empty list component
  const EmptyListComponent = memo(({searchQuery}: {searchQuery: string}) => (
    <View style={styles.emptyContainer}>
      <View style={styles.animationContainer}>
        <LottieView
          style={styles.lottie}
          source={require('../../../assets/Lotties/Nodata.json')}
          autoPlay
          loop
        />
      </View>
      <Text style={styles.emptyText}>
        {searchQuery
          ? 'No matching items found'
          : 'No inventory items available'}
      </Text>
    </View>
  ));

  // Footer loading component
  const ListFooter = memo(({loading}: {loading: boolean}) => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator
          size="small"
          color={colors.primary} // In ListFooter component
        />
        <Text style={styles.footerText}>Loading more items...</Text>
      </View>
    );
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    tableCell: {
      textAlign: 'left',
      fontSize: 16,
      paddingHorizontal: 5,
      color: colors.text,
    },
    itemNum: {
      width: 120,
    },
    itemName: {
      flex: 1,
    },
    price: {
      width: 80,
      textAlign: 'right',
    },
    searchInput: {
      height: 40,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: 5,
      paddingLeft: 10,
      marginBottom: 10,
      marginHorizontal: 10,
      backgroundColor: colors.card,
      color: colors.text,
    },
    addItemsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
    },
    addItemsButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addItemsButtonText: {
      fontFamily: Fonts.OnestBold,
      fontSize: hp('2.3%'),
      color: colors.primary,
      paddingHorizontal: 10,
    },
    searchContainer: {
      marginBottom: 5,
    },
    itemCountContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: hp('1%'),
      paddingHorizontal: wp('2%'),
    },
    itemCountText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
    },
    listContent: {
      paddingBottom: hp('2%'),
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: hp('10%'),
    },
    animationContainer: {
      borderRadius: 20,
      backgroundColor: 'transparent',
    },
    lottie: {
      height: 200,
      width: 200,
      backgroundColor: 'transparent',
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: 20,
      fontFamily: Fonts.OnestMedium,
    },
    footer: {
      padding: 15,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    footerText: {
      marginLeft: 10,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: hp('10%'),
    },
  });
  return (
    <Formik
      initialValues={initialValues}
      enableReinitialize={true}
      validationSchema={validationSchema}
      onSubmit={values => {
        console.log(values);

        createChoiceItem(values);
      }}>
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        setFieldValue,
        values,
        errors,
        touched,
      }) => (
        <View style={{flex: 1}}>
          <View
            style={{
              backgroundColor: colors.background, // Change from Backround
              flex: 1,
              justifyContent: 'space-between',
            }}>
            <View style={{paddingHorizontal: wp('2.5%')}}>
              <Header NavName="Choice Item Create" />
              <ScrollView>
                <AppFocus
                  Title="Item Number"
                  PlaceHolder="Enter Item Number"
                  onChangeText={text => {
                    setItemNumber(text);
                    handleChange('barcode');
                    if (!showLookup) {
                      // setTimeout(() => {
                      checkItemExist(text);
                      // }, 1200);
                    }
                  }}
                  Value={values.barcode}
                  AutoFocus={itemData[0]?.ItemName ? false : true}
                  isBarcode={true}
                  isRequired={true}
                  Editable={!itemData[0]?.ItemName ? true : false}
                  textInputRef={textInputRef}
                  keyboardON={showLookup}
                  onToggleLookup={value => toggleLookup(value)}
                  onBlur={
                    showLookup ? () => checkItemExist(itemNumber) : undefined
                  }
                />
                <AppDropDown
                  label="Department"
                  options={departmentOptions}
                  selectedValue={values.department}
                  onSelect={value => setFieldValue('department', value)}
                  error={errors.department}
                  touched={touched.department}
                />

                <AppTextInput
                  PlaceHolder="Enter Description"
                  Title="Description"
                  Value={values.description}
                  onChangeText={handleChange('description')}
                  onBlur={handleBlur('description')}
                  error={errors.description}
                  touched={touched.description}
                  maxLength={30}
                />

                <AppTextInput
                  PlaceHolder="Enter Prompt"
                  Title="Prompt"
                  Value={values.prompt}
                  onChangeText={handleChange('prompt')}
                  onBlur={handleBlur('prompt')}
                  error={errors.prompt}
                  touched={touched.prompt}
                />

                <TouchableOpacity
                  style={styles.addItemsButton}
                  onPress={() => setModalVisible(true)}>
                  <View style={styles.addItemsButtonContent}>
                    <Text style={styles.addItemsButtonText}>Add Items</Text>
                    <View>
                      <AntDesign
                        name="pluscircle"
                        size={hp('2.5%')}
                        color={colors.primary} // Change from Primary
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </ScrollView>

              <View style={{height: hp('24%')}}>
                <DataList
                  data={selectedItems}
                  renderItem={renderItem}
                  loading={loading}
                />
              </View>
            </View>
            {/* </ScrollView> */}
            <View
              style={{
                paddingVertical: hp('1%'),
                paddingHorizontal: wp('2.5%'),
                backgroundColor: colors.surface, // Change from Secondary
                position: 'absolute',
                bottom: 0,
                right: 0,
                left: 0,
              }}>
              <FAB
                label={'Save & Close'}
                position="bottomRight"
                onPress={handleSubmit}
              />
            </View>

            {/* Improved Modal View */}
            <Modal
              animationType="slide"
              transparent={true}
              visible={modalVisible}
              onRequestClose={handleBack}>
              <TouchableWithoutFeedback onPress={handleOutsidePress}>
                <View style={styles.container}>
                  <Header
                    NavName="Add Items"
                    Onpress={handleBack}
                    isProvid={true}
                    isOption={true}
                    Options={() => {
                      if (textInputRef.current) {
                        textInputRef.current.clear();
                        textInputRef.current.blur();
                      }

                      setSearchQuery('');
                      Keyboard.dismiss();
                      setTimeout(() => {
                        if (textInputRef.current) {
                          textInputRef.current.focus();
                        }
                      }, 200);

                      setCamera(!camera);
                    }}
                  />

                  <View style={styles.searchContainer}>
                    <AppSearchWIthFilter
                      OnSearch={handleSearchChange}
                      SearchValue={searchQuery}
                      OnSearchSet={() => setFilter(true)}
                      isEnableFilter={isEnableFilter}
                      Keyboardon={showLookup}
                      textInputRef={textInputRef}
                      onToggleLookup={toggleLookup}
                      OnSubmitEditing={handleDoneClick}
                    />

                    <View style={styles.itemCountContainer}>
                      <Text style={styles.itemCountText}>{`Total Items: (${
                        filteredData.length || 0
                      })`}</Text>
                    </View>
                  </View>

                  {/* List Container */}
                  <View style={{flex: 1, paddingHorizontal: wp('2.5%')}}>
                    {initialLoading ? (
                      <View style={styles.centered}>
                        <ActivityIndicator
                          size="large"
                          color={colors.primary} // Change from MaterialColors.primary.main
                        />
                      </View>
                    ) : (
                      <FlatList
                        ref={flatListRef}
                        data={displayData}
                        renderItem={memoizedRenderModalItem}
                        keyExtractor={keyExtractor}
                        contentContainerStyle={styles.listContent}
                        onEndReached={handleLoadMore}
                        onEndReachedThreshold={0.5}
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        getItemLayout={getItemLayout}
                        ListEmptyComponent={
                          <EmptyListComponent searchQuery={searchQuery} />
                        }
                        ListFooterComponent={
                          <ListFooter loading={loading && !initialLoading} />
                        }
                        initialNumToRender={10}
                        maxToRenderPerBatch={10}
                        windowSize={10}
                        removeClippedSubviews={true}
                        updateCellsBatchingPeriod={75}
                        showsVerticalScrollIndicator={false}
                      />
                    )}
                  </View>

                  <View
                    style={{
                      paddingVertical: hp('1%'),
                      paddingHorizontal: wp('2.5%'),
                    }}>
                    <FAB
                      label={'Done'}
                      position="bottomRight"
                      onPress={handleBack}
                    />
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>

            <View style={{flex: 1}}>
              <AppFilter
                isVisible={filter}
                setIsVisble={setFilter}
                Department={text => setSelectedDepartment(text)}
                Vedor={text => setSelectedVendor(text)}
                Brand={text => setSelectedBrand(text)}
                Category={text => setSelectedSubCategory(text)}
                EnableFilter={setIsEnableFilter}
                selectedDepartment={selectedDepartment}
                selectedVendor={selectedVendor}
                selectedBrand={selectedBrand}
                selectedSubCategory={selectedSubCategory}
                isEnableFilter={isEnableFilter}
              />
            </View>

            <Modal
              animationType="fade"
              transparent={false}
              visible={camera}
              onRequestClose={() => setCamera(false)}>
              <AppScanner
                codeScanner={codeScanner}
                onClose={() => {
                  setCamera(false);
                  textInputRef?.current?.focus();
                }}
              />
            </Modal>
          </View>
        </View>
      )}
    </Formik>
  );
};

export default ChoiceBarcode;
