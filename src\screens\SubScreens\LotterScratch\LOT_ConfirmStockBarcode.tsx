import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Button,
  Alert,
  Modal,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {Inventory, Invoice_Itemized} from '../../../server/types';
import Header from '../../../components/Inventory/Header';
import DataList from '../../../components/Inventory/AppList';
import AppButton from '../../../components/Inventory/AppButton';
import {
  formatNumber,
  GetCommonLatestID,
  GetItemsParamsNoFilter,
  showAlert,
  updateData,
} from '../../../utils/PublicHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  Game_Details,
  Send_Email,
  Shift_Details,
} from '../../../Types/Lottery/Lottery_Types';
import {getLotteryPort} from '../../../server/InstanceTypes';
import {applyDefaultsGameDetails} from '../../../Validator/Lottery/Lottery_Validator';
import {createItem} from '../../../server/service';
import {ActivityIndicator} from 'react-native';
import AppScanner from '../../../components/Inventory/AppScanner';
import {useCodeScanner} from 'react-native-vision-camera';
import AppFocus from '../../../components/Inventory/AppFocus';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts} from '../../../styles/fonts';
import {
  Backround,
  Primary,
  Secondary,
  SecondaryHint,
} from '../../../constants/Color';
import Feather from 'react-native-vector-icons/Feather';
import AppTextInput from '../../../components/Inventory/AppTextInput';

export interface Lottery_Barcodes {
  Barcode: string;
}

type BarcodeScreenRouteProp = RouteProp<any, 'ConfirmBarcodes'>;
const LOT_ConfirmStockBarcode: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [scratchGame, setScratchGame] = useState<Inventory>(
    route.params?.ItemData || [],
  );
  const updateGoodItems = route.params?.updateGoodItems;
  const updateBadItems = route.params?.updateBadItems;

  const [gameDetails, setGameDetails] = useState<Game_Details[]>([]);
  const [shiftDetails, setShiftDetails] = useState<Shift_Details[]>([]);
  const [invoiceDetails, setInvoiceDetails] = useState<Invoice_Itemized[]>([]);
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [status, setStatus] = useState<boolean>(false);
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [emailSend, setEmailSend] = useState<boolean>(false);
  const [exist, setExists] = useState<Inventory>();
  const [camera, setCamera] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  useFocusEffect(
    useCallback(() => {
      getShiftStatus();
      loadSavedSerial();
    }, []),
  );

  // useEffect(() => {
  //   getItemAsync();
  // }, []);

  // const getItemAsync = async () => {

  //   //setCurrentBarcode(getExist)
  // };
  const loadSavedSerial = async () => {
    try {
      if (status) {
        const existingItemSerials = await AsyncStorage.getItem(
          'SetSerialNumber',
        );
        if (existingItemSerials) {
          const itemSerialsObject = JSON.parse(existingItemSerials);
          const itemName = scratchGame?.ItemName;
          if (itemName && itemSerialsObject[itemName]) {
            setCurrentBarcode(itemSerialsObject[itemName]);
          }
        }
      } else {
        const getExist = await AsyncStorage.getItem(scratchGame?.ItemName);

        if (!getExist || getExist === undefined) {
          setCurrentBarcode('');
        } else {
          setCurrentBarcode(getExist);
        }
      }
    } catch (error) {
      console.error('Error loading saved serial', error);
    }
  };
  const getLastShiftDetails = async () => {
    const Shift_ID = await GetCommonLatestID(
      (await getLotteryPort()).toString(),
      '/GetShiftID',
    );
    const gameDetails = await GetItemsParamsNoFilter(
      (await getLotteryPort()).toString(),
      '/GetGameDetails/:Shift_ID/:Location',
      setGameDetails,
      {Shift_ID: Number(Shift_ID) - 1, Location: scratchGame.Location},
    );
  };

  const getShiftStatus = async () => {
    await AsyncStorage.getItem('IsShiftStarted')
      .then(value => {
        const isShiftStarted = JSON.parse(value);
        if (isShiftStarted) {
          setStatus(isShiftStarted);
        } else {
          setStatus(false);
          getLastShiftDetails();
        }
      })
      .catch(error => console.error('Failed to fetch data', error));
  };

  const confirmStockOrEndShift = async () => {
    // const getBookDetails = await GetItemsParamsNoFilter(
    //   (await getLotteryPort()).toString(),
    //   '/GetShiftBookTickets/:ItemNum/:Location',
    //   setExists,
    //   {ItemNum: scratchGame.ItemNum, Location: scratchGame.Location},
    // );
    // let endGameSerial = currentBarcode.split('-');

    // if (isConfirm) {
    //   Alert.alert('Serial Already Updated!');
    // } else {
    // if (
    //   !currentBarcode ||
    //   currentBarcode === undefined ||
    //   currentBarcode === ''
    // ) {
    //   Alert.alert('Must Fill the Details!');
    //   return;
    // }

    // if (status) {
    //   try {
    //     const getShiftID = await AsyncStorage.getItem('Shift_ID');
    //     const gameDetails = await GetItemsParamsNoFilter(
    //       (await getLotteryPort()).toString(),
    //       '/GetGameDetails/:Shift_ID/:Location',
    //       setGameDetails,
    //       {Shift_ID: getShiftID, Location: scratchGame.Location},
    //     );

    //     let soltConfiramtion = 0;

    //     if (endGameSerial[1] === gameDetails[0]?.Open_Book) {
    //       let soltVerification =
    //         Number(endGameSerial[2]) -
    //         Number(gameDetails[0]?.Shift_Open_Serial);
    //       soltConfiramtion =
    //         Number(gameDetails[0]?.Stock_Level_Open) -
    //         Number(soltVerification);
    //     } else {
    //       const shiftBookDetails = await GetItemsParamsNoFilter(
    //         (await getLotteryPort()).toString(),
    //         '/GetShiftBookTickets/:ItemNum/:Location',
    //         setGameDetails,
    //         {ItemNum: scratchGame.ItemNum, Location: scratchGame.Location},
    //       );
    //       soltConfiramtion =
    //         Number(shiftBookDetails[0]?.Book_Tickets) -
    //         Number(endGameSerial[2]);
    //     }

    //     if (soltConfiramtion === scratchGame.In_Stock) {
    //       const shiftGameData: Partial<Game_Details> = {
    //         Game_ID: gameDetails[0]?.Game_ID,
    //         Open_Book: gameDetails[0]?.Open_Book,
    //         Close_Book: endGameSerial[1],
    //         Stock_Level_Open: gameDetails[0]?.Stock_Level_Open,
    //         Stock_Level_Close: scratchGame.In_Stock,
    //         Shift_Open_Serial: gameDetails[0]?.Shift_Open_Serial,
    //         Shift_Close_Serial: endGameSerial[2],
    //         Location: scratchGame.Location,
    //         Shift_ID: getShiftID?.toString(),
    //       };

    //       const applyDefault = applyDefaultsGameDetails(shiftGameData);
    //       const result = await updateData<Game_Details>({
    //         baseURL: (await getLotteryPort()).toString(),
    //         data: applyDefault,
    //         endpoint: '/updatelotgamesdetails',
    //       });
    //       if (result) {
    //         setIsConfirm(true);
    //         setCurrentBarcode('');

    //         const existingData = await AsyncStorage.getItem(
    //           'SetSerialNumber',
    //         );
    //         const existingArray = existingData
    //           ? JSON.parse(existingData)
    //           : [];
    //         existingArray.push(scratchGame?.ItemNum);
    //         await AsyncStorage.setItem(
    //           'SetSerialNumber',
    //           JSON.stringify(existingArray),
    //         );
    //         // const existingItemSerials = await AsyncStorage.getItem(
    //         //   'SetSerialNumber',
    //         // );
    //         // const itemSerialsObject = existingItemSerials
    //         //   ? JSON.parse(existingItemSerials)
    //         //   : {};

    //         // // Overwrite the current item’s serial
    //         // itemSerialsObject[scratchGame.ItemName] = currentBarcode;

    //         // await AsyncStorage.setItem(
    //         //   'SetSerialNumber',
    //         //   JSON.stringify(itemSerialsObject),
    //         // );

    //         Alert.alert('Looks Everything Good!');
    //       } else {
    //         setIsConfirm(false);
    //       }
    //     } else {
    //       setEmailSend(true);
    //       const reportMail: Send_Email = {
    //         name: `(${
    //           Number(scratchGame.In_Stock) - Number(soltConfiramtion)
    //         })Tickets Missing Report to Owner`,
    //       };

    //       const result = await createItem(
    //         (await getLotteryPort()).toString(),
    //         '/send-mail',
    //         reportMail,
    //       );

    //       if (result.message === 'E-mail Success') {
    //         Alert.alert(
    //           `(${
    //             Number(scratchGame.In_Stock) - Number(soltConfiramtion)
    //           })Tickets Missing Report to Owner`,
    //         );
    //         setEmailSend(false);
    //       } else {
    //         setEmailSend(false);
    //       }

    //       //mail service call
    //     }
    //   } catch (error) {
    //     console.log(error);
    //   }
    // } else {
    if (
      !currentBarcode ||
      currentBarcode === undefined ||
      currentBarcode === ''
    ) {
      Alert.alert('Must Fill the Details!');
      return;
    }

    try {
      showAlert("Check Everything Correct Once You Confirm After Cant't Edit?")
        .then(async result => {
          if (result) {
            let endGameSerial = currentBarcode.split('-');
            const getShiftID = await AsyncStorage.getItem('Shift_ID');
            const gameDetails = await GetItemsParamsNoFilter(
              (await getLotteryPort()).toString(),
              '/GetGameDetails/:Shift_ID/:Location',
              setGameDetails,
              {Shift_ID: getShiftID, Location: scratchGame.Location},
            );

            let soltConfiramtion = 0;
            if (endGameSerial[1] === gameDetails[0]?.Open_Book) {
              let soltVerification =
                Number(endGameSerial[2]) -
                Number(gameDetails[0]?.Shift_Open_Serial);
              soltConfiramtion =
                Number(gameDetails[0]?.Stock_Level_Open) -
                Number(soltVerification);
            } else {
              const shiftBookDetails = await GetItemsParamsNoFilter(
                (await getLotteryPort()).toString(),
                '/GetShiftBookTickets/:ItemNum/:Location',
                setGameDetails,
                {ItemNum: scratchGame.ItemNum, Location: scratchGame.Location},
              );
              soltConfiramtion =
                Number(shiftBookDetails[0]?.Book_Tickets) -
                Number(endGameSerial[2]);
            }

            if (soltConfiramtion === scratchGame.In_Stock) {
              //Good

              updateGoodItems(scratchGame.ItemName);
            } else {
              updateBadItems(scratchGame.ItemName);
              //Bad
            }

            const existingData = await AsyncStorage.getItem('SetSerialNumber');
            const existingArray = existingData ? JSON.parse(existingData) : [];

            const indexToUpdate = existingArray.some(
              item => item.ItemNum === scratchGame?.ItemNum,
            );
            if (indexToUpdate) {
              const indexToUpdateoldValue = existingArray.findIndex(
                item => item.ItemNum === scratchGame?.ItemNum,
              );

              existingArray[indexToUpdateoldValue].EndSerial = currentBarcode;
            } else {
              existingArray.push({
                ItemNum: scratchGame.ItemNum,
                EndSerial: currentBarcode,
              });
            }

            await AsyncStorage.setItem(
              'SetSerialNumber',
              JSON.stringify(existingArray),
            );
            //setIsConfirm(true);
            AsyncStorage.setItem(scratchGame?.ItemName, currentBarcode).catch(
              error => console.error('Failed to save data', error),
            );
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    } catch (error) {
      console.log(error);
    }
    //}
    //}
  };

  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        setCurrentBarcode(codes[0].value);
        setCamera(false);
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  return (
    <View
      style={{
        width: wp('100%'),
        height: hp('100%'),
        backgroundColor: Backround,
        paddingHorizontal: wp('2.5%'),
      }}>
      <View>
        <Header NavName="Confirm Stock" />
        <View>
          <Text style={{fontSize: hp('3%'), fontFamily: Fonts.OnestBold}}>
            {scratchGame?.ItemName}
          </Text>
          <Text
            style={{
              fontSize: hp('2.5%'),
              fontFamily: Fonts.OnestBold,
              color: SecondaryHint,
            }}>
            Available Tickets #: {scratchGame.In_Stock}
          </Text>
        </View>

        {!status && (
          <View
            style={{
              backgroundColor: Secondary,
              width: '99%',
              height: '35%',
              marginVertical: hp('2.5%'),
              borderRadius: 10,
            }}>
            <Text
              style={{
                textAlign: 'center',
                fontFamily: Fonts.OnestBold,
                fontSize: hp('2.5%'),
                paddingVertical: hp('1.5%'),
              }}>
              Review Last Shift
            </Text>

            <View style={{gap: 10, paddingHorizontal: 20}}>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('2%'),
                }}>
                Last Book #: {gameDetails[0]?.Close_Book || 'Not Available'}
              </Text>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('2%'),
                }}>
                Last Serial #:{' '}
                {gameDetails[0]?.Shift_Close_Serial || 'Not Available'}
              </Text>
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('2%'),
                }}>
                Last Closed Stock #:{' '}
                {gameDetails[0]?.Stock_Level_Close || 'Not Available'}
              </Text>
            </View>
          </View>
        )}

        {false && (
          <AppFocus
            PlaceHolder="Scan Book Number"
            Title="Scan Book Number"
            Value={currentBarcode}
            onChangeText={value => {
              const formattedSerial = formatNumber(value);
              setCurrentBarcode(formattedSerial);
            }}
          />
        )}
        {/* <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            padding: 10,
            width: '100%',
          }}>
          <TextInput
            placeholder="Scan barcode"
            editable={isConfirm}
            onChangeText={value => setCurrentBarcode(value)}
            value={currentBarcode}
            style={{
              borderColor: 'red',
              borderRadius: 10,
              borderWidth: 2,
              fontSize: 18,
              width: 275,
            }}
          />

          <View style={{marginLeft: 10}}>
            <AppButton Title="Scan" OnPress={() => setCamera(true)} />
          </View>
        </View> */}

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View style={{width: wp('80%'), marginTop: 20}}>
            <AppFocus
              PlaceHolder="Scan Book Number"
              Value={currentBarcode}
              Title="Scan Book Number"
              onChangeText={value => {
                const formattedSerial = formatNumber(value);
                setCurrentBarcode(formattedSerial);
              }}
            />
          </View>
          <TouchableOpacity
            onPress={() => setCamera(!camera)}
            style={{
              width: wp('12%'),
              height: hp('5%'),
              marginTop: hp('1%'),
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Primary,
              borderRadius: 5,
            }}>
            <Feather name="camera" color={Backround} size={hp('3.5%')} />
          </TouchableOpacity>
        </View>
        {currentBarcode.length > 0 && (
          <View
            style={{
              backgroundColor: 'yellow',
              marginVertical: 20,
              justifyContent: 'space-between',
              flexDirection: 'row',
              padding: 15,
              alignItems: 'center',
            }}>
            <Text>{currentBarcode}</Text>
            <TouchableOpacity onPress={() => setCurrentBarcode('')}>
              <MaterialCommunityIcons name="delete" color={'#000'} size={30} />
            </TouchableOpacity>
          </View>
        )}

        <View style={{marginTop: 20}}>
          <AppButton
            Title={'Confirm'}
            OnPress={() => confirmStockOrEndShift()}
            //disabled={currentBarcode === '' ? true : false}
          />
        </View>
        {emailSend && <ActivityIndicator size={50} />}
      </View>
      <Modal
        animationType="slide"
        transparent={true}
        visible={camera}
        onRequestClose={() => setCamera(!camera)}>
        <View style={{width: '100%', height: '100%'}}>
          <AppScanner codeScanner={codeScanner} />
        </View>
      </Modal>
    </View>
  );
};

export default LOT_ConfirmStockBarcode;
