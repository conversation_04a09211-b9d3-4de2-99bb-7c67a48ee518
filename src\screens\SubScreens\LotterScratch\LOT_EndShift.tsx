
import { View, Text } from 'react-native'
import React from 'react'
import { Backround } from '../../../constants/Color'
import Header from '../../../components/Inventory/Header'
import AppTextInput from '../../../components/Inventory/AppTextInput'
import AppButton from '../../../components/Inventory/AppButton'
import AssignBookCart from '../../../components/LotteryScratch/AssignBookCart'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import SearchBar from '../../../components/LotteryScratch/SearchBar'
type NavProps = {
    navigation: NativeStackNavigationProp<any>; 
  };
  
  
  
  const LOT_EndShift: React.FC<NavProps> = ({ navigation }) => {
  return (
    <View style={{backgroundColor: Backround, width: '100%', height: '100%'}}>
        <Header NavName='End Shift'/>
        <SearchBar />
        <View style={{width: "100%", flexDirection: 'row', alignItems: 'center', paddingHorizontal: 10}}>
            <View style={{width: "70%", paddingHorizontal: 10}}>
                <AppTextInput Value='03/14/2024'/>
            </View>

            <View style={{width: "30%"}}>
                <AppButton Title='Show'/>
            </View>

        </View>
        <View style={{ paddingHorizontal: 20, gap: 30, marginTop: 40}}>
            <AssignBookCart Onpress={() => navigation.navigate("FinalizePage")}/>
            <AssignBookCart />
            <AssignBookCart />
            <AssignBookCart />
            <AssignBookCart />
            <AssignBookCart />
        </View>
    </View>
  )
}

export default LOT_EndShift