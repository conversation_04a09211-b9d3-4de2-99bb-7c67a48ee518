// src/constants/colors.ts
export interface ThemeColors {
  background: string;
  surface: string;
  primary: string;
  secondary: string;
  text: string;
  textSecondary: string;
  border: string;
  card: string;
  notification: string;
  success: string;
  warning: string;
  error: string;
  shadow: string;
  accent: string;
  disabled: string;
  placeholder: string;
}

export const lightTheme: ThemeColors = {
  background: '#FFFFFF',
  surface: '#F8F9FA',
  primary: '#007AFF',
  secondary: '#5856D6',
  text: '#000000',
  textSecondary: '#6C757D',
  border: '#E9ECEF',
  card: '#FFFFFF',
  notification: '#FF3B30',
  success: '#28A745',
  warning: '#FFC107',
  error: '#DC3545',
  shadow: '#000000',
  accent: '#17A2B8',
  disabled: '#ADB5BD',
  placeholder: '#9CA3AF',
};

export const darkTheme: ThemeColors = {
  background: '#000000',
  surface: '#1C1C1E',
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  text: '#FFFFFF',
  textSecondary: '#EBEBF5',
  border: '#38383A',
  card: '#2C2C2E',
  notification: '#FF453A',
  success: '#30D158',
  warning: '#FF9F0A',
  error: '#FF453A',
  shadow: '#000000',
  accent: '#64D2FF',
  disabled: '#48484A',
  placeholder: '#8E8E93',
};

export const getThemeColors = (isDark: boolean): ThemeColors => {
  return isDark ? darkTheme : lightTheme;
};