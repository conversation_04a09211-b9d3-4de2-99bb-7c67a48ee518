import { useState, useEffect, useCallback } from 'react';
import { Inventory_Filter, Department, Vendor, BrandOrSubCategory, Brands, SubCategories } from '../../server/types';
import { CountItemInput } from './useCountItemData';

export const useCountItemFilters = (
  masterData: Inventory_Filter[],
  inputValues: CountItemInput[],
  debouncedQuery: string,
  onFilteredDataChange: (data: Inventory_Filter[]) => void,
  applyPagination: (data: Inventory_Filter[], page: number) => void
) => {
  // Filter state
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('');
  const [brandorSubCat, setBrandSubOrCat] = useState<BrandOrSubCategory[]>([]);
  const [getBrands, setGetBrands] = useState<Brands[]>([]);
  const [getSubCategories, setGetSubCategories] = useState<SubCategories[]>([]);
  const [isEnableFilter, setIsEnableFilter] = useState<boolean>(false);

  // Check if filters are enabled
  useEffect(() => {
    if (selectedBrand || selectedDepartment || selectedVendor || selectedSubCategory) {
      setIsEnableFilter(true);
    } else {
      setIsEnableFilter(false);
    }
  }, [selectedBrand, selectedDepartment, selectedVendor, selectedSubCategory]);

  // Apply filters and search
  useEffect(() => {
    if (masterData.length === 0) return;

    // Filter by department, vendor, brand, and subcategory
    const filtered = masterData.filter(item => {
      const matchesDepartment = selectedDepartment
        ? item.Dept_ID === selectedDepartment
        : true;
      const matchesVendor = selectedVendor
        ? item.Vendor_Number === selectedVendor
        : true;
      const matchesBrand = selectedBrand ? item.Brand === selectedBrand : true;
      const matchesSubCategory = selectedSubCategory
        ? item.SubCategory === selectedSubCategory
        : true;

      // Apply search filter if we have a query
      const searchTerm = debouncedQuery.toLowerCase();
      const matchesSearch = !debouncedQuery
        ? true
        : (item.ItemNum?.toString().toLowerCase() || '').includes(searchTerm) ||
          (item.ItemName?.toLowerCase() || '').includes(searchTerm);

      return (
        matchesDepartment &&
        matchesVendor &&
        matchesBrand &&
        matchesSubCategory &&
        matchesSearch
      );
    });

    // Sort data - put items with input values first
    const sortedFiltered = [...filtered].sort((a, b) => {
      const aHasInput = inputValues.some(
        input => input.itemNum === a.ItemNum && input.value,
      );
      const bHasInput = inputValues.some(
        input => input.itemNum === b.ItemNum && input.value,
      );

      if (aHasInput && !bHasInput) return -1;
      if (!aHasInput && bHasInput) return 1;
      return 0;
    });

    onFilteredDataChange(sortedFiltered);
    applyPagination(sortedFiltered, 1);
  }, [
    selectedDepartment,
    selectedVendor,
    selectedBrand,
    selectedSubCategory,
    debouncedQuery,
    masterData,
    inputValues,
    onFilteredDataChange,
    applyPagination,
  ]);

  // Reset filters
  const resetFilters = useCallback(() => {
    setSelectedDepartment('');
    setSelectedVendor('');
    setSelectedBrand('');
    setSelectedSubCategory('');
    setIsEnableFilter(false);
  }, []);

  return {
    // Filter state
    departments,
    setDepartments,
    selectedDepartment,
    setSelectedDepartment,
    vendors,
    setVendors,
    selectedVendor,
    setSelectedVendor,
    selectedBrand,
    setSelectedBrand,
    selectedSubCategory,
    setSelectedSubCategory,
    brandorSubCat,
    setBrandSubOrCat,
    getBrands,
    setGetBrands,
    getSubCategories,
    setGetSubCategories,
    isEnableFilter,
    setIsEnableFilter,
    
    // Functions
    resetFilters,
  };
};
