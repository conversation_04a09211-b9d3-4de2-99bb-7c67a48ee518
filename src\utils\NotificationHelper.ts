import AsyncStorage from '@react-native-async-storage/async-storage';
import {AvailabilityInfo} from '../Types/GlobalTypes';
import {getLotteryPort} from '../server/InstanceTypes';
import {GetItemsParamsNoFilterNoReturn} from './PublicHelper';

export interface WorkingDay {
  day: string;
  enabled: boolean;
  openTime: Date;
  closeTime: Date;
}

export interface NotificationSettings {
  workingDays: WorkingDay[];
  reportSendTime: Date;
}

const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
];

const parseTimeString = (timeStr: string): Date => {
  const match = timeStr.match(/(\d+):(\d+)\s*(AM|PM)/i);
  if (!match) {
    return new Date(2023, 0, 1, 9, 0); // Default 9:00 AM
  }

  let hours = parseInt(match[1], 10);
  const minutes = parseInt(match[2], 10);
  const period = match[3].toUpperCase();

  if (period === 'PM' && hours !== 12) {
    hours += 12;
  }
  if (period === 'AM' && hours === 12) {
    hours = 0;
  }

  return new Date(2023, 0, 1, hours, minutes);
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
};

export const getOrgId = async (): Promise<string> => {
  const userData = await AsyncStorage.getItem('userData');
  if (!userData) {
    return '';
  }

  const parsedUserData = JSON.parse(userData);
  const id = parsedUserData.id || '';
  return id.toString();
};

export const getNotificationSettings =
  async (): Promise<NotificationSettings | null> => {
    try {
      const orgId = await getOrgId();
      if (!orgId) {
        return null;
      }

      const response = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetAvailabilityInfo/:org_id',
        {org_id: orgId.toString()},
      );

      // Return default settings if no data
      if (
        !response ||
        typeof response !== 'object' ||
        Object.keys(response).length === 0
      ) {
        return getDefaultSettings();
      }

      const data = response as AvailabilityInfo;
      return parseServerData(data);
    } catch (error) {
      console.error('Error loading notification settings:', error);
      return null;
    }
  };

const getDefaultSettings = (): NotificationSettings => {
  const workingDays = DAYS_OF_WEEK.map(day => ({
    day,
    enabled: false,
    openTime: parseTimeString('9:00 AM'),
    closeTime: parseTimeString('6:00 PM'),
  }));

  return {
    workingDays,
    reportSendTime: parseTimeString('5:00 PM'),
  };
};

const parseServerData = (data: AvailabilityInfo): NotificationSettings => {
  const availableDaysStr = data.available_days || '';
  let enabledDays: string[] = [];

  if (availableDaysStr === 'Monday-Friday') {
    enabledDays = DAYS_OF_WEEK.slice(0, 5);
  } else if (availableDaysStr === 'Monday-Sunday') {
    enabledDays = DAYS_OF_WEEK;
  } else if (availableDaysStr) {
    enabledDays = availableDaysStr.split(', ').map(d => d.trim());
  }

  // Parse individual day times or use default
  const timeStr = data.available_time || '';
  const dayTimesMap = new Map<string, {open: string; close: string}>();

  if (timeStr.includes(':')) {
    // New format: "Monday: 9:00 AM-6:00 PM, Tuesday: 10:00 AM-7:00 PM"
    const dayTimeEntries = timeStr.split(', ');
    dayTimeEntries.forEach(entry => {
      const dayTimeParts = entry.split(': ');
      if (dayTimeParts.length === 2) {
        const day = dayTimeParts[0].trim();
        const times = dayTimeParts[1].split('-');
        if (times.length === 2) {
          dayTimesMap.set(day, {
            open: times[0].trim(),
            close: times[1].trim(),
          });
        }
      }
    });
  } else if (timeStr.includes('-')) {
    // Old format: "9AM-6PM" (same time for all days)
    const [open, close] = timeStr.split('-');
    if (open && close) {
      const defaultOpen = open.trim().replace(/(\d+)(AM|PM)/i, '$1:00 $2');
      const defaultClose = close.trim().replace(/(\d+)(AM|PM)/i, '$1:00 $2');
      enabledDays.forEach(day => {
        dayTimesMap.set(day, {open: defaultOpen, close: defaultClose});
      });
    }
  }

  const workingDays = DAYS_OF_WEEK.map(day => {
    const isEnabled = enabledDays.includes(day);
    const dayTimes = dayTimesMap.get(day);

    return {
      day,
      enabled: isEnabled,
      openTime: parseTimeString(dayTimes?.open || '9:00 AM'),
      closeTime: parseTimeString(dayTimes?.close || '6:00 PM'),
    };
  });

  return {
    workingDays,
    reportSendTime: parseTimeString(data.report_send_time || '5:00 PM'),
  };
};

export const saveNotificationSettings = async (
  settings: NotificationSettings,
): Promise<boolean> => {
  try {
    const orgId = await getOrgId();
    if (!orgId) {
      console.error('No organization ID found');
      return false;
    }

    const enabledDays = settings.workingDays.filter(day => day.enabled);

    // Format data for server
    let availableDays = '';
    let availableTime = '';

    if (enabledDays.length > 0) {
      // Format available days
      if (
        enabledDays.length === 5 &&
        enabledDays.every(d => DAYS_OF_WEEK.slice(0, 5).includes(d.day))
      ) {
        availableDays = 'Monday-Friday';
      } else if (enabledDays.length === 7) {
        availableDays = 'Monday-Sunday';
      } else {
        availableDays = enabledDays.map(d => d.day).join(', ');
      }

      // Format available time for each day
      const dayTimes = enabledDays.map(
        day =>
          `${day.day}: ${formatTime(day.openTime)}-${formatTime(
            day.closeTime,
          )}`,
      );
      availableTime = dayTimes.join(', ');
    }
    const existingData = await hasExistingData(orgId);

    const data = {
      org_id: orgId.toString(),
      available_days: availableDays,
      available_time: availableTime,
      report_send_time: formatTime(settings.reportSendTime),
      is_ticket_ascending: existingData?.is_ticket_ascending ?? null,
    };

    // Check if data exists to decide POST vs PUT
    const hasData = !!existingData;
    const endpoint = hasData
      ? '/updateavailabilityinfo'
      : '/createavailabilityinfo';
    const method = hasData ? 'PUT' : 'POST';

    const response = await fetch(`${await getLotteryPort()}${endpoint}`, {
      method,
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify(data),
    });

    console.log(data, 'Data sent to server:', response);

    // Fallback to PUT if POST fails
    if (!response.ok && !hasData) {
      const fallbackResponse = await fetch(
        `${await getLotteryPort()}/updateavailabilityinfo`,
        {
          method: 'PUT',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify(data),
        },
      );
      return fallbackResponse.ok;
    }

    return response.ok;
  } catch (error) {
    console.error('Error saving notification settings:', error);
    return false;
  }
};

export const hasExistingData = async (
  orgId: string,
): Promise<AvailabilityInfo | null> => {
  try {
    const response = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetAvailabilityInfo/:org_id',
      {org_id: orgId.toString()},
    );

    if (
      response &&
      typeof response === 'object' &&
      Object.keys(response).length > 0
    ) {
      return response as AvailabilityInfo;
    }

    return null;
  } catch (error) {
    console.error('Error checking existing data:', error);
    return null;
  }
};

// Get availability info for ticket ordering
export const getAvailabilityInfo = async (
  orgId: string,
): Promise<AvailabilityInfo | null> => {
  try {
    const response = await GetItemsParamsNoFilterNoReturn(
      (await getLotteryPort()).toString(),
      '/GetAvailabilityInfo/:org_id',
      {org_id: orgId.toString()},
    );

    if (
      !response ||
      typeof response !== 'object' ||
      Object.keys(response).length === 0
    ) {
      return null;
    }

    return response as AvailabilityInfo;
  } catch (error) {
    console.error('Error getting availability info:', error);
    return null;
  }
};

// Create availability info
export const createAvailabilityInfo = async (
  data: Partial<AvailabilityInfo>,
): Promise<boolean> => {
  try {
    const response = await fetch(
      `${await getLotteryPort()}/createavailabilityinfo`,
      {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(data),
      },
    );

    if (response.ok) {
      return true;
    } else {
      console.error(
        'Failed to create availability info:',
        await response.text(),
      );
      return false;
    }
  } catch (error) {
    console.error('Error creating availability info:', error);
    return false;
  }
};

// Update availability info
export const updateAvailabilityInfo = async (
  data: Partial<AvailabilityInfo>,
): Promise<boolean> => {
  try {
    const response = await fetch(
      `${await getLotteryPort()}/updateavailabilityinfo`,
      {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(data),
      },
    );

    if (response.ok) {
      return true;
    } else {
      console.error(
        'Failed to update availability info:',
        await response.text(),
      );
      return false;
    }
  } catch (error) {
    console.error('Error updating availability info:', error);
    return false;
  }
};
