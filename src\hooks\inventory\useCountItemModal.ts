import { useState, useCallback, useEffect } from 'react';
import { showAlertOK } from '../../utils/PublicHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useCountItemModal = (
  actionRef: React.MutableRefObject<boolean>,
  handleInputChange: (value: string, itemNum: string) => void,
  updateStockInData: (itemNum: string, newStock: string) => void,
  clearSearchAndFocus: () => void
) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalItemId, setModalItemId] = useState<string | null>(null);
  const [modalStockInput, setModalStockInput] = useState<string>('');

  // Debug modal state changes
  useEffect(() => {
    console.log('Modal state changed - modalVisible:', modalVisible, 'modalItemId:', modalItemId);
  }, [modalVisible, modalItemId]);

  // Open modal for specific item
  const openModalForItem = useCallback(
    async (itemNum: string, deptId?: string) => {
      console.log('openModalForItem called with:', itemNum, 'actionRef.current:', actionRef.current);
      if (!actionRef.current) {
        console.log('Physical count not started, showing alert');
        setTimeout(() => {
          showAlertOK(
            'To proceed, please initialize the physical count by clicking \'Start Physical Count\'',
            'Physical Count Not Started',
          );
        }, 100);
        return;
      }

      // Check lottery department restriction
      const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
      if (lotteryDepartment && lotteryDepartment === deptId) {
        setTimeout(() => {
          showAlertOK(
            'This item cannot be counted at the moment due to system restrictions or configuration settings. Please contact support if you believe this is an error',
            'Not Able to Count',
            'OK',
          );
        }, 100);
        return;
      }

      console.log('Setting modal visible for item:', itemNum);
      setModalItemId(itemNum);
      setModalStockInput('');
      setModalVisible(true);
      console.log('Modal should now be visible');
    },
    [actionRef]
  );

  // Handle modal input change
  const handleModalInputChange = useCallback(
    (value: string) => {
      if (modalItemId) {
        handleInputChange(value, modalItemId);
        setModalStockInput(value);
      }
    },
    [modalItemId, handleInputChange]
  );

  // Handle done click on modal
  const handleDoneClick = useCallback(() => {
    if (modalItemId && modalStockInput) {
      updateStockInData(modalItemId, modalStockInput);
    }

    setModalVisible(false);
    setModalItemId(null);
    setModalStockInput('');
    clearSearchAndFocus();
  }, [modalItemId, modalStockInput, updateStockInData, clearSearchAndFocus]);

  // Close modal
  const closeModal = useCallback(() => {
    setModalVisible(false);
    setModalItemId(null);
    setModalStockInput('');
  }, []);

  return {
    modalVisible,
    modalItemId,
    modalStockInput,
    openModalForItem,
    handleModalInputChange,
    handleDoneClick,
    closeModal,
    setModalVisible,
  };
};
