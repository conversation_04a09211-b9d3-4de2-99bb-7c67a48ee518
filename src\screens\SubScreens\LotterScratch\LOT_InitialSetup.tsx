import {
  View,
  Text,
  Alert,
  TextInput,
  Modal,
  StyleSheet,
  Keyboard,
  InteractionManager,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import Header from '../../../components/Inventory/Header';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import {
  Department,
  Inventory,
  Inventory_In,
  Invoice_Itemized,
  Reason_Codes,
} from '../../../server/types';
import {
  createData,
  formatNumber,
  GetAllItems,
  GetCommonLatestID,
  getFormateDate,
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
  showAlertOK,
  updateData,
  getSerialNumberOrder,
} from '../../../utils/PublicHelper';
import DataList from '../../../components/Inventory/AppList';
import {TouchableOpacity} from 'react-native';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import AppButton from '../../../components/Inventory/AppButton';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  applyDefaultsGameDetails,
  applyDefaultsShiftDetails,
} from '../../../Validator/Lottery/Lottery_Validator';
import {
  Game_Details,
  Send_Email,
  Shift_Details,
  Activate_Book,
} from '../../../Types/Lottery/Lottery_Types';
import {getLotteryPort, getInventoryPort} from '../../../server/InstanceTypes';
import TicketsDetails from '../../../components/LotteryScratch/TicketsDetails';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, Primary, SecondaryHint} from '../../../constants/Color';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {createItem} from '../../../server/service';
import AntDesign from 'react-native-vector-icons/AntDesign';
import AppFocus from '../../../components/Inventory/AppFocus';
import Feather from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useCodeScanner} from 'react-native-vision-camera';
import {
  applyDefaults,
  applyDefaultsInventoryAdjust,
} from '../../../Validator/Inventory/Barcode';
import {MaterialColors} from '../../../constants/MaterialColors';
import FAB from '../../../components/common/FAB';
import AppFocusForLotteryReset from '../../../components/LotteryScratch/AppFocusForLotteryReset';
import {ServerConnection} from '../../../Validator/Inventory/ServerValidation';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';

type BarcodeScreenRouteProp = RouteProp<any, 'IntialSetup'>;
const LOT_InitialSetup: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [lottery, setLottery] = useState<Inventory[]>([]);
  const [selectedLottery, setSelectedLottery] = useState<Inventory>();
  const [status, setStatus] = useState<boolean>(false);
  const [shiftDetails, setShiftDetails] = useState<Shift_Details>();
  const [isShiftClosed, setIsShiftClosed] = useState<boolean>(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [reason, setReason] = useState<Reason_Codes[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedBook, setSelectedBook] = useState<string>('');
  const [currentItemIndex, setCurrentItemIndex] = useState<number>(0);
  const [completedItems, setCompletedItems] = useState<Set<string>>(new Set());
  const inputRefs = useRef<TextInput[]>([]);
  const [gameDetails, setGameDetails] = useState<Game_Details[]>([]);
  const [badItems, setBadItems] = useState<Set<string>>(new Set());
  const [goodItems, setGoodItems] = useState<Set<string>>(new Set());
  const [modal, setModal] = useState<boolean>(false);
  const [resetStock, setResetStock] = useState<boolean>(false);
  const [isSkipReset, setIsSkipReset] = useState<boolean>(false);
  const [isClosed, setIsClosed] = useState<boolean>(false);
  const [missingBySkip, setIsMissingBySkip] = useState<number>(0);
  const [selectReason, setSelectedReason] = useState<string>('');
  const [currentBarcode, setCurrentBarcode] = useState<string>('');
  const [resetStockUpdate, setResetStockUpdate] = useState<number>(0);
  const [camera, setCamera] = useState<boolean>(false);
  const [isAsc, setIsAsc] = useState<boolean>(false);
  const [bookQty, setBookQty] = useState<number>(
    route.params?.ItemData?.In_Stock,
  );
  const [resetItemData, setResetItemData] = useState({
    itemNum: '',
    barcode: '',
  });

  useEffect(() => {
    const initializeCompletedItems = async () => {
      const FindNextIndex = await AsyncStorage.getItem('SETCURRENTINDEX');

      if (FindNextIndex) {
        const parsedIndex = JSON.parse(FindNextIndex);
        setCurrentItemIndex(parsedIndex);
        // Remove automatic focusing - user will manually select cards
      } else {
        // Don't automatically set first item as active - user will manually select
        setCurrentItemIndex(-1); // Set to -1 to indicate no card is selected
      }
    };

    // Only initialize when lottery data is available and has items
    if (lottery && lottery.length > 0) {
      initializeCompletedItems();
    }
  }, [lottery.length]);

  // Remove automatic focusing effect - cards will only be focused when manually selected

  useEffect(() => {}, [badItems, goodItems]);
  useFocusEffect(
    useCallback(() => {
      checkServerConnection();
      loadSerialNumberOrder();
      //hendleCurrentIndex();
    }, []),
  );

  const loadSerialNumberOrder = async () => {
    try {
      const order = await getSerialNumberOrder();
      setIsAsc(order === 'ascending');
    } catch (error) {
      console.error('Error loading serial number order:', error);
      setIsAsc(true); // Default to ascending
    }
  };

  // Function to handle manual card selection
  const handleCardSelection = async (index: number) => {
    setCurrentItemIndex(index);
    await AsyncStorage.setItem('SETCURRENTINDEX', index.toString());

    // Focus the input of the selected card
    setTimeout(() => {
      if (inputRefs.current && inputRefs.current[index]) {
        inputRefs.current[index]?.focus();
      }
    }, 100);
  };

  const checkServerConnection = async () => {
    const inventoryPort = await getInventoryPort();
    const serverStatus = await ServerConnection(inventoryPort);
    if (!serverStatus) {
      showAlertOK(
        'Database Connection Falied, Please Check Your Database Configuration',
        'Connection Failed',
        'OK',
      );
    } else {
      getShiftStatus();
      getInitDept();
      GetDepartmentsbyTickets();
      setModal(false);
      setResetStock(false);
    }
  };
  const handleSerialChange = async (serial: string, Item: Inventory) => {
    if (status) {
      //endshift
      let endGameSerial = serial.split('-');
      const getShiftID = await AsyncStorage.getItem('Shift_ID');
      const gameDetails = await GetItemsParamsNoFilter(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        setGameDetails,
        {Shift_ID: getShiftID, Location: Item.Location},
      );

      const shiftBookDetails = await GetItemsParamsNoFilter(
        (await getLotteryPort()).toString(),
        '/GetShiftBookTickets/:ItemNum/:Location',
        setGameDetails,
        {ItemNum: Item.ItemNum, Location: Item.Location},
      );

      let soltConfiramtion = 0;

      let soltVerification = isAsc
        ? Number(endGameSerial[2]) - Number(shiftBookDetails[0]?.Open_Serial)
        : Number(shiftBookDetails[0]?.Open_Serial) - Number(endGameSerial[2]);
      soltConfiramtion =
        Number(shiftBookDetails[0]?.Book_Tickets) - Number(soltVerification);

      if (soltConfiramtion === Item.In_Stock) {
        const existingData = await AsyncStorage.getItem('SetSerialNumber');
        const existingArray = existingData ? JSON.parse(existingData) : [];

        const indexToUpdate = existingArray.some(
          item => item.ItemNum === Item?.ItemNum,
        );
        if (indexToUpdate) {
          const indexToUpdateoldValue = existingArray.findIndex(
            item => item.ItemNum === Item?.ItemNum,
          );

          existingArray[indexToUpdateoldValue].EndSerial = serial;
        } else {
          existingArray.push({ItemNum: Item.ItemNum, EndSerial: serial});
        }

        await AsyncStorage.setItem(
          'SetSerialNumber',
          JSON.stringify(existingArray),
        );

        //await AsyncStorage.setItem(Item.ItemName, serial);
        const newGoodItems = new Set([...goodItems, Item.ItemName]);
        const newBadItems = new Set(badItems);
        newBadItems.delete(Item.ItemName);

        setGoodItems(newGoodItems);
        setBadItems(newBadItems);

        await AsyncStorage.setItem(
          'GoodItems',
          JSON.stringify(Array.from(newGoodItems)),
        );
        await AsyncStorage.setItem(
          'BadItems',
          JSON.stringify(Array.from(newBadItems)),
        );
        // Remove automatic focus shifting - user will manually select next card
        // Just clear the current selection so user can manually select next card
        setCurrentItemIndex(-1);
        await AsyncStorage.removeItem('SETCURRENTINDEX');
      } else {
        const existingData = await AsyncStorage.getItem('SetSerialNumber');
        const existingArray = existingData ? JSON.parse(existingData) : [];

        const indexToUpdate = existingArray.some(
          item => item.ItemNum === Item?.ItemNum,
        );
        if (indexToUpdate) {
          const indexToUpdateoldValue = existingArray.findIndex(
            item => item.ItemNum === Item?.ItemNum,
          );

          existingArray[indexToUpdateoldValue].EndSerial = serial;
        } else {
          existingArray.push({ItemNum: Item.ItemNum, EndSerial: serial});
        }

        await AsyncStorage.setItem(
          'SetSerialNumber',
          JSON.stringify(existingArray),
        );

        //await AsyncStorage.setItem(Item.ItemName, serial);

        const newBadItems = new Set([...badItems, Item.ItemName]);
        const newGoodItems = new Set(goodItems);
        newGoodItems.delete(Item.ItemName);

        setBadItems(newBadItems);
        setGoodItems(newGoodItems);

        await AsyncStorage.setItem(
          'BadItems',
          JSON.stringify(Array.from(newBadItems)),
        );
        await AsyncStorage.setItem(
          'GoodItems',
          JSON.stringify(Array.from(newGoodItems)),
        );
        // Remove automatic focus shifting - user will manually select next card
        // Just clear the current selection so user can manually select next card
        setCurrentItemIndex(-1);
        await AsyncStorage.removeItem('SETCURRENTINDEX');
      }
    } else {
      const Shift_ID = await GetCommonLatestID(
        (await getLotteryPort()).toString(),
        '/GetShiftID',
      );
      if (Number(Shift_ID) - 1 === 0) {
        if (serial.trim()) {
          // Save serial in AsyncStorage
          await AsyncStorage.setItem(Item.ItemName, serial);
          setCompletedItems(prev => new Set(prev).add(Item.ItemName));

          // Remove automatic focus shifting - user will manually select next card
          // Just clear the current selection so user can manually select next card
          setCurrentItemIndex(-1);
          await AsyncStorage.removeItem('SETCURRENTINDEX');
        }
      } else {
        const GetShiftID = Number(Shift_ID) - 1;
        const getExistGameDetails = await GetItemsParamsNoFilterNoReturn(
          (await getLotteryPort()).toString(),
          '/GetGameDetails/:Shift_ID/:Location',
          {Shift_ID: GetShiftID, Location: Item.Location},
        );

        let endGameSerial = serial.split('-');
        const lastSerial = endGameSerial[2];

        const getActivBookDetails = await GetItemsParamsNoFilterNoReturn(
          (await getLotteryPort()).toString(),
          '/GetShiftBookTickets/:ItemNum/:Location',
          {ItemNum: Item.ItemNum, Location: Item.Location},
        );

        const activeOpen = isAsc
          ? Number(lastSerial) - Number(getActivBookDetails[0]?.Open_Serial)
          : Number(getActivBookDetails[0]?.Open_Serial) - Number(lastSerial); // 010 - 005 = 5
        const activeBookstarted =
          Number(getActivBookDetails[0]?.Book_Tickets) - Number(activeOpen);

        if (serial.trim()) {
          if (
            Number(getExistGameDetails[0]?.Stock_Level_Close) > 0 &&
            getExistGameDetails[0]?.Shift_Close_Serial === 'N/A'
          ) {
            // 15 - 5
            if (Number(activeBookstarted) === Number(Item.In_Stock)) {
              // Save serial in AsyncStorage
              await AsyncStorage.setItem(Item.ItemName, serial);
              setCompletedItems(prev => new Set(prev).add(Item.ItemName));

              // Remove automatic focus shifting - user will manually select next card
              // Just clear the current selection so user can manually select next card
              setCurrentItemIndex(-1);
              await AsyncStorage.removeItem('SETCURRENTINDEX');
            } else {
              setResetStock(true);
              setSelectedBook(Item.ItemName);
              setSelectedLottery(Item);
              // Pre-set which item will be reset
              setIsSkipReset(true);
              setIsMissingBySkip(
                Number(Item?.In_Stock) - Number(activeBookstarted),
              );
              setResetItemData({itemNum: Item.ItemNum, barcode: ''});
              setCurrentBarcode(serial);
            }
          } else {
            if (Number(activeBookstarted) === Number(Item.In_Stock)) {
              // Save serial in AsyncStorage
              await AsyncStorage.setItem(Item.ItemName, serial);
              setCompletedItems(prev => new Set(prev).add(Item.ItemName));

              // Remove automatic focus shifting - user will manually select next card
              // Just clear the current selection so user can manually select next card
              setCurrentItemIndex(-1);
              await AsyncStorage.removeItem('SETCURRENTINDEX');
            } else {
              setResetStock(true);
              setSelectedBook(Item.ItemName);
              setSelectedLottery(Item);
              // Pre-set which item will be reset
              setIsSkipReset(true);
              setIsMissingBySkip(
                Number(Item?.In_Stock) - Number(activeBookstarted),
              );
              setResetItemData({itemNum: Item.ItemNum, barcode: ''});
              setCurrentBarcode(serial);
            }
          }
        }
      }
    }
  };
  const getInitDept = async () => {
    const data = await GetItemsParamsNoFilter(
      (await getLotteryPort()).toString(),
      '/GetLotteryReasonCode/:Reason_Type',
      setReason,
      {Reason_Type: 55},
      false,
    );
  };
  const getShiftStatus = async () => {
    await AsyncStorage.getItem('IsShiftStarted')
      .then(value => {
        const isShiftStarted = JSON.parse(value);
        if (isShiftStarted) {
          setStatus(isShiftStarted);
        } else {
          setStatus(false);
        }
      })
      .catch(error => console.error('Failed to fetch data', error));
  };

  const GetDepartmentsbyTickets = async () => {
    const lotteryDepartment = await AsyncStorage.getItem('LOTTERY_DEP_ID');
    if (lotteryDepartment) {
      const data = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/inventorybydepidwithLocation/:Dept_ID',
        setLottery,
        {Dept_ID: lotteryDepartment},
      );

      setSelectedDepartment(lotteryDepartment);
    } else {
      showAlert(
        'Lottery Department Not Found Would You Like to Select the Lottery Department?',
        'Lottery Not Found!',
      )
        .then(async result => {
          if (result) {
            navigation.navigate('LotterySetup');
          }
        })
        .catch(error => {
          console.error('Error showing alert', error);
        });
    }
  };

  const startOrEndShift = async () => {
    // await removeZeroSerials();
    // await AsyncStorage.removeItem('Shift_ID');
    // await AsyncStorage.removeItem('IsShiftStarted');
    // await AsyncStorage.removeItem('SetSerialNumber');
    // await AsyncStorage.removeItem('GetSerials');
    // await AsyncStorage.removeItem('BadItems');
    // await AsyncStorage.removeItem('GoodItems');
    // await AsyncStorage.removeItem('SETCURRENTINDEX');
    try {
      if (isShiftClosed) {
        if (status) {
          Alert.alert('Shift Already Closed');
        } else {
          Alert.alert('Shift Already Started');
        }
      } else {
        if (status) {
          const data = await AsyncStorage.getItem('SetSerialNumber');
          if (data) {
            const parsedData = JSON.parse(data);
            if (lottery.length === parsedData.length) {
              const getShiftID = await AsyncStorage.getItem('Shift_ID');
              const getEndShiftSerials = await AsyncStorage.getItem(
                'SetSerialNumber',
              );
              const parseData = getEndShiftSerials
                ? JSON.parse(getEndShiftSerials)
                : [];
              let sendGameDetails: Game_Details[] = [];
              let missingTicketsReport: Game_Details[] = [];
              // Process each lottery item and its corresponding shift data
              for (const exists of lottery) {
                for (const shift of parseData) {
                  if (exists.ItemNum === shift.ItemNum) {
                    const gameDetailsArray =
                      (await GetItemsParamsNoFilterNoReturn(
                        (await getLotteryPort()).toString(),
                        '/GetGameDetails/:Shift_ID/:Location',
                        {Shift_ID: getShiftID, Location: exists.Location},
                      )) as Game_Details[];
                    const shiftBookDetailsArray =
                      (await GetItemsParamsNoFilterNoReturn(
                        (await getLotteryPort()).toString(),
                        '/GetShiftBookTickets/:ItemNum/:Location',
                        {ItemNum: exists.ItemNum, Location: exists.Location},
                      )) as Activate_Book[];
                    let endGameSerial = shift.EndSerial.split('-');
                    let openseialConfirmASC =
                      Number(endGameSerial[2]) -
                      Number(shiftBookDetailsArray[0]?.Open_Serial);
                    let openseialConfirmDSC =
                      Number(shiftBookDetailsArray[0]?.Open_Serial) -
                      Number(endGameSerial[2]);
                    let soltVerification =
                      Number(shiftBookDetailsArray[0]?.Book_Tickets) -
                      Number(isAsc ? openseialConfirmASC : openseialConfirmDSC);
                    let missingTickets =
                      Number(exists?.In_Stock) - Number(soltVerification); // 12 - 10 = 2
                    if (
                      !gameDetailsArray ||
                      (Array.isArray(gameDetailsArray) &&
                        gameDetailsArray.length === 0)
                    ) {
                      if (
                        !shiftBookDetailsArray ||
                        (Array.isArray(shiftBookDetailsArray) &&
                          shiftBookDetailsArray.length === 0)
                      ) {
                        const shiftGameData: Game_Details = {
                          Game_ID:
                            endGameSerial[0] === null ||
                            !endGameSerial[0] ||
                            endGameSerial[0] === undefined
                              ? 'N/A'
                              : endGameSerial[0],
                          Open_Book: 'N/A',
                          Close_Book:
                            endGameSerial[1] === null ||
                            !endGameSerial[1] ||
                            endGameSerial[1] === undefined
                              ? 'N/A'
                              : endGameSerial[1],
                          Stock_Level_Open: null,
                          Stock_Level_Close: exists.In_Stock,
                          Shift_Open_Serial: 'N/A',
                          Shift_Close_Serial:
                            endGameSerial[2] === null ||
                            !endGameSerial[2] ||
                            endGameSerial[2] === undefined
                              ? 'N/A'
                              : endGameSerial[2],
                          Missing_Ticket: 0,
                          Location: exists.Location,
                          Shift_ID: getShiftID?.toString() || '',
                          Date: getFormateDate(Date()),
                          Shift_End_Skip: null,
                          Shift_Start_Skip: null,
                        } as Game_Details;
                        const applyDefault =
                          applyDefaultsGameDetails(shiftGameData);
                        const result1 = await createData<Game_Details>({
                          baseURL: (await getLotteryPort()).toString(),
                          data: applyDefault,
                          endpoint: '/creategamedetail',
                        });
                        if (result1) {
                          sendGameDetails.push(shiftGameData);
                          await AsyncStorage.removeItem(exists?.ItemName);
                        }
                      } else {
                        if (soltVerification === exists.In_Stock) {
                          const shiftGameData: Game_Details = {
                            Game_ID: endGameSerial[0] || 'N/A',
                            Open_Book: 'N/A',
                            Close_Book:
                              endGameSerial[1] === null ||
                              !endGameSerial[1] ||
                              endGameSerial[1] === undefined
                                ? 'N/A'
                                : endGameSerial[1],
                            Stock_Level_Open: null,
                            Stock_Level_Close: exists.In_Stock,
                            Shift_Open_Serial: 'N/A',
                            Shift_Close_Serial:
                              endGameSerial[2] === null ||
                              !endGameSerial[2] ||
                              endGameSerial[2] === undefined
                                ? 'N/A'
                                : endGameSerial[2],
                            Missing_Ticket: 0,
                            Location: exists.Location,
                            Shift_ID: getShiftID?.toString() || '',
                            Date: getFormateDate(Date()),
                            Shift_End_Skip: null,
                            Shift_Start_Skip: null,
                          } as Game_Details;
                          const applyDefault =
                            applyDefaultsGameDetails(shiftGameData);
                          const result = await createData<Game_Details>({
                            baseURL: (await getLotteryPort()).toString(),
                            data: applyDefault,
                            endpoint: '/creategamedetail',
                          });
                          if (result) {
                            sendGameDetails.push(shiftGameData);
                            await AsyncStorage.removeItem(exists?.ItemName);
                          }
                        } else {
                          const shiftGameData: Game_Details = {
                            Game_ID: endGameSerial[0] || 'N/A',
                            Open_Book: 'N/A',
                            Close_Book:
                              endGameSerial[1] === null ||
                              !endGameSerial[1] ||
                              endGameSerial[1] === undefined
                                ? 'N/A'
                                : endGameSerial[1],
                            Stock_Level_Open: null,
                            Stock_Level_Close: exists.In_Stock,
                            Shift_Open_Serial: 'N/A',
                            Shift_Close_Serial:
                              endGameSerial[2] === null ||
                              !endGameSerial[2] ||
                              endGameSerial[2] === undefined
                                ? 'N/A'
                                : endGameSerial[2],
                            Missing_Ticket: missingTickets,
                            Location: exists.Location,
                            Shift_ID: getShiftID?.toString() || '',
                            Date: getFormateDate(Date()),
                            Reset: 'Y',
                            Shift_End_Skip: null,
                            Shift_Start_Skip: null,
                          } as Game_Details;
                          const applyDefault =
                            applyDefaultsGameDetails(shiftGameData);
                          const result1 = await createData<Game_Details>({
                            baseURL: (await getLotteryPort()).toString(),
                            data: applyDefault,
                            endpoint: '/creategamedetail',
                          });
                          if (result1) {
                            sendGameDetails.push(shiftGameData);
                            // Add to missing tickets report for email
                            missingTicketsReport.push(shiftGameData);
                            await AsyncStorage.removeItem(exists?.ItemName);
                          }
                        }
                      }
                    } else {
                      if (shift.EndSerial === 'N/A') {
                        const skipReason =
                          shift.EndSkipReason || selectReason || null;
                        const shiftGameData: Game_Details = {
                          Game_ID:
                            Number(exists.In_Stock) > 0
                              ? shiftBookDetailsArray[0]?.Game_ID || 'N/A'
                              : 'N/A',
                          Open_Book: gameDetailsArray[0]?.Open_Book || 'N/A',
                          Close_Book: 'N/A',
                          Stock_Level_Open:
                            gameDetailsArray[0]?.Stock_Level_Open || null,
                          Stock_Level_Close: exists.In_Stock,
                          Shift_Open_Serial:
                            gameDetailsArray[0]?.Shift_Open_Serial || 'N/A',
                          Shift_Close_Serial: 'N/A',
                          Missing_Ticket: 0,
                          Location: exists.Location,
                          Shift_ID: getShiftID?.toString() || '',
                          Date: getFormateDate(Date()),
                          Shift_End_Skip: skipReason, // Set the end shift skip reason
                          Shift_Start_Skip:
                            gameDetailsArray[0]?.Shift_Start_Skip || null,
                        } as Game_Details;
                        const applyDefault =
                          applyDefaultsGameDetails(shiftGameData);
                        const result = await updateData<Game_Details>({
                          baseURL: (await getLotteryPort()).toString(),
                          data: applyDefault,
                          endpoint: '/updatelotgamesdetails',
                        });
                        if (result) {
                          sendGameDetails.push(shiftGameData);
                          await AsyncStorage.removeItem(exists?.ItemName);
                        }
                      } else {
                        if (soltVerification === exists.In_Stock) {
                          const shiftGameData: Game_Details = {
                            Game_ID: endGameSerial[0] || 'N/A',
                            Open_Book: gameDetailsArray[0]?.Open_Book || 'N/A',
                            Close_Book:
                              endGameSerial[1] === null ||
                              !endGameSerial[1] ||
                              endGameSerial[1] === undefined
                                ? null
                                : endGameSerial[1],
                            Stock_Level_Open:
                              gameDetailsArray[0]?.Stock_Level_Open || null,
                            Stock_Level_Close: exists.In_Stock,
                            Shift_Open_Serial:
                              gameDetailsArray[0]?.Shift_Open_Serial || 'N/A',
                            Shift_Close_Serial:
                              endGameSerial[2] === null ||
                              !endGameSerial[2] ||
                              endGameSerial[2] === undefined
                                ? 'N/A'
                                : endGameSerial[2],
                            Missing_Ticket: 0,
                            Location: exists.Location,
                            Shift_ID: getShiftID?.toString() || '',
                            Date: getFormateDate(Date()),
                            Shift_End_Skip: null,
                            Shift_Start_Skip:
                              gameDetailsArray[0]?.Shift_Start_Skip || null,
                          } as Game_Details;
                          const applyDefault =
                            applyDefaultsGameDetails(shiftGameData);
                          const result = await updateData<Game_Details>({
                            baseURL: (await getLotteryPort()).toString(),
                            data: applyDefault,
                            endpoint: '/updatelotgamesdetails',
                          });
                          if (result) {
                            sendGameDetails.push(shiftGameData);
                            await AsyncStorage.removeItem(exists?.ItemName);
                          }
                        } else {
                          const shiftGameData: Game_Details = {
                            Game_ID: endGameSerial[0] || 'N/A',
                            Open_Book: gameDetailsArray[0]?.Open_Book || 'N/A',
                            Close_Book:
                              endGameSerial[1] === null ||
                              !endGameSerial[1] ||
                              endGameSerial[1] === undefined
                                ? null
                                : endGameSerial[1],
                            Stock_Level_Open:
                              gameDetailsArray[0]?.Stock_Level_Open || null,
                            Stock_Level_Close: exists.In_Stock,
                            Shift_Open_Serial:
                              gameDetailsArray[0]?.Shift_Open_Serial || 'N/A',
                            Shift_Close_Serial:
                              endGameSerial[2] === null ||
                              !endGameSerial[2] ||
                              endGameSerial[2] === undefined
                                ? 'N/A'
                                : endGameSerial[2],
                            Missing_Ticket: missingTickets,
                            Location: exists.Location,
                            Shift_ID: getShiftID?.toString() || '',
                            Date: getFormateDate(Date()),
                            Reset: 'Y',
                            Shift_End_Skip: null,
                            Shift_Start_Skip:
                              gameDetailsArray[0]?.Shift_Start_Skip || null,
                          } as Game_Details;
                          const applyDefault =
                            applyDefaultsGameDetails(shiftGameData);
                          const result1 = await updateData<Game_Details>({
                            baseURL: (await getLotteryPort()).toString(),
                            data: applyDefault,
                            endpoint: '/updatelotgamesdetails',
                          });
                          if (result1) {
                            sendGameDetails.push(shiftGameData);
                            // Add to missing tickets report for email
                            missingTicketsReport.push(shiftGameData);
                            await AsyncStorage.removeItem(exists?.ItemName);
                          }
                        }
                      }
                    }
                    break; // Exit inner loop once matching item is found
                  }
                }
              }
              const shiftDetailsResult = (await GetItemsParamsNoFilterNoReturn(
                (await getLotteryPort()).toString(),
                '/GetShiftDetails/:Shift_ID',
                {Shift_ID: getShiftID},
              )) as Shift_Details[];
              const shiftData: Partial<Shift_Details> = {
                Shift_ID: getShiftID?.toString(),
                Shift_Cashier_ID: shiftDetailsResult[0]?.Shift_Cashier_ID,
                Shift_Start_Time: shiftDetailsResult[0]?.Shift_Start_Time,
                Shift_End_Time: getFormateDate(Date()),
                Shift_Status: false,
              };
              //create map
              const applyDefault = applyDefaultsShiftDetails(shiftData);
              const result = await updateData<Shift_Details>({
                baseURL: (await getLotteryPort()).toString(),
                data: applyDefault,
                endpoint: '/updateshiftdetails',
              });
              if (result) {
                // Send missing tickets report if there are any missing tickets
                if (missingTicketsReport.length > 0) {
                  shiftMailSend(missingTicketsReport, applyDefault);
                }
                await removeZeroSerials();
                await AsyncStorage.removeItem('Shift_ID');
                await AsyncStorage.removeItem('IsShiftStarted');
                await AsyncStorage.removeItem('SetSerialNumber');
                await AsyncStorage.removeItem('GetSerials');
                await AsyncStorage.removeItem('BadItems');
                await AsyncStorage.removeItem('GoodItems');
                await AsyncStorage.removeItem('SETCURRENTINDEX');
                // await AsyncStorage.removeItem('000');
                setIsShiftClosed(true);
                Alert.alert('Shift Closed');
                navigation.goBack();
              }
            } else {
              Alert.alert('Please Complete the Entry');
            }
          } else {
            Alert.alert('Please Complete the Entry');
          }
        } else {
          const Shift_ID = await GetCommonLatestID(
            (await getLotteryPort()).toString(),
            '/GetShiftID',
          );
          const data = await AsyncStorage.getAllKeys();
          const matches = lottery.filter(item => data.includes(item.ItemName));
          try {
            if (lottery.length === Number(matches.length)) {
              const storeId = await AsyncStorage.getItem('STOREID');
              const ValidStore = storeId === null ? '1001' : storeId;
              const CashierID = await AsyncStorage.getItem('SWIPEID');
              const ValideCashier = CashierID === null ? '100101' : CashierID;
              const shiftData: Partial<Shift_Details> = {
                Shift_ID: Shift_ID?.toString(),
                Shift_Cashier_ID: ValideCashier,
                Shift_Start_Time: getFormateDate(Date()),
                Shift_End_Time: null,
                Shift_Status: true,
              };
              const applyDefault = applyDefaultsShiftDetails(shiftData);
              createData<Shift_Details>({
                baseURL: (await getLotteryPort()).toString(),
                data: applyDefault,
                endpoint: '/createshiftdetail',
              });
              // Get skip reasons if any
              const skipData = await AsyncStorage.getItem('ShiftSkipReasons');
              const skipReasons = skipData ? JSON.parse(skipData) : [];
              lottery.map(async (game: Inventory) => {
                const savedData = await AsyncStorage.getItem(game.ItemName);
                const getActivBookDetails =
                  await GetItemsParamsNoFilterNoReturn(
                    (await getLotteryPort()).toString(),
                    '/GetShiftBookTickets/:ItemNum/:Location',
                    {ItemNum: game.ItemNum, Location: game.Location},
                  );
                if (savedData) {
                  const getBarcodeLatest = await GetItemsParamsNoFilterNoReturn(
                    (await getInventoryPort()).toString(),
                    '/inventory/:ItemNum',
                    {ItemNum: game.ItemNum},
                  );
                  if (savedData === 'N/A') {
                    // This is a skipped item, find the skip reason
                    const skipReason =
                      skipReasons.find(item => item.ItemNum === game.ItemNum)
                        ?.SkipReason ||
                      selectReason ||
                      null;
                    const shiftGameData: Partial<Game_Details> = {
                      Game_ID:
                        Number(game.In_Stock) > 0
                          ? getActivBookDetails[0]?.Game_ID
                          : 'N/A',
                      Open_Book: 'N/A',
                      Close_Book: null,
                      Stock_Level_Open: Number(getBarcodeLatest[0]?.In_Stock),
                      Stock_Level_Close: null,
                      Shift_Open_Serial: 'N/A',
                      Shift_Close_Serial: null,
                      Missing_Ticket: null,
                      Location: game.Location,
                      Shift_ID: Shift_ID?.toString(),
                      Date: getFormateDate(Date()),
                      Shift_End_Skip: null,
                      Shift_Start_Skip: skipReason, // Set the start shift skip reason
                    };
                    const applyDefault =
                      applyDefaultsGameDetails(shiftGameData);
                    const GameCreated = await createData<Game_Details>({
                      baseURL: (await getLotteryPort()).toString(),
                      data: applyDefault,
                      endpoint: '/creategamedetail',
                    });
                    if (GameCreated) {
                      await AsyncStorage.removeItem(game.ItemName);
                    }
                  } else {
                    // Normal non-skipped item processing
                    let parts = savedData.split('-');
                    let GameID = parts[0];
                    let BookNo = parts[1];
                    let Serial = parts[2];
                    const shiftGameData: Partial<Game_Details> = {
                      Game_ID: GameID,
                      Open_Book:
                        BookNo === null ||
                        !BookNo ||
                        BookNo === undefined ||
                        BookNo === ''
                          ? 'N/A'
                          : BookNo,
                      Close_Book: null,
                      Stock_Level_Open: Number(getBarcodeLatest[0]?.In_Stock),
                      Stock_Level_Close: null,
                      Shift_Open_Serial:
                        Serial === null ||
                        !Serial ||
                        Serial === undefined ||
                        Serial === ''
                          ? 'N/A'
                          : Serial,
                      Shift_Close_Serial: null,
                      Missing_Ticket: null,
                      Location: game.Location,
                      Shift_ID: Shift_ID?.toString(),
                      Date: getFormateDate(Date()),
                      Shift_End_Skip: null,
                      Shift_Start_Skip: null,
                    };
                    const applyDefault =
                      applyDefaultsGameDetails(shiftGameData);
                    const GameCreated = await createData<Game_Details>({
                      baseURL: (await getLotteryPort()).toString(),
                      data: applyDefault,
                      endpoint: '/creategamedetail',
                    });
                    if (GameCreated) {
                      await AsyncStorage.removeItem(game.ItemName);
                    }
                  }
                  AsyncStorage.setItem(
                    'IsShiftStarted',
                    JSON.stringify(true),
                  ).catch(error => console.error('Failed to save data', error));
                  AsyncStorage.setItem(
                    'Shift_ID',
                    JSON.stringify(Shift_ID),
                  ).catch(error => console.error('Failed to save data', error));
                  setIsShiftClosed(true);
                }
              });
              // Clear skip reasons after processing
              await AsyncStorage.removeItem('ShiftSkipReasons');
              await AsyncStorage.removeItem('SETCURRENTINDEX');
              Alert.alert('Shift Started!');
              navigation.goBack();
            } else {
              Alert.alert('Please Complete the Entry');
              setIsShiftClosed(false);
            }
          } catch (error) {
            console.log(error);
          }
        }
        await AsyncStorage.removeItem('SETCURRENTINDEX');
      }
    } catch (error) {
      console.log('Error', error);
    }
  };
  const removeZeroSerials = async () => {
    for (const game of lottery) {
      const value = await AsyncStorage.getItem(game.ItemName);
      if (value === '000') {
        await AsyncStorage.removeItem(game.ItemName);
      }
    }
  };

  const LastShiftMissing = async (item: Inventory) => {
    if (item.Location) {
      const Shift_ID = await GetCommonLatestID(
        (await getLotteryPort()).toString(),
        '/GetShiftID',
      );
      const getMissingTickets = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        {Shift_ID: Number(Shift_ID) - 1, Location: item.Location},
      );

      return getMissingTickets;
    }
  };

  const [missingTicketsMap, setMissingTicketsMap] = useState<{
    [key: string]: any;
  }>({});
  const [barcodeMap, setBarcodeMap] = useState<{[key: string]: any}>({});

  useEffect(() => {
    const fetchData = async () => {
      const resultTickets: {[key: string]: any} = {};
      const resultBarcodes: {[key: string]: any} = {};

      for (const item of lottery) {
        if (!item) continue;
        try {
          const missing = await LastShiftMissing(item);
          resultTickets[item.ItemNum] = missing;
          const barcode = await GetItemsParamsNoFilterNoReturn(
            (await getInventoryPort()).toString(),
            '/inventory/:ItemNum',
            {ItemNum: item.ItemNum},
          );
          resultBarcodes[item.ItemNum] = barcode;
        } catch (err) {
          console.log('Error loading item data', err);
        }
      }

      setMissingTicketsMap(resultTickets);
      setBarcodeMap(resultBarcodes);
    };

    if (lottery.length > 0) fetchData();
  }, [lottery]);

  const refreshItemData = async (itemNum: string) => {
    try {
      // Refresh barcode data
      const barcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/inventory/:ItemNum',
        {ItemNum: itemNum},
      );

      // Refresh missing tickets data
      const Shift_ID = await GetCommonLatestID(
        (await getLotteryPort()).toString(),
        '/GetShiftID',
      );
      const missing = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetGameDetails/:Shift_ID/:Location',
        {Shift_ID: Number(Shift_ID) - 1, Location: selectedLottery?.Location},
      );

      // Update the maps
      setBarcodeMap(prev => ({
        ...prev,
        [itemNum]: barcode,
      }));

      setMissingTicketsMap(prev => ({
        ...prev,
        [itemNum]: missing,
      }));
    } catch (error) {
      console.log('Error refreshing item data:', error);
    }
  };
  const renderItem = ({
    item,
    index,
  }: {
    item: Inventory | undefined;
    index: number;
  }) => {
    if (!item) return null;

    const getMissingTickets = missingTicketsMap[item.ItemNum];
    const getBarcode = barcodeMap[item.ItemNum];

    return (
      <TicketsDetails
        key={
          isClosed
            ? `${item.ItemNum}-${ticketDetailsKey}-${
                index === currentItemIndex ? 'active' : 'inactive'
              }`
            : item.ItemNum
        }
        Item={item}
        ResetScanned={
          index === currentItemIndex && !resetStock
            ? ''
            : item.ItemNum === resetItemData.itemNum
            ? resetItemData.barcode
            : item.ItemNum === currentItemIndex
            ? currentBarcode
            : ''
        }
        OnSerialChange={handleSerialChange}
        Stock={getBarcode?.[0]?.In_Stock}
        Cost={Number(item?.Price)}
        IsActive={index === currentItemIndex}
        IsCompleted={completedItems.has(item.ItemName)}
        isBadItem={badItems.has(item.ItemName)}
        isGoodItem={goodItems.has(item.ItemName)}
        status={status}
        IsLastMissings={
          getMissingTickets?.[0]?.Missing_Ticket > 0 &&
          getMissingTickets?.[0]?.Reset === 'Y' // Will only show if not reset
        }
        MissingValue={
          getMissingTickets?.[0]?.Missing_Ticket > 0 &&
          getMissingTickets?.[0]?.Reset === 'Y' &&
          getMissingTickets?.[0]?.Missing_Ticket
        }
        inputRef={el => {
          if (el) {
            inputRefs.current[index] = el;
          }
        }}
        onFocus={() => handleCardSelection(index)}
        Skip={() => {
          setModal(true);
          setSelectedBook(item.ItemName);
          setSelectedLottery(item);
        }}
        ResetStock={() => {
          setResetStock(true);
          setSelectedBook(item.ItemName);
          setSelectedLottery(item);
          setResetItemData({itemNum: item.ItemNum, barcode: ''});
        }}
      />
    );
  };

  const shiftMailSend = async (
    ShiftGamesDetails: Game_Details[],
    shiftDetails: Shift_Details,
  ) => {
    const reportMail: Send_Email = {
      ShiftGames: ShiftGamesDetails,
      ShiftDetails: shiftDetails,
    };

    try {
      const lotteryPort = await getLotteryPort();

      console.log('SHIFT GAME', ShiftGamesDetails);
      console.log('SHIFT DATA', shiftDetails);

      const result = await createItem(
        lotteryPort.toString(),
        '/send-mail',
        reportMail,
      );

      if (result) {
        console.log('✅ Email sent successfully');
        //Alert.alert('Success', 'Email report sent successfully');
      } else {
        console.log('❌ Email send failed - no result returned');
        //Alert.alert('Error', 'Failed to send email report');
      }
    } catch (error) {
      console.error('❌ Error sending email:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', 'Failed to send email report: ' + errorMessage);
    }
  };
  const departmentOptions = departments.map(dept => ({
    label: dept.Description,
    value: dept.Dept_ID,
  }));
  const reasonOptions = reason.map(dept => ({
    label: dept.Reason_Code,
    value: dept.Reason_Code,
  }));

  const SkipCounting = async () => {
    if (selectReason.trim() === '') {
      Alert.alert('Reason Required');
    } else {
      if (status) {
        // End shift scenario - store skip reason in AsyncStorage with the EndSerial
        const existingData = await AsyncStorage.getItem('SetSerialNumber');
        const existingArray = existingData ? JSON.parse(existingData) : [];

        const indexToUpdate = existingArray.some(
          item => item.ItemNum === selectedLottery?.ItemNum,
        );
        if (indexToUpdate) {
          const indexToUpdateoldValue = existingArray.findIndex(
            item => item.ItemNum === selectedLottery?.ItemNum,
          );

          existingArray[indexToUpdateoldValue].EndSerial = 'N/A';
          existingArray[indexToUpdateoldValue].EndSkipReason = selectReason; // Store the skip reason
        } else {
          existingArray.push({
            ItemNum: selectedLottery?.ItemNum,
            EndSerial: 'N/A',
            EndSkipReason: selectReason, // Store the skip reason
          });
        }

        await AsyncStorage.setItem(
          'SetSerialNumber',
          JSON.stringify(existingArray),
        );

        await AsyncStorage.setItem(selectedLottery?.ItemName, 'N/A');
        setCompletedItems(prev => new Set(prev).add(selectedLottery?.ItemName));

        // Remove automatic focus shifting - user will manually select next card
        // Just clear the current selection so user can manually select next card
        setCurrentItemIndex(-1);
        await AsyncStorage.removeItem('SETCURRENTINDEX');
        setModal(false);
      } else {
        // Start shift scenario - store skip reason for later use
        const skipData = await AsyncStorage.getItem('ShiftSkipReasons');
        const skipReasons = skipData ? JSON.parse(skipData) : [];

        skipReasons.push({
          ItemNum: selectedLottery?.ItemNum,
          SkipReason: selectReason,
        });

        await AsyncStorage.setItem(
          'ShiftSkipReasons',
          JSON.stringify(skipReasons),
        );

        await AsyncStorage.setItem(selectedLottery?.ItemName, 'N/A');
        setCompletedItems(prev => new Set(prev).add(selectedLottery?.ItemName));

        // Remove automatic focus shifting - user will manually select next card
        // Just clear the current selection so user can manually select next card
        setCurrentItemIndex(-1);
        await AsyncStorage.removeItem('SETCURRENTINDEX');
        setModal(false);
      }
      setSelectedReason('');
      setIsSkipReset(false);
    }
  };

  useEffect(() => {
    inputRefs.current[currentItemIndex]?.focus();
  }, [resetStock]);
  useEffect(() => {
    inputRefs.current[currentItemIndex]?.focus();
  }, [modal]);
  const ResetShift = async () => {
    console.log('ISLOGGING HERE');

    try {
      if (!currentBarcode || currentBarcode === '' || currentBarcode === null) {
        Alert.alert('Must Be Scan the Book Number');
      } else {
        const shiftBookDetails = await GetItemsParamsNoFilter(
          (await getLotteryPort()).toString(),
          '/GetShiftBookTickets/:ItemNum/:Location',
          setGameDetails,
          {
            ItemNum: selectedLottery?.ItemNum,
            Location: selectedLottery?.Location,
          },
        );

        const Shift_ID = await GetCommonLatestID(
          (await getLotteryPort()).toString(),
          '/GetShiftID',
        );
        const getMissingTickets = await GetItemsParamsNoFilterNoReturn(
          (await getLotteryPort()).toString(),
          '/GetGameDetails/:Shift_ID/:Location',
          {
            Shift_ID: Number(Shift_ID) - 1,
            Location: selectedLottery?.Location,
          },
        );

        let endGameSerial = currentBarcode.split('-');
        let Adjustment = 0;
        let MainStockAdjustment = 0;
        let AdjustLower = 0;

        if (getMissingTickets[0]?.Shift_Close_Serial === endGameSerial[2]) {
          console.log(
            'LOG 2',
            getMissingTickets[0]?.Shift_Close_Serial,
            endGameSerial[2],
          );

          Adjustment = isAsc
            ? Number(endGameSerial[2]) -
              Number(shiftBookDetails[0]?.Open_Serial)
            : Number(shiftBookDetails[0]?.Open_Serial) -
              Number(endGameSerial[2]); // 097 - 097 = 0

          MainStockAdjustment =
            Number(shiftBookDetails[0]?.Book_Tickets) - Number(Adjustment); // 30 - 0
        } else {
          if (getMissingTickets[0]?.Shift_Close_Serial === 'N/A') {
            AdjustLower = isAsc
              ? Number(endGameSerial[2]) -
                Number(shiftBookDetails[0]?.Open_Serial)
              : Number(shiftBookDetails[0]?.Open_Serial) -
                Number(endGameSerial[2]); // 097 - 099 = 5

            MainStockAdjustment =
              Number(shiftBookDetails[0]?.Book_Tickets) - Number(AdjustLower); // 30 - 2 =  10
          } else {
            if (getMissingTickets[0]?.Shift_Open_Serial === endGameSerial[2]) {
              AdjustLower =
                Number(getMissingTickets[0]?.Shift_Open_Serial) -
                Number(endGameSerial[2]); // 099 - 097 = 2

              MainStockAdjustment =
                Number(shiftBookDetails[0]?.Book_Tickets) - Number(AdjustLower);
            } else {
              AdjustLower =
                Number(getMissingTickets[0]?.Shift_Close_Serial) -
                Number(endGameSerial[2]); // 097 - 097
            }
            MainStockAdjustment =
              Number(getMissingTickets[0]?.Stock_Level_Close) -
              Number(AdjustLower); // 30 - 0
          }
        }
        const inventoryData: Partial<Inventory> = {
          ItemNum: selectedLottery?.ItemNum,
          ItemName: selectedLottery?.ItemName,
          Dept_ID: selectedLottery?.Dept_ID,
          Cost: selectedLottery?.Cost,
          Price: selectedLottery?.Price,
          Retail_Price: selectedLottery?.Retail_Price,
          In_Stock: Number(MainStockAdjustment),
          Date_Created: selectedLottery?.Date_Created,
          Last_Sold: selectedLottery?.Last_Sold,
          Location: selectedLottery?.Location,
          Vendor_Number: selectedLottery?.Vendor_Number,
          Vendor_Part_Num: selectedLottery?.Vendor_Part_Num,
          Reorder_Level: selectedLottery?.Reorder_Level,
          Reorder_Quantity: selectedLottery?.Reorder_Quantity,
          ReOrder_Cost: selectedLottery?.ReOrder_Cost,
          Unit_Size: selectedLottery?.Unit_Size,
          Unit_Type: selectedLottery?.Unit_Type,
          FoodStampable: selectedLottery?.FoodStampable,
          Tax_1: selectedLottery?.Tax_1[0],
          Tax_2: selectedLottery?.Tax_2[0],
          Tax_3: selectedLottery?.Tax_3[0],
          Tax_4: selectedLottery?.Tax_4[0],
          Tax_5: selectedLottery?.Tax_5[0],
          Tax_6: selectedLottery?.Tax_6[0],
          Check_ID: selectedLottery?.Check_ID,
          Check_ID2: selectedLottery?.Check_ID2,
          Store_ID: selectedLottery?.Store_ID,
          ItemName_Extra: selectedLottery?.ItemName_Extra,
        };

        const applyDefault = applyDefaults(inventoryData);

        const result = await updateData<Inventory>({
          baseURL: (await getInventoryPort()).toString(),
          data: applyDefault,
          endpoint: '/updatebarcode',
        });

        let inventoryin = 0;
        let StockClose = 0;
        let missingtickets = 0;

        if (getMissingTickets[0]?.Shift_Close_Serial === endGameSerial[2]) {
          inventoryin =
            Number(selectedLottery?.In_Stock) - Number(MainStockAdjustment);
          StockClose = Number(getMissingTickets[0]?.Stock_Level_Close);
          missingtickets = Number(getMissingTickets[0]?.Missing_Ticket);
        } else {
          if (getMissingTickets[0]?.Shift_Close_Serial === 'N/A') {
            inventoryin =
              Number(selectedLottery?.In_Stock) - Number(MainStockAdjustment);
            StockClose = Number(MainStockAdjustment);
            missingtickets =
              Number(selectedLottery?.In_Stock) - Number(MainStockAdjustment);
            console.log('LOG 6', inventoryin, StockClose, missingtickets);
          } else {
            console.log('LOG 7');

            inventoryin =
              Number(selectedLottery?.In_Stock) - Number(AdjustLower);
            StockClose =
              Number(getMissingTickets[0]?.Stock_Level_Close) -
              Number(AdjustLower);
            if (getMissingTickets[0]?.Shift_Open_Serial === endGameSerial[2]) {
              missingtickets = 0;
            } else {
              missingtickets =
                Number(getMissingTickets[0]?.Missing_Ticket) -
                Number(AdjustLower);
            }
          }
        }

        if (result) {
          const storeId = await AsyncStorage.getItem('STOREID');
          const ValidStore = storeId === null ? '1001' : storeId;
          const CashierID = await AsyncStorage.getItem('SWIPEID');
          const ValideCashier = CashierID === null ? '100101' : CashierID;

          const inventoryAdjustData: Partial<Inventory_In> = {
            ItemNum: selectedLottery?.ItemNum,
            Store_ID: ValidStore,
            Quantity: '-' + inventoryin,
            DateTime: getFormateDate(Date()),
            Dirty: true,
            TransType: 'L',
            Description: 'LOTTERY SCRATCH',
            Cashier_ID: ValideCashier,
            CostPer: selectedLottery?.Cost,
          };
          const applyDefault =
            applyDefaultsInventoryAdjust(inventoryAdjustData);
          const result = await createData<Inventory_In>({
            baseURL: (await getInventoryPort()).toString(),
            data: applyDefault,
            endpoint: '/createinvetoryin',
          });

          if (result) {
            const ShiftDetails = await GetItemsParamsNoFilterNoReturn(
              (await getLotteryPort()).toString(),
              '/GetShiftDetails/:Shift_ID',
              {Shift_ID: Number(Shift_ID) - 1},
            );

            const shiftGameData: Partial<Game_Details> = {
              Game_ID: getMissingTickets[0]?.Game_ID,
              Open_Book: getMissingTickets[0]?.Open_Book,
              Close_Book: getMissingTickets[0]?.Close_Book,
              Stock_Level_Open: getMissingTickets[0]?.Stock_Level_Open,
              Stock_Level_Close: StockClose,
              Shift_Open_Serial: getMissingTickets[0]?.Shift_Open_Serial,
              Shift_Close_Serial: isSkipReset
                ? endGameSerial[2]
                : getMissingTickets[0]?.Shift_Close_Serial === endGameSerial[2]
                ? getMissingTickets[0]?.Shift_Close_Serial
                : endGameSerial[2],
              Missing_Ticket: isSkipReset ? missingBySkip : missingtickets,
              Location: getMissingTickets[0]?.Location,
              Shift_ID: getMissingTickets[0]?.Shift_ID,
              Date: getMissingTickets[0]?.Date,
              Reset: 'D',
              Shift_Start_Skip: '',
              Shift_End_Skip: '',
            };

            const applyDefault = applyDefaultsGameDetails(shiftGameData);
            shiftMailSend([applyDefault], ShiftDetails[0]);
            const result = await updateData<Game_Details>({
              baseURL: (await getLotteryPort()).toString(),
              data: applyDefault,
              endpoint: '/updatelotgamesdetails',
            });

            if (result) {
              setBarcodeMap(prevMap => ({
                ...prevMap,
                [selectedLottery.ItemNum]: [
                  {
                    ...prevMap[selectedLottery.ItemNum]?.[0],
                    In_Stock: MainStockAdjustment,
                  },
                ],
              }));

              // Update the missing tickets map to reflect reset
              setMissingTicketsMap(prevMap => ({
                ...prevMap,
                [selectedLottery.ItemNum]: [
                  {
                    ...prevMap[selectedLottery.ItemNum]?.[0],
                    Missing_Ticket: isSkipReset
                      ? missingBySkip
                      : missingtickets,
                    Reset: 'D', // Changed from 'Y' to 'D' to indicate it's been dealt with
                    Stock_Level_Close: StockClose,
                  },
                ],
              }));
              setResetStock(false);

              if (isSkipReset) {
                await AsyncStorage.setItem(
                  selectedLottery.ItemName,
                  currentBarcode,
                );
                setCompletedItems(prev =>
                  new Set(prev).add(selectedLottery.ItemName),
                );

                const nextIndex = lottery.findIndex(
                  (item, index) =>
                    !completedItems.has(item.ItemName) &&
                    index > currentItemIndex,
                );
                if (nextIndex >= 0) {
                  setCurrentItemIndex(nextIndex);

                  setTimeout(() => {
                    if (inputRefs.current) {
                      inputRefs.current[nextIndex]?.blur();
                      setTimeout(() => {
                        if (inputRefs.current) {
                          inputRefs.current[nextIndex]?.focus();
                        }
                      }, 1000);
                    }
                  }, 1000);
                }
              } else {
                handleSerialChange(currentBarcode, selectedLottery);
              }

              // Set the reset item data with the current barcode
              setResetItemData({
                itemNum: selectedLottery?.ItemNum,
                barcode: currentBarcode,
              });

              // Important: Force a re-render by updating the reference of currentBarcodef
              // This ensures the useEffect in TicketsDetails will trigger
              setCurrentBarcode(prev => prev + ''); // This creates a new string reference
              setIsSkipReset(false);
              setIsMissingBySkip(0);
              refreshItemData(selectedLottery?.ItemNum);
            }
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const codeScanner = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        setCurrentBarcode(codes[0].value);
        setCamera(false);
      } else {
      }
    },
  });

  const handleNavigation = async () => {
    await AsyncStorage.removeItem('SETCURRENTINDEX');
    navigation.goBack();
  };
  const [keyboardVisible, setKeyboardVisible] = useState<boolean>(false);

  const [ticketDetailsKey, setTicketDetailsKey] = useState(0);

  const handleCloseModal = () => {
    Keyboard.dismiss();
    setKeyboardVisible(false);
    setResetStock(false);
    setIsClosed(true);
    setCurrentBarcode('');
    setResetItemData({
      itemNum: '',
      barcode: '',
    });

    // Force re-render of TicketsDetails to clear states
    setTicketDetailsKey(prev => prev + 1);
  };
  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    container: {
      justifyContent: 'space-between',
      flex: 1,
    },
    contentContainer: {
      paddingHorizontal: wp('2.5%'),
    },
    gameCountText: {
      textAlign: 'center',
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      margin: 15,
    },
    bottomButtonContainer: {
      paddingVertical: hp('1%'),
      paddingHorizontal: wp('2.5%'),
      position: 'absolute',
      bottom: 0,
      right: 0,
      left: 0,
    },
    modalContainer: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '93%',
      height: '47%',
      borderRadius: 15,
      paddingVertical: 10,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
    },
    modalTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
    closeButton: {
      paddingLeft: 10,
    },
    alertContainer: {
      paddingHorizontal: wp('2.5%'),
    },
    alertTitle: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    alertText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    dropdownContainer: {
      marginTop: 20,
      alignItems: 'center',
      paddingHorizontal: wp('2.5%'),
    },
    modalFooter: {
      paddingHorizontal: wp('2.5%'),
      position: 'absolute',
      right: 0,
      left: 0,
      bottom: 0,
      marginBottom: 10,
    },
    resetInfoText: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    scanContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: FontSizes.small,
      paddingHorizontal: 20,
      width: '92%',
    },
  });
  const getItemLayout = (data: any, index: number) => ({
    length: 120, // Approximate height of each item
    offset: 120 * index,
    index,
  });

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={styles.contentContainer}>
        <Header
          NavName={status ? 'End Shift' : 'Starting Shift'}
          isProvid={true}
          Onpress={() => handleNavigation()}
        />
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            marginVertical: 5,
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
            <View
              style={{
                height: 10,
                width: 10,
                backgroundColor: colors.warning,
                borderRadius: 10,
              }}
            />
            <Text
              style={{
                fontFamily: Fonts.OnestRegular,
                color: colors.text,
                fontSize: 13,
              }}>
              Scan Ready
            </Text>
          </View>

          <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
            <View
              style={{
                height: 10,
                width: 10,
                backgroundColor: colors.success,
                borderRadius: 10,
              }}
            />
            <Text
              style={{
                fontFamily: Fonts.OnestRegular,
                color: colors.text,
                fontSize: 13,
              }}>
              Scanned
            </Text>
          </View>

          <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
            <View
              style={{
                height: 10,
                width: 10,
                backgroundColor: colors.error,
                borderRadius: 10,
              }}
            />
            <Text
              style={{
                fontFamily: Fonts.OnestRegular,
                color: colors.text,
                fontSize: 13,
              }}>
              Missing Tickets
            </Text>
          </View>
        </View>
        <Text style={[styles.gameCountText, {color: colors.text}]}>
          {`(${lottery.length}) Games Found`}
        </Text>
        <DataList
          data={lottery}
          renderItem={renderItem}
          loading={loading}
          Hight="75%"
          IsCreate={true}
          IsCreateMessage="Lottery Games Not Found Would You Like to Create New Game?"
          IsCreateBtnLabel="Add New Game"
          GetItemLayout={getItemLayout}
          onIsCreate={async () => {
            const lotteryDepartment = await AsyncStorage.getItem(
              'LOTTERY_DEP_ID',
            );

            if (lotteryDepartment) {
              navigation.navigate('Lookup', {
                isFromLottery: true,
                LOTTERYDEPT: lotteryDepartment,
              });
            }
          }}
        />
      </View>

      <View style={styles.bottomButtonContainer}>
        <FAB
          label={status ? 'End Shift' : 'Start Shift'}
          position="bottomRight"
          onPress={() => startOrEndShift()}
        />
      </View>

      <Modal
        key="skipModal"
        animationType="fade"
        transparent={true}
        visible={modal}
        onRequestClose={() => setModal(false)}>
        <View style={styles.modalContainer}>
          <View
            style={[styles.modalContent, {backgroundColor: colors.surface}]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, {color: colors.text}]}>
                Reason For Skip Counting
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModal(false)}>
                <AntDesign
                  name="closecircle"
                  color={colors.error}
                  size={hp('4%')}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.alertContainer}>
              <Text style={[styles.alertTitle, {color: colors.error}]}>
                Attention!
              </Text>
              <Text style={[styles.alertText, {color: colors.error}]}>
                A reason is required to skip counting {selectedBook} when
                starting your shift.
              </Text>
            </View>

            <View style={styles.dropdownContainer}>
              <AppDropDown
                label="Reason Code"
                options={reasonOptions}
                selectedValue={selectReason}
                onSelect={value => setSelectedReason(value)}
                isRequired={true}
                isAdd={true}
                onCreate={() => {
                  setModal(false);
                  setResetStock(false);
                  navigation.navigate('LotteryReason');
                }}
                Height="75%"
              />
            </View>

            <View style={styles.modalFooter}>
              <AppButton Title="SKIP" OnPress={() => SkipCounting()} />
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        key="resetModal"
        animationType="fade"
        transparent={true}
        visible={resetStock}
        onRequestClose={handleCloseModal}>
        <View style={styles.modalContainer}>
          <View
            style={[styles.modalContent, {backgroundColor: colors.surface}]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, {color: colors.text}]}>
                Reset Shift Tickets
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleCloseModal}>
                <AntDesign
                  name="closecircle"
                  color={colors.error}
                  size={hp('4%')}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.alertContainer}>
              <Text style={[styles.resetInfoText, {color: colors.text}]}>
                Last Shift Missing Tickets Adjustment!
              </Text>
              <Text style={[styles.alertText, {color: colors.error}]}>
                Adjust {selectedBook} for Missing Last Shift Stock
              </Text>
            </View>

            {isSkipReset && (
              <Text
                style={{
                  paddingHorizontal: 10,
                  paddingTop: 20,
                  fontFamily: Fonts.OnestMedium,
                  fontSize: FontSizes.medium,
                  color: colors.error,
                }}>
                Missing {missingBySkip || 0} Tickets Found in Last Shift
              </Text>
            )}
            {!isSkipReset && (
              <View style={styles.scanContainer}>
                <AppFocusForLotteryReset
                  PlaceHolder="Scan Book Number"
                  Title="Scan Book Number"
                  onChangeText={value => {
                    const formattedSerial = formatNumber(value);
                    setCurrentBarcode(formattedSerial);
                  }}
                  SelectedItem={selectedLottery}
                />
              </View>
            )}

            <View style={styles.modalFooter}>
              <AppButton Title="Reset" OnPress={() => ResetShift()} />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default LOT_InitialSetup;

const styles = StyleSheet.create({
  container: {
    backgroundColor: MaterialColors.background,
    justifyContent: 'space-between',
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: wp('2.5%'),
  },
  gameCountText: {
    textAlign: 'center',
    fontSize: FontSizes.medium,
    fontFamily: Fonts.OnestBold,
    color: MaterialColors.text.primary,
    margin: 15,
  },
  bottomButtonContainer: {
    paddingVertical: hp('1%'),
    paddingHorizontal: wp('2.5%'),
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
  },
  modalContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Backround,
    width: '93%',
    height: '47%',
    borderRadius: 15,
    paddingVertical: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: wp('2.5%'),
    paddingVertical: hp('1.5%'),
  },
  modalTitle: {
    fontFamily: Fonts.OnestBold,
    fontSize: FontSizes.medium,
  },
  closeButton: {
    paddingLeft: 10,
  },
  alertContainer: {
    paddingHorizontal: wp('2.5%'),
  },
  alertTitle: {
    fontFamily: Fonts.OnestBold,
    color: 'tomato',
    fontSize: FontSizes.small,
  },
  alertText: {
    fontFamily: Fonts.OnestBold,
    color: 'tomato',
    fontSize: FontSizes.small,
  },
  dropdownContainer: {
    marginTop: 20,
    alignItems: 'center',
    paddingHorizontal: wp('2.5%'),
  },
  modalFooter: {
    paddingHorizontal: wp('2.5%'),
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    marginBottom: 10,
  },
  resetInfoText: {
    fontFamily: Fonts.OnestBold,
    color: '#000',
    fontSize: FontSizes.small,
  },
  scanContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: FontSizes.small,
    paddingHorizontal: 20,
    width: '92%',
  },
});
