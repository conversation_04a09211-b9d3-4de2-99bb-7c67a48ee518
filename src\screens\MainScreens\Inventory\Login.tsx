import {View, Text, TouchableOpacity, Alert} from 'react-native';
import React, {useState} from 'react';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import AppButton from '../../../components/Inventory/AppButton';
import Header from '../../../components/Inventory/Header';
import AppTextInput from '../../../components/Inventory/AppTextInput';
import {
  GetItemsParamsNoFilter,
  GetItemsParamsNoFilterNoReturn,
} from '../../../utils/PublicHelper';
import {getInventoryPort, getLotteryPort} from '../../../server/InstanceTypes';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, SecondaryHint} from '../../../constants/Color';
import {Fonts} from '../../../styles/fonts';
import {Employee} from '../../../server/types';

type NavProps = {
  navigation: NativeStackNavigationProp<any>;
};

const Login: React.FC<NavProps> = ({navigation}) => {
  const [cashierID, setCashierID] = useState<string>('02');
  const [exists, setExists] = useState<Employee[]>([]);

  const loginEmployee = async () => {
    const getStoreID = await AsyncStorage.getItem('STOREID');
    const CashierID = getStoreID + cashierID;

    const swipeLoginWithoutStore = await GetItemsParamsNoFilter(
      (await getInventoryPort()).toString(),
      '/loginEmployee/:Cashier_ID',
      setExists,
      {Cashier_ID: cashierID},
    );

    if (
      Array.isArray(swipeLoginWithoutStore) &&
      swipeLoginWithoutStore.length === 0
    ) {
      const swipeLogin = await GetItemsParamsNoFilter(
        (await getInventoryPort()).toString(),
        '/loginEmployee/:Cashier_ID',
        setExists,
        {Cashier_ID: CashierID},
      );
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getLotteryPort()).toString(),
        '/GetEmployeePermission/:Emp_ID',
        {Emp_ID: CashierID},
      );
      if (Array.isArray(swipeLogin) && swipeLogin.length === 0) {
        Alert.alert('Login Failed, Check your Swipe ID');
      } else {
        const employeeData = swipeLogin[0];
        const lotteryPermissions = getBarcode
          ? {
              StartOrEndShift: getBarcode[0].StartOrEndShift || false,
              ActivateBook: getBarcode[0].ActivateBook || false,
              CreateNewGame: getBarcode[0].CreateNewGame || false,
              OrganizeSlot: getBarcode[0].OrganizeSlot || false,
              ViewShift: getBarcode[0].ViewShift || false,
              ResetShift: getBarcode[0].ResetShift || false,
              CFA_Inven_Add: getBarcode[0]?.CFA_Inven_Add || false,
              CFA_Inven_Edit: getBarcode[0]?.CFA_Inven_Edit || false,
              CFA_Vendors_Add: getBarcode[0]?.CFA_Vendors_Add || false,
              CFA_Depts_Add: getBarcode[0]?.CFA_Depts_Add || false,
              CFA_Depts_Edit: getBarcode[0]?.CFA_Depts_Edit || false,
              CFA_INVEN_VIEW: getBarcode[0]?.CFA_INVEN_VIEW || false,
              CFA_HH_Create_PO: getBarcode[0]?.CFA_HH_Create_PO || false,
              CFA_HH_DSD: getBarcode[0]?.CFA_HH_DSD || false,
              CFA_HH_Inv_Count: getBarcode[0]?.CFA_HH_Inv_Count || false,
              CFA_HH_PO_Receive: getBarcode[0]?.CFA_HH_PO_Receive || false,
              CFA_HH_Inv_Adjust: getBarcode[0]?.CFA_HH_Inv_Adjust || false,
              CFA_HH_PRINT_LABELS: getBarcode[0]?.CFA_HH_PRINT_LABELS || false,
            }
          : {
              StartOrEndShift: false,
              ActivateBook: false,
              CreateNewGame: false,
              OrganizeSlot: false,
              ViewShift: false,
              ResetShift: false,
              CFA_Inven_Add: false,
              CFA_Inven_Edit: false,
              CFA_Vendors_Add: false,
              CFA_Depts_Add: false,
              CFA_Depts_Edit: false,
              CFA_INVEN_VIEW: false,
              CFA_HH_Create_PO: false,
              CFA_HH_DSD: false,
              CFA_HH_Inv_Count: false,
              CFA_HH_PO_Receive: false,
              CFA_HH_Inv_Adjust: false,
              CFA_HH_PRINT_LABELS: false,
            };

        await AsyncStorage.setItem('SWIPEID', CashierID);
        await AsyncStorage.setItem('EMPLOYEE', JSON.stringify(employeeData));
        await AsyncStorage.setItem(
          'LOTTERY_PERMISSIONS',
          JSON.stringify(lotteryPermissions),
        );

        Alert.alert('Login Success');
        navigation.navigate('Home');
      }
    } else {
      await AsyncStorage.setItem('SWIPEID', cashierID);
      await AsyncStorage.setItem(
        'EMPLOYEE',
        JSON.stringify(swipeLoginWithoutStore[0]),
      );
      Alert.alert('Login Success');
      navigation.navigate('Home');
    }
  };
  return (
    <View
      style={{
        backgroundColor: Backround,
        justifyContent: 'space-between',
        paddingTop: hp('2%'),
        flex: 1,
      }}>
      <View style={{paddingHorizontal: wp('2.5%')}}>
        <Text
          style={{
            fontSize: hp('3.5%'),
            fontFamily: Fonts.OnestBold,
          }}>
          Login
        </Text>

        <Text
          style={{
            fontSize: hp('1.9%'),
            paddingTop: 10,
            paddingBottom: 30,
            fontFamily: Fonts.OnestBold,
            color: SecondaryHint,
          }}>
          Login your account from here!
        </Text>
        <AppTextInput
          PlaceHolder="Enter Your Cashier ID"
          Title="Cashier ID"
          Value={cashierID}
          onChangeText={swipe => setCashierID(swipe)}
        />
      </View>
      <View
        style={{
          backgroundColor: SecondaryHint,
          paddingVertical: hp('1%'),
          paddingHorizontal: wp('2.5%'),
        }}>
        <AppButton Title="Login" OnPress={() => loginEmployee()} />
      </View>
    </View>
  );
};

export default Login;
