import {View, Text, FlatList, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useNavigation} from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Header from '../../../components/Inventory/Header';
import {Backround} from '../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import AddUserModal from './AddUserModal';
import {fetchUsers} from '../../../services/apiService';
import {GetAllItems} from '../../../utils/PublicHelper';
import {getLotteryPort} from '../../../server/InstanceTypes';
import {App_User} from '../../../Types/Lottery/Lottery_Types';
import DataList from '../../../components/Inventory/AppList';
import { MaterialColors } from '../../../constants/MaterialColors';

const Users = () => {
  const [users, setUsers] = useState<App_User[]>([]);
  const [isModalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    getAllUsers();
  }, []);

  useEffect(() => {
    getAllUsers();
  }, [isModalVisible]);

  const getAllUsers = async () => {
    GetAllItems<App_User[]>(
      (await getLotteryPort()).toString(),
      '/GetUsers',
      setUsers,
      setLoading,
    );
  };
  // const loadUsers = async () => {
  //   try {
  //     const responseData = await fetchUsers();
  //     if (responseData.success) {
  //       setUsers(responseData.data.users);
  //     } else {
  //       console.error('Failed to fetch users:', responseData.message);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching users:', error);
  //   }
  // };p
  const handleUserAdded = newUser => {
    setUsers(prevUsers => [...prevUsers, newUser]);
  };

  const renderItem = ({item}: {item: App_User}) => (
    <View style={styles.userItem}>
      <Text style={styles.userId}>Emp ID: {item.Cashier_ID}</Text>

      <Text style={styles.userName}>Emp Name: {item.Emp_Name}</Text>
      <Text style={styles.userEmail}>Password: {item.Password}</Text>
      <Text style={styles.userRole}>Role: {item.Role}</Text>
    </View>
  );

  return (
    <View
      style={{
        paddingHorizontal: 14,
        height: '100%',
        width: '100%',
        backgroundColor: MaterialColors.surface,
      }}>
      <Header NavName="Users" />

      {/* <FlatList
        data={users}
        renderItem={renderItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContainer}
      /> */}

      <DataList data={users} renderItem={renderItem} loading={loading} />
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setModalVisible(true)}>
        <MaterialCommunityIcons name="account-plus" size={24} color="white" />
        <Text style={styles.addButtonText}>Add New User</Text>
      </TouchableOpacity>

      <AddUserModal
        visible={isModalVisible}
        onClose={() => setModalVisible(false)}
        onUserAdded={handleUserAdded}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#003366',
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  headerText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  listContainer: {
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  userItem: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    borderRadius: 8,
    marginBottom: 8,
  },
  userId: {
    fontSize: 14,
    color: '#888',
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  userRole: {
    fontSize: 14,
    color: '#888',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#007bff',
    borderRadius: 8,
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default Users;
