import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Alert,
  StyleSheet,
} from 'react-native';
import {RouteProp, useFocusEffect} from '@react-navigation/native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useCodeScanner} from 'react-native-vision-camera';

import {VendorItem} from '../../../server/types';
import {usePurchaseOrderVendorItems} from '../../../hooks/usePurchaseOrderVendorItems';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {Fonts, FontSizes} from '../../../styles/fonts';
import {MaterialColors} from '../../../constants/MaterialColors';

import Header from '../../../components/Inventory/Header';
import AppSearchWIthFilter from '../../../components/Inventory/AppSearchWIthFilter';
import DataList from '../../../components/Inventory/AppList';
import AppFilter from '../../../components/Inventory/AppFilter';
import AppScanner from '../../../components/Inventory/AppScanner';

type AddVendorItemsRouteProp = RouteProp<any, 'AddVendorItemsScreen'>;

interface AddVendorItemsScreenProps {
  route: AddVendorItemsRouteProp;
  navigation: any;
}

const AddVendorItemsScreen: React.FC<AddVendorItemsScreenProps> = ({
  route,
  navigation,
}) => {
  const {
    vendorItems = [],
    itemPO = [],
    mainPO,
    isEnableFilter = false,
    selectedDepartment = '',
    selectedVendor = '',
    selectedBrand = '',
    selectedSubCategory = '',
  } = route.params || {};

  const colors = useThemeColors();

  // Filter state
  const [filter, setFilter] = useState<boolean>(false);
  const [localIsEnableFilter, setLocalIsEnableFilter] =
    useState<boolean>(isEnableFilter);
  const [localSelectedDepartment, setLocalSelectedDepartment] =
    useState<string>(selectedDepartment);
  const [localSelectedVendor, setLocalSelectedVendor] =
    useState<string>(selectedVendor);
  const [localSelectedBrand, setLocalSelectedBrand] =
    useState<string>(selectedBrand);
  const [localSelectedSubCategory, setLocalSelectedSubCategory] =
    useState<string>(selectedSubCategory);

  // Use the custom hook
  const {
    vendorItemsFilter,
    searchQueryForModal,
    showLookup,
    loading,
    cameraModal,
    setCameraModal,
    textInputRef,
    toggleLookup,
    onChangeAddItems,
    handleDoneClickSub,
    isItemNumExist,
  } = usePurchaseOrderVendorItems(vendorItems, itemPO, navigation, mainPO);

  // Auto-focus the search input when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Small delay to ensure the screen is fully loaded before focusing
      const timer = setTimeout(() => {
        if (textInputRef?.current) {
          textInputRef.current.focus();
        }
      }, 100);

      // Cleanup function
      return () => clearTimeout(timer);
    }, [textInputRef]),
  );

  // Additional effect to handle focus when returning from any navigation
  useFocusEffect(
    useCallback(() => {
      // Ensure keyboard lookup state is properly set for scanning
      if (!showLookup) {
        toggleLookup(false); // Ensure keyboard is hidden for laser scanner input
      }

      // Focus the input with a longer delay for navigation scenarios
      const focusTimer = setTimeout(() => {
        if (textInputRef?.current) {
          textInputRef.current.focus();
        }
      }, 300);

      return () => clearTimeout(focusTimer);
    }, [showLookup, toggleLookup, textInputRef]),
  );

  // Effect to maintain focus when component updates
  useEffect(() => {
    if (textInputRef?.current && !cameraModal && !filter) {
      const timer = setTimeout(() => {
        textInputRef.current?.focus();
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [cameraModal, filter, textInputRef]);

  const VendorItemAdd = useCallback(
    (item: VendorItem) => {
      const IsExists = isItemNumExist(item.ItemNum);
      if (IsExists) {
        Alert.alert('Item Already Exists');
      } else {
        navigation.navigate('PurchaseOrderQuanity', {
          ItemData: item,
          Main: mainPO,
          poType: 0,
        });
      }
    },
    [isItemNumExist, navigation, mainPO],
  );

  const renderModalItem = ({item}: {item: VendorItem}) => {
    return (
      <TouchableOpacity
        style={styles.vendorItemCard}
        onPress={() => VendorItemAdd(item)}>
        <View style={styles.vendorItemInfo}>
          <Text style={[styles.vendorItemName, {color: colors.text}]}>
            {item.ItemName}
          </Text>
          <Text
            style={[styles.vendorItemDetail, {color: colors.textSecondary}]}>
            Per Case: {item.NumPerVenCase}
          </Text>
        </View>

        <Text style={[styles.vendorItemPrice, {color: colors.primary}]}>
          ${item.CostPer.toFixed(2)}
        </Text>
      </TouchableOpacity>
    );
  };

  const codeScannerModal = useCodeScanner({
    codeTypes: [
      'qr',
      'ean-13',
      'upc-a',
      'ean-8',
      'upc-e',
      'code-128',
      'code-39',
      'code-93',
    ],
    onCodeScanned: codes => {
      if (codes.length > 0) {
        onChangeAddItems(codes[0].value || '');
      } else {
        console.log('No valid barcode detected.');
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: wp('2.5%'),
    },
    headerContainer: {
      width: '95%',
      backgroundColor: colors.background,
    },
    searchContainer: {
      paddingBottom: hp('1%'),
    },
    totalItemsText: {
      fontSize: FontSizes.medium,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      paddingVertical: hp('1%'),
    },
    listContainer: {
      borderRadius: 10,
      backgroundColor: MaterialColors.surface,
    },
    listInnerContainer: {
      height: '82%',
      backgroundColor: colors.background,
    },
    filterContainer: {
      flex: 1,
    },
    scannerContainer: {
      width: '100%',
      height: '100%',
    },
    vendorItemCard: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2.5%'),
      paddingVertical: hp('1.5%'),
      borderBottomColor: colors.border,
      borderBottomWidth: 1,
      backgroundColor: colors.card,
    },
    vendorItemInfo: {
      gap: 7,
    },
    vendorItemName: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    vendorItemDetail: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.small,
    },
    vendorItemPrice: {
      fontFamily: Fonts.OnestBold,
      fontSize: FontSizes.medium,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Header
          NavName="Add Items"
          Onpress={() => {
            navigation.goBack();
          }}
          isProvid={true}
          isOption={true}
          Options={() => {
            setCameraModal(!cameraModal);
          }}
        />
      </View>

      {/* Search bar */}
      <View style={styles.searchContainer}>
        <AppSearchWIthFilter
          OnSearch={onChangeAddItems}
          SearchValue={searchQueryForModal}
          OnSearchSet={() => setFilter(true)}
          isEnableFilter={localIsEnableFilter}
          Keyboardon={showLookup}
          textInputRef={textInputRef}
          onToggleLookup={value => toggleLookup(value)}
          OnSubmitEditing={handleDoneClickSub}
        />

        <Text style={styles.totalItemsText}>
          {`Total Items: (${vendorItemsFilter.length || 0})`}
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.listInnerContainer}>
          <DataList
            data={vendorItemsFilter}
            keyExtractor={item => String(item.ItemNum)}
            renderItem={renderModalItem}
            loading={loading}
          />
        </View>
      </View>

      {/* Filter Modal */}
      <View style={styles.filterContainer}>
        <AppFilter
          isVisible={filter}
          setIsVisble={value => {
            setFilter(value);
            // Ensure the TextInput regains focus after closing filter modal
            if (!value) {
              setTimeout(() => {
                if (textInputRef?.current) {
                  textInputRef.current.focus();
                }
              }, 100);
            }
          }}
          Department={text => setLocalSelectedDepartment(text)}
          Vedor={text => setLocalSelectedVendor(text)}
          Brand={text => setLocalSelectedBrand(text)}
          Category={text => setLocalSelectedSubCategory(text)}
          EnableFilter={setLocalIsEnableFilter}
          selectedDepartment={localSelectedDepartment}
          selectedVendor={localSelectedVendor}
          selectedBrand={localSelectedBrand}
          selectedSubCategory={localSelectedSubCategory}
          isEnableFilter={localIsEnableFilter}
        />
      </View>

      {/* Camera Scanner Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={cameraModal}
        onRequestClose={() => {
          setCameraModal(false);
          // Ensure the TextInput regains focus after closing camera modal
          setTimeout(() => {
            if (textInputRef?.current) {
              textInputRef.current.focus();
            }
          }, 100);
        }}>
        <View style={styles.scannerContainer}>
          <AppScanner
            codeScanner={codeScannerModal}
            onClose={() => {
              setCameraModal(false);
              // Ensure the TextInput regains focus after closing scanner
              setTimeout(() => {
                if (textInputRef?.current) {
                  textInputRef.current.focus();
                }
              }, 100);
            }}
          />
        </View>
      </Modal>
    </View>
  );
};

export default AddVendorItemsScreen;
