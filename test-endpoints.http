LOTTERY_IP="*************"  # Replace with your lottery server IP
# LOTTERY_IP="127.0.0.1"
LOTTERY_PORT="9090"         # Replace with your lottery port
INVENTORY_IP="*************"  # Replace with your inventory server IP
INVENTORY_PORT="8090"       # Replace with your inventory port

PORT="8091"

http://*************:8090

### Test Lottery Server - Get All Active Books
# Replace {LOTTERY_IP} with your actual lottery server IP
# Replace {PORT} with your lottery server port (default: 8091)

GET http://{{LOTTERY_IP}}:{{PORT}}/api/GetAllActiveBooks
Accept: application/json

### Test Lottery Server - Get All Active Books
# Replace {LOTTERY_IP} with your actual lottery server IP
# Replace {PORT} with your lottery server port (default: 8091)

GET http://*************:9090/api/GetAllActiveBooks
Accept: application/json

### Test Inventory Server - Get Departments
# Replace {INVENTORY_IP} with your actual inventory server IP
# Replace {PORT} with your inventory server port (default: 8090)
GET http://{{INVENTORY_IP}}:{{PORT}}/api/GetDepartments
Accept: application/json

### Test Server Connection - Basic Health Check for Lottery
GET http://{{LOTTERY_IP}}:{{PORT}}/api/health
Accept: application/json

### Test Server Connection - Basic Health Check for Inventory
GET http://{{INVENTORY_IP}}:{{PORT}}/api/health
Accept: application/json

### Test with curl command for Lottery Server
# @name lotteryTest
# Run this in terminal:
# curl -v "http://{LOTTERY_IP}:{PORT}/api/GetAllActiveBooks"

### Test with curl command for Inventory Server
# @name inventoryTest
# Run this in terminal:
# curl -v "http://{INVENTORY_IP}:{PORT}/api/GetDepartments"