# CountItem Components

This directory contains the refactored CountItem functionality, broken down into reusable components for better maintainability and performance.

## Components

### CountItemList
- **Purpose**: Main list component that renders the inventory items with optimized performance
- **Features**: 
  - Virtualized FlatList with performance optimizations
  - Pull-to-refresh functionality
  - Infinite scrolling with pagination
  - Memoized rendering for better performance

### CountInventoryItem
- **Purpose**: Individual item component in the list
- **Features**:
  - Memoized component with optimized re-render conditions
  - Shows item details, stock information, and input values
  - Visual indicators for items with count updates

### CountItemModal
- **Purpose**: Modal for entering new stock counts
- **Features**:
  - Clean, focused interface for stock input
  - Auto-focus on text input
  - Proper keyboard handling

### CountItemHeader
- **Purpose**: Header component showing item count
- **Features**:
  - Displays total number of filtered items
  - Consistent styling with theme support

### CountItemActions
- **Purpose**: Action buttons (FAB) for count operations
- **Features**:
  - Start/Finish physical count functionality
  - Cancel operation support
  - Responsive positioning

### CountItemEmptyList
- **Purpose**: Empty state component
- **Features**:
  - Animated Lottie illustration
  - Context-aware messaging

### CountItemListFooter
- **Purpose**: Loading indicator for pagination
- **Features**:
  - Shows loading state when fetching more items
  - Consistent styling

## Performance Optimizations

1. **Memoization**: All components use React.memo with custom comparison functions
2. **Virtualization**: FlatList with optimized props for large datasets
3. **Efficient Re-renders**: Components only re-render when necessary props change
4. **Optimized Callbacks**: useCallback hooks prevent unnecessary re-renders
5. **Item Layout**: Pre-calculated item heights for smooth scrolling

## Usage

```tsx
import {
  CountItemList,
  CountItemModal,
  CountItemHeader,
  CountItemActions,
} from '../../../components/Inventory/CountItem';

// Use in your screen component
<CountItemList
  displayData={displayData}
  inputValues={inputValues}
  actionRef={actionRef}
  // ... other props
/>
```

## Dependencies

- React Native
- react-native-responsive-screen
- react-native-vector-icons
- lottie-react-native
- Theme system (useThemeColors, useTheme)
