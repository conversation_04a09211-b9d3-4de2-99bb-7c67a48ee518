import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  Keyboard,
  StyleSheet,
} from 'react-native';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {Primary, Secondary, SecondaryHint} from '../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {MaterialColors} from '../../constants/MaterialColors';
import {useThemeColors} from '../../Theme/useThemeColors';
import {useTheme} from '../../Theme/ThemeContext';

interface AppTextInputProps {
  PlaceHolder?: string;
  onChangeText?: (text: string) => void;
  Value?: string | number | null;
  Title?: string;
  Editable?: boolean;
  isNumeric?: boolean;
  error?: string;
  touched?: boolean;
  AutoFocus?: boolean;
  onBlur?: (e: any) => void;
  maxLength?: number;
  keyboardON?: boolean;
  clearInputTrigger?: boolean;
  isBarcode?: boolean;
  isRequired?: boolean;
  isFromLottery?: boolean;
  textInputRef?: React.RefObject<TextInput>;
  onToggleLookup?: (value: boolean) => void;
  IsAddPress?: () => void;
  IsAdd?: boolean;
}

const AppFocus: React.FC<AppTextInputProps> = ({
  PlaceHolder,
  onChangeText,
  Value,
  Title,
  Editable = true,
  isNumeric = false,
  keyboardON = false,
  error,
  touched,
  AutoFocus = true,
  onBlur,
  maxLength,
  clearInputTrigger,
  isBarcode = false,
  isRequired = false,
  textInputRef,
  isFromLottery = false,
  onToggleLookup,
  IsAdd = false,
  IsAddPress,
}: AppTextInputProps & {clearInputTrigger?: boolean}) => {
  const [inputValue, setInputValue] = useState(Value?.toString() ?? '');
  const [togglingIcon, setTogglingIcon] = useState(false);
  const isFocused = useIsFocused();
  const [clear, setClear] = useState<boolean>(false);
  useEffect(() => {
    setInputValue(Value?.toString() ?? '');
  }, [Value]);
  // Focus the input when the screen is focused
  useEffect(() => {
    if (isFocused) {
      textInputRef?.current?.focus();
    }
  }, [isFocused]);
  useEffect(() => {
    if (clearInputTrigger) {
      handleDismissKeyboard();
      if (!isBarcode) {
        setInputValue('');
      }
      textInputRef?.current?.focus(); // Clear input when trigger is true
    }
  }, [clearInputTrigger]);

  // Handle the clear input action
  const handleClearInput = () => {
    if (!isBarcode) {
      setInputValue('');
    }
    setClear(false);
    if (onChangeText) {
      onChangeText('');
    }
  };

  useFocusEffect(
    useCallback(() => {
      handleDismissKeyboard();
      if (!isBarcode) {
        setInputValue('');
      }
    }, []),
  );

  const handleDismissKeyboard = () => {
    Keyboard.dismiss();
  };
  useEffect(() => {
    if (!isBarcode) {
      setInputValue(Value?.toString() ?? '');
    }
  }, [Value]);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const styles = StyleSheet.create({
    addButton: {
      width: wp('10.5%'),
      height: wp('10.5%'),
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 8,
      borderWidth: 1,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 2,
      elevation: 2,
      marginLeft: 10,
    },
  });
  return (
    <View style={{marginBottom: 15}}>
      {Title && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: hp('1.5%'),
              marginBottom: hp('1%'),
              fontFamily: Fonts.OnestMedium,
              color: colors.text,
            }}>
            {Title}
          </Text>
          {isRequired && (
            <Text style={{color: colors.error, marginBottom: hp('1%')}}>
              {' '}
              *
            </Text>
          )}
        </View>
      )}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View
          style={{
            borderColor: error && touched ? colors.error : colors.border,
            borderWidth: 1,
            borderRadius: 10,
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 20,
            justifyContent: 'space-between',
            backgroundColor: colors.surface,
            shadowColor: colors.shadow,
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: isDark ? 0.3 : 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}>
          <TextInput
            placeholder={PlaceHolder}
            style={
              !Editable
                ? {
                    fontSize: FontSizes.medium,
                    fontFamily: Fonts.OnestMedium,
                    width: IsAdd ? '74%' : '84%',
                    color: colors.textSecondary,
                  }
                : {
                    fontSize: FontSizes.medium,
                    fontFamily: Fonts.OnestMedium,
                    width: IsAdd ? '74%' : '84%',
                    color: colors.text,
                  }
            }
            placeholderTextColor={colors.placeholder}
            value={inputValue}
            editable={Editable}
            showSoftInputOnFocus={keyboardON}
            onChangeText={text => {
              setInputValue(text);
              setClear(true);
              if (onChangeText) onChangeText(text);
            }}
            keyboardType={isNumeric ? 'numeric' : 'default'}
            onBlur={e => {
              onBlur?.(e);
              if (!togglingIcon && keyboardON) {
                onToggleLookup?.(false);
              }
            }}
            maxLength={maxLength}
            autoFocus={AutoFocus}
            ref={textInputRef}
          />

          {isFromLottery ? (
            <TouchableOpacity
              onPress={() => handleClearInput()}
              style={{
                padding: 10,
                backgroundColor: togglingIcon
                  ? colors.primary
                  : colors.secondary,
                borderRadius: 10,
                shadowColor: colors.shadow,
                shadowOffset: {
                  width: 0,
                  height: 1,
                },
                shadowOpacity: isDark ? 0.3 : 0.2,
                shadowRadius: 2,
                elevation: 2,
              }}>
              <MaterialCommunityIcons
                name="barcode-scan"
                size={20}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPressIn={() => setTogglingIcon(true)}
              onPressOut={() => {
                setTimeout(() => setTogglingIcon(false), 300);
              }}
              onPress={() => {
                onToggleLookup?.(!keyboardON);
              }}
              style={{paddingRight: IsAdd ? 0 : 20}}>
              <MaterialCommunityIcons
                name="keyboard"
                size={hp('4%')}
                color={keyboardON ? colors.primary : colors.disabled}
              />
            </TouchableOpacity>
          )}
        </View>
        {IsAdd && (
          <TouchableOpacity
            style={[
              styles.addButton,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
                shadowColor: colors.shadow,
              },
            ]}
            onPress={IsAddPress}>
            <MaterialCommunityIcons
              name="book-plus-multiple"
              color={colors.primary}
              size={hp('2.8%')}
            />
          </TouchableOpacity>
        )}
      </View>

      {error && touched && (
        <Text style={{color: colors.error, fontSize: 12, marginTop: 5}}>
          {error}
        </Text>
      )}

      {maxLength && inputValue && (
        <Text style={{fontSize: 12, color: colors.textSecondary, marginTop: 5}}>
          {inputValue.length}/{maxLength} characters
        </Text>
      )}
    </View>
  );
};

export default AppFocus;
