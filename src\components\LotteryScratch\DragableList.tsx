import React, {useState} from 'react';
import {
  SafeAreaView,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import DraggableFlatList from 'react-native-draggable-flatlist';
import {GestureHandlerRootView} from 'react-native-gesture-handler';

interface Item {
  key: string;
  label: string;
  location: string;
}

const App: React.FC = () => {
  const [data, setData] = useState<Item[]>([
    {key: '1', label: 'monkey', location: 'LOC1'},
    {key: '2', label: 'elephant', location: 'LOC2'},
    {key: '3', label: 'tiger', location: 'LOC3'},
    {key: '4', label: 'rabbit', location: 'LOC4'},
    {key: '5', label: 'parrot', location: 'LOC5'},
  ]);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const renderItem = ({
    item,
    index,
    drag,
    isActive,
  }: {
    item: Item;
    index: number;
    drag: () => void;
    isActive: boolean;
  }) => (
    <View
      style={[styles.item, {backgroundColor: isActive ? '#d3d3d3' : '#fff'}]}
      // Trigger the drag action when the item is pressed anywhere
      onStartShouldSetResponder={drag}>
      <Text>{item.label}</Text>
      <Text>{item.location}</Text>
    </View>
  );

  const handleDragEnd = (newData: Item[]) => {
    setIsLoading(true);

    setTimeout(() => {
      setData(newData);
      setIsLoading(false);
    }, 500);
  };

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <SafeAreaView style={styles.container}>
        {/* Show loading indicator when isLoading is true */}
        {isLoading ? (
          <ActivityIndicator
            size="large"
            color="#0000ff"
            style={styles.loadingIndicator}
          />
        ) : null}

        <DraggableFlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={item => item.key}
          onDragEnd={({data}) => handleDragEnd(data)}
        />
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  item: {
    padding: 20,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: -30}, {translateY: -30}], // Center the indicator
  },
});

export default App;
