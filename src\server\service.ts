// apiService.ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import createAxiosInstance from './axiosInstance';
import { Alert } from 'react-native';

type FetchError = string | { message: string; code?: number };

// Keep track of recent errors to avoid alert spam
const recentErrors = new Map<string, number>();
const ERROR_COOLDOWN = 5000; // 5 seconds

// Helper to check if we should show alert
const shouldShowAlert = (errorKey: string): boolean => {
  const lastShown = recentErrors.get(errorKey);
  const now = Date.now();
  
  if (!lastShown || now - lastShown > ERROR_COOLDOWN) {
    recentErrors.set(errorKey, now);
    return true;
  }
  return false;
};

// Helper to handle errors consistently
const handleApiError = (error: unknown, operation: string): never => {
  let errorMessage = 'An unexpected error occurred';
  let shouldAlert = true;

  if (axios.isAxiosError(error)) {
    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      errorMessage = 'Request timed out. Please check your connection.';
    } else if (error.message === 'Network Error' || !error.response) {
      errorMessage = 'Network error. Please check your internet connection.';
    } else if (error.response) {
      errorMessage = error.response.data?.message || error.message;
      // Don't alert for client errors (4xx) except 408 (timeout)
      shouldAlert = error.response.status >= 500 || error.response.status === 408;
    }
  }

  // Only show alert if appropriate and not recently shown
  if (shouldAlert && shouldShowAlert(`${operation}-${errorMessage}`)) {
    Alert.alert(
      "Connection Error",
      errorMessage,
      [
        { text: "OK" },
        { 
          text: "Retry", 
          onPress: () => {
            // Clear error tracking to allow immediate retry
            recentErrors.clear();
          }
        }
      ]
    );
  }

  // Throw the original error data structure expected by the app
  if (axios.isAxiosError(error)) {
    throw error.response ? error.response.data : error.message;
  } else {
    throw errorMessage;
  }
};

// Generic fetch function - UNCHANGED SIGNATURE
export const fetchAllItems = async <T>(baseURL: string, url: string): Promise<T> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    const response: AxiosResponse<T> = await axiosInstance.get<T>(url);
    return response.data;
  } catch (error: unknown) {
    return handleApiError(error, 'fetchAllItems');
  }
};

// Generic function to fetch a single item from a dynamic route URL - UNCHANGED SIGNATURE
export const fetchSingleItem = async <T>(baseURL: string, url: string, ItemNum: string): Promise<T> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    const response = await axiosInstance.get<T>(`${url}/${ItemNum}`);
    return response.data;
  } catch (error: unknown) {
    return handleApiError(error, 'fetchSingleItem');
  }
};

// UNCHANGED SIGNATURE - Fixed typo in original name but keeping it for compatibility
export const fetchMultiPlartform = async <T>(baseURL: string, endpoint: string, params: object = {}): Promise<T> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    // Dynamically replace placeholders in the endpoint URL with actual values from the params object
    const url = Object.keys(params).reduce((url, key) => {
      return url.replace(`:${key}`, (params as any)[key]);
    }, endpoint);

    const response = await axiosInstance.get<T>(url);
    return response.data;
  } catch (error: unknown) {
    return handleApiError(error, 'fetchMultiPlartform');
  }
};

// UNCHANGED SIGNATURE
export const createItem = async <T, R = any>(baseURL: string, path: string, data: Partial<T>, config?: AxiosRequestConfig): Promise<R> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    // Create a copy of the data object and set missing properties to null
    const payload: Record<string, any> = Object.keys(data).reduce((acc, key) => {
      acc[key] = data[key as keyof T] ?? null;
      return acc;
    }, {} as Record<string, any>);

    const response = await axiosInstance.post<R>(path, payload, config);
    return response.data;
  } catch (error) {
    return handleApiError(error, 'createItem');
  }
};

// UNCHANGED SIGNATURE
export const updateItem = async <T, R = any>(
  baseURL: string,
  path: string, 
  data: Partial<T>, 
  config?: AxiosRequestConfig
): Promise<R> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    // Create a copy of the data object and set missing properties to null
    const payload: Record<string, any> = Object.keys(data).reduce((acc, key) => {
      acc[key] = data[key as keyof T] ?? null;
      return acc;
    }, {} as Record<string, any>);

    const response = await axiosInstance.put<R>(path, payload, config);
    return response.data;
  } catch (error) {
    return handleApiError(error, 'updateItem');
  }
};

// UNCHANGED SIGNATURE
export const deleteItem = async <T>(baseURL: string, endpoint: string, params: object = {}): Promise<T> => {
  const axiosInstance = createAxiosInstance(baseURL);
  try {
    // Dynamically replace placeholders in the endpoint URL with actual values from the params object
    const url = Object.keys(params).reduce((url, key) => {
      return url.replace(`:${key}`, (params as any)[key]);
    }, endpoint);

    const response = await axiosInstance.delete<T>(url);
    return response.data;
  } catch (error: unknown) {
    return handleApiError(error, 'deleteItem');
  }
};