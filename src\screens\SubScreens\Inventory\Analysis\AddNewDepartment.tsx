import {View, Text, ScrollView, Alert, TextInput} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {Backround, Secondary, SecondaryHint} from '../../../../constants/Color';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Header from '../../../../components/Inventory/Header';
import AppTextInput from '../../../../components/Inventory/AppTextInput';
import CustomCheckbox from '../../../../components/Inventory/CustomCheckbox';
import {Fonts} from '../../../../styles/fonts';
import AppButton from '../../../../components/Inventory/AppButton';
import {Department_CRUD} from '../../../../server/types';
import {applyDefaultsDepartment} from '../../../../Validator/Inventory/Barcode';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  createData,
  GetItemsParamsNoFilterNoReturn,
  showAlert,
  showAlertOK,
} from '../../../../utils/PublicHelper';
import {getInventoryPort} from '../../../../server/InstanceTypes';
import {RouteProp} from '@react-navigation/native';
import {useThemeColors} from '../../../../Theme/useThemeColors';
import {useTheme} from '../../../../Theme/ThemeContext';

// const AddNewDepartment = () => {
type BarcodeScreenRouteProp = RouteProp<any, 'NewDepartment'>;
const AddNewDepartment: React.FC<{
  route: BarcodeScreenRouteProp;
  navigation: any;
}> = ({route, navigation}) => {
  const [depId, setDepID] = useState<string>('');
  const [depName, setDepName] = useState<string>('');
  const [reciptNotes, setReciptNotes] = useState<string>('');
  const [squareFootage, setSquareFootage] = useState<string>('');
  const [itemCost, setItemCost] = useState<string>('');
  const [isEdit, setIsEdit] = useState<boolean>(true);
  const [printDepartment, setPrintDepartment] = useState<boolean>(false);
  const [requirePermission, setRequirePermission] = useState<boolean>(false);
  const [requireSerial, setRequireSerial] = useState<boolean>(false);
  const [barTax, setBarTax] = useState<boolean>(false);
  const [scaleExport, setScaleExport] = useState<boolean>(false);
  const textInputRef = useRef<TextInput>(null);

  const AddDepartments = async () => {
    if (depId === '' || !depId || depName === '' || !depName) {
      Alert.alert('Please Enter Required Fields!');
    } else {
      const storeId = await AsyncStorage.getItem('STOREID');
      const ValidStore = storeId === null ? '1001' : storeId;

      const AdditionalInfo: Partial<Department_CRUD> = {
        Dept_ID: depId,
        Description: depName,
        Dept_Notes: reciptNotes,
        Square_Footage: Number(squareFootage),
        Cost_Calculation_Percentage: Number(itemCost),
        Print_Dept_Notes: printDepartment,
        Require_Permission: requirePermission,
        Require_Serials: requireSerial,
        BarTaxInclusive: barTax,
        IncludeInScaleExport: scaleExport,
        SubType: 'NONE',
        Store_ID: ValidStore,
        TSDisplay: true,
      };
      const applyDefualts = applyDefaultsDepartment(AdditionalInfo);
      console.log(applyDefualts);

      const result = await createData<Department_CRUD>({
        baseURL: (await getInventoryPort()).toString(),
        data: applyDefualts,
        endpoint: '/createdepartment',
      });
      if (result) {
        showAlert(
          `Your Department Has Been Added. Would You Like to Add Another?`,
          'Confirmation',
          true,
          'NO',
          'YES',
        )
          .then(async result => {
            if (result) {
              setDepID('');
              setDepName('');
              setPrintDepartment(false);
              setRequirePermission(false);
              setReciptNotes('');
              textInputRef.current?.focus();
            } else {
              navigation.goBack();
            }
          })
          .catch(error => {
            console.error('Error showing alert', error);
          });
      }
    }
  };

  const checkExists = async () => {
    if (depId === '') {
      setDepID('');
    } else {
      const getBarcode = await GetItemsParamsNoFilterNoReturn(
        (await getInventoryPort()).toString(),
        '/getExistDepartments/:Dept_ID',
        {Dept_ID: depId},
      );
      if (Array.isArray(getBarcode) && getBarcode.length > 0) {
        showAlertOK(
          `This Department ID Already in Use. By an Department With a Discription: ${getBarcode[0]?.Description}`,
          'Already in Use',
        );
        setDepID('');
        textInputRef.current?.focus();
      }
    }
  };

  useEffect(() => {
    textInputRef.current?.focus();
  }, []);
  const colors = useThemeColors();
  const {isDark} = useTheme();

  return (
    <View
      style={{
        backgroundColor: colors.background,
        height: '100%',
        width: wp('100%'),
        paddingHorizontal: wp('2.5%'),
      }}>
      <Header NavName="Add New Department" />
      <ScrollView style={{maxHeight: '80%'}}>
        <View>
          <AppTextInput
            PlaceHolder="Enter Department ID"
            Title="Department ID"
            Value={depId}
            onChangeText={text => setDepID(text)}
            isRequired={true}
            Editable={isEdit ? true : false}
            textInputRef={textInputRef}
            onBlur={() => checkExists()}
            maxLength={8}
          />
          <AppTextInput
            PlaceHolder="Enter Department Description"
            Title="Department Description"
            Value={depName}
            onChangeText={text => setDepName(text)}
            isRequired={true}
            Editable={isEdit ? true : false}
            maxLength={30}
          />
          <AppTextInput
            PlaceHolder="Enter Receipt Notes"
            Title="Receipt Notes"
            Value={reciptNotes}
            onChangeText={text => setReciptNotes(text)}
            Editable={isEdit ? true : false}
            //IsMutipleLine={true}
          />
          <AppTextInput
            PlaceHolder="Enter Square Footage"
            Title="Square Footage"
            Value={squareFootage}
            onChangeText={text => setSquareFootage(text)}
            Editable={isEdit ? true : false}
            //IsMutipleLine={true}
            isNumeric
          />
          <AppTextInput
            PlaceHolder="Enter Item Cost Percentage"
            Title="Item Cost Percentage"
            Value={itemCost}
            onChangeText={text => setItemCost(text)}
            Editable={isEdit ? true : false}
            //IsMutipleLine={true}
            isNumeric
          />

          <View style={{gap: 15}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <CustomCheckbox
                isChecked={printDepartment}
                onChange={value => setPrintDepartment(value)}
              />
              <Text
                style={{
                  marginLeft: 5,
                  color: SecondaryHint,
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.7%'),
                }}>
                Print Department Notes on Receipt
              </Text>
            </View>

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <CustomCheckbox
                isChecked={requirePermission}
                onChange={value => setRequirePermission(value)}
              />
              <Text
                style={{
                  marginLeft: 5,
                  color: SecondaryHint,
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.7%'),
                }}>
                Require Permission for Sale
              </Text>
            </View>

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <CustomCheckbox
                isChecked={requireSerial}
                onChange={value => setRequireSerial(value)}
              />
              <Text
                style={{
                  marginLeft: 5,
                  color: SecondaryHint,
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.7%'),
                }}>
                Require Serial #/ Reference Entry
              </Text>
            </View>

            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <CustomCheckbox
                isChecked={barTax}
                onChange={value => setBarTax(value)}
              />
              <Text
                style={{
                  marginLeft: 5,
                  color: SecondaryHint,
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.7%'),
                }}>
                Bar Tax Inclusive
              </Text>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <CustomCheckbox
                isChecked={scaleExport}
                onChange={value => setScaleExport(value)}
              />
              <Text
                style={{
                  marginLeft: 5,
                  color: SecondaryHint,
                  fontFamily: Fonts.OnestBold,
                  fontSize: hp('1.7%'),
                }}>
                Include in Scale Export
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          left: 0,
          paddingHorizontal: wp('2.5%'),
          paddingVertical: hp('1.5%'),
          backgroundColor: colors.background,
        }}>
        <AppButton Title="Save" OnPress={() => AddDepartments()} />
      </View>
    </View>
  );
};

export default AddNewDepartment;
