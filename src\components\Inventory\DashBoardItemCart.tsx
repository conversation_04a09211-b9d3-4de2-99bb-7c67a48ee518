import {View, Text, StyleSheet} from 'react-native';
import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Fonts, FontSizes} from '../../styles/fonts';
import {MaterialColors} from '../../constants/MaterialColors';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useThemeColors} from '../../Theme/useThemeColors';

interface Props {
  Name: string;
  Department: number;
  Date: string;
  Price: string;
  // Optional icon name from MaterialIcons
  IconName?: string;
}

const DashBoardItemCart = ({
  Name,
  Department,
  Price,
  Date,
  IconName = 'shopping-bag',
}: Props) => {
  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: wp('2%'),
      paddingVertical: hp('0.8%'),
      borderBottomWidth: 0.5,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    iconContainer: {
      marginRight: wp('1.5%'),
      justifyContent: 'center',
      alignItems: 'center',
      width: wp('8%'),
      height: wp('8%'),
      borderRadius: wp('4%'),
      backgroundColor: colors.primary + '15',
    },
    infoContainer: {
      flex: 2,
      gap: 1,
    },
    metaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('0.5%'),
    },
    nameText: {
      fontSize: FontSizes.small - 1,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginBottom: 2,
      maxWidth: wp('45%'),
    },
    quantityText: {
      fontSize: FontSizes.small - 1,
      fontFamily: Fonts.OnestBold,
      color: colors.textSecondary,
    },
    dotSeparator: {
      fontSize: wp('1.8%'),
      color: colors.textSecondary,
      marginHorizontal: wp('1%'),
    },
    dateText: {
      fontSize: FontSizes.small - 1,
      fontFamily: Fonts.OnestRegular,
      color: colors.textSecondary,
      marginLeft: wp('0.5%'),
    },
    priceTextContainer: {
      flex: 1,
      alignItems: 'flex-end',
      maxWidth: wp('25%'),
    },
    priceText: {
      fontSize: wp('3.2%'),
      fontFamily: Fonts.OnestBold,
      color: colors.primary,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Icon name={IconName} size={wp('4%')} color={colors.primary} />
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.nameText} numberOfLines={1} ellipsizeMode="tail">
          {Name}
        </Text>
        <View style={styles.metaContainer}>
          <Text style={styles.quantityText}>Qty: {Department}</Text>
          <Text style={styles.dotSeparator}>•</Text>
          <View style={styles.dateContainer}>
            <MaterialCommunityIcons
              name="barcode"
              size={wp('2.5%')}
              color={colors.textSecondary}
            />
            <Text style={styles.dateText}>{Date}</Text>
          </View>
        </View>
      </View>
      <View style={styles.priceTextContainer}>
        <Text style={styles.priceText} numberOfLines={1}>
          ${Price}
        </Text>
      </View>
    </View>
  );
};

export default DashBoardItemCart;
