// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

import {
  calculateSerialDifference,
  calculateTicketsSold,
  calculateRemainingStock,
} from '../PublicHelper';

describe('Serial Number Order Utility Functions', () => {
  describe('calculateSerialDifference', () => {
    it('should calculate difference correctly for ascending order', () => {
      // Test case: 010 - 005 = 5 (ascending)
      const result = calculateSerialDifference(10, 5, 'ascending');
      expect(result).toBe(5);
    });

    it('should calculate difference correctly for descending order', () => {
      // Test case: 100 - 095 = 5 (descending, but start > end)
      const result = calculateSerialDifference(95, 100, 'descending');
      expect(result).toBe(5);
    });

    it('should handle zero difference', () => {
      const ascendingResult = calculateSerialDifference(10, 10, 'ascending');
      const descendingResult = calculateSerialDifference(10, 10, 'descending');
      
      expect(ascendingResult).toBe(0);
      expect(descendingResult).toBe(0);
    });

    it('should handle negative differences correctly', () => {
      // Ascending: end < start should give negative
      const ascendingResult = calculateSerialDifference(5, 10, 'ascending');
      expect(ascendingResult).toBe(-5);

      // Descending: start < end should give negative  
      const descendingResult = calculateSerialDifference(10, 5, 'descending');
      expect(descendingResult).toBe(-5);
    });
  });

  describe('calculateTicketsSold', () => {
    it('should return serial difference for both orders', () => {
      const ascendingResult = calculateTicketsSold(15, 5, 'ascending');
      const descendingResult = calculateTicketsSold(15, 5, 'descending');
      
      expect(ascendingResult).toBe(5);
      expect(descendingResult).toBe(5);
    });
  });

  describe('calculateRemainingStock', () => {
    it('should calculate remaining stock correctly', () => {
      const result = calculateRemainingStock(15, 5);
      expect(result).toBe(10);
    });

    it('should handle zero tickets sold', () => {
      const result = calculateRemainingStock(15, 0);
      expect(result).toBe(15);
    });

    it('should handle all tickets sold', () => {
      const result = calculateRemainingStock(15, 15);
      expect(result).toBe(0);
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle ascending order scenario (000, 001, 002, etc.)', () => {
      // Book has 15 tickets, starts at 005, ends at 010
      const startSerial = 5;
      const endSerial = 10;
      const totalTickets = 15;
      
      const ticketsSold = calculateSerialDifference(endSerial, startSerial, 'ascending');
      const remainingStock = calculateRemainingStock(totalTickets, ticketsSold);
      
      expect(ticketsSold).toBe(5);
      expect(remainingStock).toBe(10);
    });

    it('should handle descending order scenario (100, 099, 098, etc.)', () => {
      // Book has 15 tickets, starts at 100, ends at 095
      const startSerial = 100;
      const endSerial = 95;
      const totalTickets = 15;
      
      const ticketsSold = calculateSerialDifference(endSerial, startSerial, 'descending');
      const remainingStock = calculateRemainingStock(totalTickets, ticketsSold);
      
      expect(ticketsSold).toBe(5);
      expect(remainingStock).toBe(10);
    });
  });
});
