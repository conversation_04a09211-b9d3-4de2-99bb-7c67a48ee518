import {View, Text, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import ChoieItemCard from '../../../components/Inventory/ChoieItemCard';
import Header from '../../../components/Inventory/Header';
import {Inventory} from '../../../server/types';
import {RouteProp} from '@react-navigation/native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {Backround, Secondary, SecondaryHint} from '../../../constants/Color';
import {Fonts, FontSizes} from '../../../styles/fonts';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {MaterialColors} from '../../../constants/MaterialColors';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
type BarcodeScreenRouteProp = RouteProp<any, 'Barcode'>;

const ItemType: React.FC<{route: BarcodeScreenRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
  const colors = useThemeColors();
  const {isDark} = useTheme();
  return (
    <View
      style={{
        width: wp('100%'),
        height: hp('100%'),
        paddingHorizontal: 10,
        backgroundColor: colors.background,
      }}>
      <Header NavName="Select Item Type" />

      <View style={{marginTop: 50}}>
        <TouchableOpacity
          style={{
            borderRadius: 10,
            paddingVertical: hp('1.9%'),
            paddingHorizontal: 10,
            marginTop: hp('1.5%'),
            backgroundColor: colors.card,
            alignItems: 'center',
            flexDirection: 'row',
            borderWidth: 1.5,
            borderColor: colors.border,
          }}
          onPress={() => {
            if (route.params?.ISPO) {
              navigation.navigate('Barcode', {
                BARCODENEW: route.params?.ItemData,
                ISPO: route.params?.ISPO,
                MAINPO: route.params?.MAINPO,
                ISDSD: route.params?.ISDSD,
                POTYPE: route.params?.POTYPE,
              });
            } else {
              navigation.navigate('Barcode', {
                BARCODENEW: route.params?.ItemData,
                ISCREATE: route.params?.ISCREATE,
              });
            }
          }}>
          <View>
            <AntDesign name="addfile" color={colors.text} size={hp('3%')} />
          </View>
          <View style={{marginLeft: 20}}>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: FontSizes.medium,
                color: colors.text,
              }}>
              Standard Item
            </Text>
            {route.params?.ItemData && (
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.medium,
                  color: colors.textSecondary,
                }}>
                {route.params?.ItemData}
              </Text>
            )}
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            borderRadius: 10,
            paddingVertical: hp('1.9%'),
            paddingHorizontal: 10,
            marginTop: hp('1.5%'),
            backgroundColor: colors.card,
            alignItems: 'center',
            flexDirection: 'row',
            borderWidth: 1.5,
            borderColor: colors.border,
          }}
          onPress={() => {
            console.log('LOTTINGDSKJF', route.params?.ItemData);

            navigation.navigate('ChoiceBarcode', {
              ItemData: route.params?.ItemData || '',
              IsLookup: route.params?.IsLookup || false,
            });
          }}>
          <View>
            <MaterialIcons
              name="assignment-add"
              color={colors.text}
              size={hp('3%')}
            />
          </View>
          <View style={{marginLeft: 20}}>
            <Text
              style={{
                fontFamily: Fonts.OnestBold,
                fontSize: FontSizes.medium,
                color: colors.text,
              }}>
              Choice Item
            </Text>
            {route.params?.ItemData && (
              <Text
                style={{
                  fontFamily: Fonts.OnestBold,
                  fontSize: FontSizes.medium,
                  color: colors.textSecondary,
                }}>
                {route.params?.ItemData}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ItemType;
