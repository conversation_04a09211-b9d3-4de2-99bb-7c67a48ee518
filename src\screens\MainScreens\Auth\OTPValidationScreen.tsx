import React, {useState, useRef} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation, useRoute} from '@react-navigation/native';
import {validateOTP} from '../../../services/apiService';
import {MaterialColors} from '../../../constants/MaterialColors';
import Header from '../../../components/Inventory/Header';
import {useThemeColors} from '../../../Theme/useThemeColors';
import {useTheme} from '../../../Theme/ThemeContext';
import {Fonts} from '../../../styles/fonts';

const OTPValidationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {email} = route.params as {email: string};

  const [otpInputs, setOtpInputs] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const inputRefs = useRef<Array<TextInput | null>>([]);

  const colors = useThemeColors();
  const {isDark} = useTheme();

  const focusNext = (index: number, value: string) => {
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
    let newOtpInputs = [...otpInputs];
    newOtpInputs[index] = value;
    setOtpInputs(newOtpInputs);
  };

  const focusPrevious = (index: number) => {
    if (index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleValidateOTP = async () => {
    const reset_code = otpInputs.join('');
    if (reset_code.length !== 6) {
      Alert.alert('Error', 'Please enter complete OTP');
      return;
    }

    try {
      setIsLoading(true);
      const response = await validateOTP(email, reset_code);

      if (response.success) {
        Alert.alert('Success', response.data.message, [
          {
            text: 'OK',
            onPress: () =>
              navigation.navigate('ResetPassword', {
                email,
                reset_code,
              }),
          },
        ]);
      }
    } catch (error) {
      Alert.alert('Error', 'Invalid reset code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    contentContainer: {
      flex: 1,
      padding: 24,
      justifyContent: 'center',
    },
    formContainer: {
      width: '100%',
      maxWidth: 400,
      alignSelf: 'center',
    },
    titleContainer: {
      alignItems: 'center',
      marginBottom: 32,
    },
    title: {
      fontSize: 28,
      fontFamily: Fonts.OnestBold,
      color: colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    otpContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 32,
    },
    otpInput: {
      width: 45,
      height: 55,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 12,
      backgroundColor: colors.card,
      textAlign: 'center',
      fontSize: 18,
      fontFamily: Fonts.OnestSemiBold,
      color: colors.text,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: isDark ? 0.3 : 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    otpInputFocused: {
      borderColor: colors.primary,
      borderWidth: 2,
    },
    button: {
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    primaryButton: {
      backgroundColor: colors.primary,
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontFamily: Fonts.OnestSemiBold,
    },
    resendContainer: {
      alignItems: 'center',
    },
    resendText: {
      fontSize: 14,
      fontFamily: Fonts.OnestMedium,
      color: colors.textSecondary,
    },
    resendLink: {
      color: colors.primary,
      fontFamily: Fonts.OnestSemiBold,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        backgroundColor={colors.surface}
        barStyle={isDark ? 'light-content' : 'dark-content'}
      />

      <View style={styles.header}>
        <Header NavName="OTP Verification" />
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.formContainer}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>OTP Verification</Text>
            <Text style={styles.subtitle}>
              Please enter the 6-digit code sent to{'\n'}
              <Text
                style={{
                  color: colors.primary,
                  fontFamily: Fonts.OnestSemiBold,
                }}>
                {email}
              </Text>
            </Text>
          </View>

          <View style={styles.otpContainer}>
            {[0, 1, 2, 3, 4, 5].map(index => (
              <TextInput
                key={index}
                ref={ref => (inputRefs.current[index] = ref)}
                style={[
                  styles.otpInput,
                  otpInputs[index] && styles.otpInputFocused,
                ]}
                maxLength={1}
                keyboardType="numeric"
                value={otpInputs[index]}
                onChangeText={value => focusNext(index, value)}
                onKeyPress={({nativeEvent}) => {
                  if (nativeEvent.key === 'Backspace' && !otpInputs[index]) {
                    focusPrevious(index);
                  }
                }}
              />
            ))}
          </View>

          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleValidateOTP}
            disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.buttonText}>Verify Code</Text>
            )}
          </TouchableOpacity>

          <View style={styles.resendContainer}>
            <TouchableOpacity>
              <Text style={styles.resendText}>
                Didn't receive code?{' '}
                <Text style={styles.resendLink}>Resend</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OTPValidationScreen;
