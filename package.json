{"name": "NurP<PERSON>z", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "axios": "^1.7.7", "base64-js": "^1.5.1", "country-state-city": "^3.2.1", "formik": "^2.4.6", "lottie-react-native": "^7.2.1", "react": "18.3.1", "react-native": "0.76.1", "react-native-document-picker": "^9.3.1", "react-native-draggable-flatlist": "^4.0.1", "react-native-dropdown-picker": "^5.4.6", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.21.2", "react-native-html-to-pdf": "^0.12.0", "react-native-image-picker": "^8.2.0", "react-native-permissions": "^5.2.1", "react-native-reanimated": "^3.16.6", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^3.35.0", "react-native-share": "^12.0.3", "react-native-swipe-list-view": "^3.2.9", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.3", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "@types/react": "^18.2.6", "@types/react-native-html-to-pdf": "^0.8.3", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}