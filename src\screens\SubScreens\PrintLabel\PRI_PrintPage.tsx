import { View, Text, Alert } from 'react-native'
import React, { useEffect, useState } from 'react'
import { RouteProp } from '@react-navigation/native';
import AppButton from '../../../components/Inventory/AppButton';
import Header from '../../../components/Inventory/Header';
import { GetAllItems, getFormateDate, GetItemsParamsNoFilterNoReturn } from '../../../utils/PublicHelper';
import { getInventoryPort } from '../../../server/InstanceTypes';
import { Inventory, Printers } from '../../../server/types';
import { sendPrintLabelBulkPricing, sendPrintLabelFive, sendPrintLabelFour, sendPrintLabelMixAndMatch, sendPrintLabelOne, sendPrintLabelRound, sendPrintLabelSalePricing, sendPrintLabelSix, sendPrintLabelThree, sendPrintLabelThreeTest, sendPrintLabelTwo } from '../../../utils/PrinterHelper';
import AppDropDown from '../../../components/Inventory/AppDropDown';
import { Backround } from '../../../constants/Color';

type CountItemRouteProp = RouteProp<any, 'PrintPage'>;

const PRI_PrintPage: React.FC<{route: CountItemRouteProp; navigation: any}> = ({
  route,
  navigation,
}) => {
    const [printItems, setPrintItems] = useState<{itemNum: string; value: string}[]>(route.params?.PRINT_LABELS || [])
    const [printers, setPrinters] = useState<Printers[]>([])
    const [selectedPrinters, setSelectedPrinters] = useState<string>("")
    const [selectedTemplate, setSelectedTemplate] = useState<string>("")
    const [loading, setLoading] = useState<boolean>(false)
    
    useEffect(() => {
      getPrinters()
    }, [])
    
    const getPrinters = async () => {
      GetAllItems<Printers[]>(
            (await getInventoryPort()).toString(),
            '/GetAllprinters',
            setPrinters,
            setLoading,
          );
    }
    const printOptions = printers.map(dept => ({
      label: dept.name,
      value: dept.deviceId,
    }));
    const templateOptions = [
      {label: 'Shelf Label 2.25"x 1.25"', value: 'Label1'},
      {label: 'Shelf Label 2"x 1"', value: 'Label2'},
      {label: 'Shelf Label 2.36"x 1.18"', value: 'Label3'},
      {label: 'Shelf Label 2.25"x 1"', value: 'Label4'},
      {label: 'Product Label 1"x 1"', value: 'Label5'},
      {label: 'Product Label 1.5"x 0.5"', value: 'Label6'},
      {label: 'Promotional Bulk Pricing Label 2.362"x 3.937"', value: 'Label7'},
      {label: 'Promotional Sale Pricing Label 2.362"x 3.937"', value: 'Label8'},
      {label: 'Promotional Mix N Match Label 2.362"x 3.937"', value: 'Label9'},
      {label: 'Deli Circle Label 2.75"', value: 'Label10'},
    ];
    const onPrintLabels = async () => {
      if (selectedPrinters === '' || selectedTemplate === '') {
        Alert.alert('Please select a printer and a label template.');
        return;
      }
        printItems.map(async(print) => {
          const labelDetails = await GetItemsParamsNoFilterNoReturn(
                  (await getInventoryPort()).toString(),
                  '/inventory/:ItemNum',
                  {ItemNum: print.itemNum},
                );

                for (let i = 0; i < Number(print.value); i++) {
                  console.log(labelDetails[0]);
                  
                  handleCondition(selectedTemplate, labelDetails[0])

                } 
        })
    };
    
    const handleCondition = (condition: string, SendLabelDetails: Inventory) => {
      switch (condition) {
        case 'Label1':
          sendPrintLabelOne(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label2':
          sendPrintLabelTwo(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label3':
          sendPrintLabelThree(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label4':
          sendPrintLabelFour(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label5':
          sendPrintLabelFive(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label6':
          sendPrintLabelSix(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label7':
          sendPrintLabelBulkPricing(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label8':
          sendPrintLabelSalePricing(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label9':
          sendPrintLabelMixAndMatch(SendLabelDetails, selectedPrinters)
          break;
    
        case 'Label10':
          sendPrintLabelRound(SendLabelDetails, selectedPrinters)
          break;
    
        default:
          console.log('Unknown condition.');
      }
    };
    

    
  return (
    <View style={{flex: 1, backgroundColor: Backround, paddingHorizontal: '2.5%'}}>
        <Header NavName='Print Labels'/>
        <AppDropDown
            label="Printers"
            options={printOptions}
            selectedValue={selectedPrinters}
            onSelect={value => setSelectedPrinters(value)}
            isRequired={true}
          />
          <AppDropDown
            label="Select Label"
            options={templateOptions}
            selectedValue={selectedTemplate}
            onSelect={value => setSelectedTemplate(value)}
            isRequired={true}
          />
      <AppButton Title='Print Labels' OnPress={() => onPrintLabels()}/>
    </View>
  )
}

export default PRI_PrintPage